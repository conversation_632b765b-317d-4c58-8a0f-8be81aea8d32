{"_id": {"$oid": "67a1428ed8c7c257b84b4096"}, "title": "Beginner program (Introduction to ChatGPT)", "description": "Master GitHub Copilot to enhance your coding productivity. Learn how to effectively use AI pair programming to write better code faster. werwer", "chapters": [{"title": "Getting Started with GitHub Copilot", "description": "Introduction to GitHub Copilot and setting up your development environment", "topics": [{"title": "What is GitHub Copilot?", "description": "Understanding AI pair programming and how GitHub Copilot works", "duration": 221, "contentBlocks": [{"type": "video", "title": "tete", "video_duration": 20, "description": "tete", "textContent": {"content": "", "formatting": "markdown"}, "videoContent": {"type": "hls", "hlsData": {"streamingUrls": [{"paths": ["https://d25yi7ujgmvrf2.cloudfront.net/Video_Intro_02_EN_Expert+journey_vf_17May+(1)/Video_Intro_02_EN_Expert+journey_vf_17May+(1)_360.m3u8"], "_id": {"$oid": "67aafb27405bfd8781b0905b"}}]}, "thumbnail": "https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/Big.Buck.Bunny.-.Opening.Screen.png/1200px-Big.Buck.Bunny.-.Opening.Screen.png"}, "order": 0, "_id": "67aafb27405bfd8781b0905a"}], "order": 1, "_id": {"$oid": "67a214bcbeebfcb72df13bf6"}}, {"title": "Installation and Setup", "description": "Setting up GitHub Copilot in VS Code and other supported IDEs", "duration": 15, "contentBlocks": [{"quiz": {"questions": [{"id": "q1", "type": "multiple-choice", "question": "In which IDEs can you use GitHub Copilot?", "options": [{"option": "Only Visual Studio Code", "correct": false}, {"option": "Visual Studio Code, Visual Studio, JetBrains IDEs and Neovim", "correct": true}, {"option": "Only Visual Studio", "correct": false}, {"option": "Only JetBrains IDEs", "correct": false}]}, {"id": "q2", "type": "true-false", "question": "You need a GitHub account to use GitHub Copilot.", "correctAnswer": true}, {"id": "q3", "type": "multiple-selection", "question": "Which requirements are correct for GitHub Copilot setup?", "options": [{"option": "Active GitHub account", "correct": true}, {"option": "GitHub Copilot subscription", "correct": true}, {"option": "Windows 11 operating system", "correct": false}, {"option": "Supported IDE", "correct": true}]}, {"id": "q4", "type": "fill-in-the-blank", "question": "To enable GitHub Copilot in VS Code, you need to install the ________ extension.", "correctAnswer": "GitHub Copilot"}, {"id": "q5", "type": "matching", "question": "Match the following IDEs with their Copilot installation methods.", "pairs": [{"left": "VS Code", "right": "Extensions Marketplace"}, {"left": "IntelliJ IDEA", "right": "Plugins Marketplace"}, {"left": "<PERSON><PERSON><PERSON>", "right": "Github Copilot.vim"}]}]}}], "order": 2, "_id": {"$oid": "67a214bcbeebfcb72df13bf8"}}, {"title": "Getting Started with GitHub Copilot (Video Guide)", "description": "A comprehensive video guide on how to start using GitHub Copilot", "duration": 8, "contentBlocks": [{"type": "video", "title": "GitHub Copilot Tutorial", "description": "Learn how to use GitHub Copilot effectively", "textContent": {"content": "In this video, you'll learn the basics of GitHub Copilot and how to get started with it in your development environment.", "formatting": "markdown"}, "videoContent": {"type": "youtube", "youtubeUrl": "https://www.youtube.com/watch?v=qcjC__FyWjw", "thumbnail": "https://img.youtube.com/vi/Dkt8zIeB-Yg/maxresdefault.jpg"}, "video-duration": 20, "order": 0, "_id": {"$oid": "67aafb27405bfd8781b0905c"}}], "order": 3, "_id": {"$oid": "67a214bcbeebfcb72df13bf9"}}, {"title": "Practice with AI Text Use Cases", "description": "Practice your skills with real AI text use cases", "duration": 15, "contentBlocks": [{"usecase_id": "67c00e62a1cd3e732ce9873b", "usecase_type": "text"}], "order": 4, "_id": {"$oid": "67a214bcbeebfcb72df13c02"}}, {"title": "Practice with AI Image Generation", "description": "Create amazing images with AI", "duration": 15, "contentBlocks": [{"usecase_slug": "67c00e62a1cd3e732ce9880c", "usecase_type": "image"}], "order": 5, "_id": {"$oid": "67a214bcbeebfcb72df13c03"}}], "order": 1, "isLocked": true, "_id": {"$oid": "67a214bcbeebfcb72df13bf5"}}], "createdBy": {"$oid": "668e4ef82531bb60dfb9e94f"}, "category": "Programming", "coverImage": "https://github.blog/wp-content/uploads/2022/06/Copilot.jpeg?fit=1200%2C630", "tags": [], "level": "advanced", "prerequisites": ["No prerequisites required"], "objectives": ["Complete the course successfully"], "isPublished": true, "rating": {"average": 0, "count": 0}, "createdAt": {"$date": "2025-02-03T22:26:22.319Z"}, "updatedAt": {"$date": "2025-02-16T18:23:14.137Z"}, "slug": "beginner-program-introduction-to-chatgpt", "duration": "45m", "__v": 2, "certificateAvailable": true, "instructors": [{"name": "<PERSON>", "role": "Senior Software Engineer", "company": "Microsoft", "bio": "Experienced software engineer with expertise in cloud computing and distributed systems.", "_id": {"$oid": "67a31858687712be19576cc9"}}, {"name": "<PERSON>", "role": "Developer Advocate", "company": "GitHub", "bio": "Open source enthusiast and community builder with a passion for developer education.", "_id": {"$oid": "67a31858687712be19576cca"}}], "providerImage": "https://aibsassets.blob.core.windows.net/course-providers/Microsoft_logo_(2012).svg"}