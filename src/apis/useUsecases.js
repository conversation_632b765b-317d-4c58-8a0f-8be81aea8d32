/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';
import { API_URL } from '../config-global';

export const useUsecases = () => {
  const [usecases, setUsecases] = useState(null);
  const [aof, setAof] = useState(null);
  const [managementRoles, setManagementRoles] = useState(null);
  const [functions, setFunctions] = useState(null);
  const [levels, setLevels] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getAOFS = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${CONFIG.site.serverUrl}/onboarding/get-industries${id ? `?_id=${id}` : ''}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setAof(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setAof(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const getManagementRoles = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/onboarding/get-managementRoles`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setManagementRoles(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setManagementRoles(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const getFunctions = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/onboarding/get-functions`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setFunctions(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setFunctions(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const getLevels = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/onboarding/get-levels`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setLevels(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setLevels(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const getUsecases = useCallback(
    async (page = 1, searchTerm = '', apiType = '', function_label = '', limit = 25) => {
      try {
        const params = {
          page,
          limit,
        };

        if (searchTerm) params.search = searchTerm;
        if (apiType) params.api_type = apiType;
        if (function_label) params.function_label = function_label;

        const response = await axios.get(`${API_URL}/usecase`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
          params,
        });

        let result;
        if (response.data && response.data.data) {
          if (response.data.data.useCases && Array.isArray(response.data.data.useCases)) {
            // Ensure each useCase has a unique key
            const uniqueUseCases = response.data.data.useCases.map((useCase, index) => ({
              ...useCase,
              uniqueKey: useCase._id || useCase.id || `usecase-${index}`,
            }));
            
            result = {
              data: {
                ...response.data.data,
                useCases: uniqueUseCases,
              },
            };
          } else if (Array.isArray(response.data.data)) {
            // Ensure each item in the array has a unique key
            const uniqueData = response.data.data.map((item, index) => ({
              ...item,
              uniqueKey: item._id || item.id || `usecase-${index}`,
            }));
            
            result = {
              data: {
                useCases: uniqueData,
                pagination: response.data.pagination || {
                  page,
                  limit,
                  total: response.data.data.length,
                  hasNextPage: false,
                },
              },
            };
          }
        } else {
          console.warn('No usecases found in response, returning empty array');
          result = {
            data: {
              useCases: [],
              pagination: {
                page,
                limit,
                total: 0,
                hasNextPage: false,
              },
            },
          };
        }

        // Set the usecases state with the fetched data
        setUsecases(result);
        return result;
      } catch (error) {
        console.error('Error fetching usecases:', error);
        throw error;
      }
    },
    [authToken]
  );

  const addUsecase = useCallback(
    async (usecaseData) => {
      setLoading(true);
      try {
        const response = await axios.post(`${CONFIG.site.serverUrl}/usecase/`, usecaseData, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });
        setUsecases(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        const errorMessage = err.response?.data.message;
        setError(errorMessage);
        setUsecases(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const editUsecase = useCallback(
    async (journeyId, journey) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/usecase/edit`,
          {
            journeyId,
            journey,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setUsecases(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setUsecases(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    getAOFS,
    getFunctions,
    getLevels,
    getManagementRoles,
    getUsecases,
    addUsecase,
    editUsecase,
    usecases,
    levels,
    functions,
    managementRoles,
    aof,
    loading,
    error,
  };
};
