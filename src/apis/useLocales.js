/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useLocales = () => {
  const [locales, setLocales] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getLocales = useCallback(
    async (lang) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/locales/getLocales`,
          { lang },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setLocales(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setLocales(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateLocales = useCallback(
    async (lang, key, value) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/locales/editLocales`,
          { lang, key, value },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setLocales(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setLocales(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return { getLocales, updateLocales, locales, loading, error };
};
