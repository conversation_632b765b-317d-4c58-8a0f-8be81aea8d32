/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useIdeations = () => {
  const [ideations, setIdeations] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getIdeations = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/ideation/get`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setIdeations(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setIdeations(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const addIdeation = useCallback(
    async (title, slug) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/ideation/add`,
          { title, slug },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setIdeations(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err.response.data.data);
        setIdeations(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteIdeation = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.delete(`${CONFIG.site.serverUrl}/ideation/delete?_id=${id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });
        setIdeations(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setIdeations(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateIdeation = useCallback(
    async (id, title, slug) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/ideation/edit`,
          { _id: id, title, slug },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setIdeations(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setIdeations(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return { getIdeations, addIdeation, deleteIdeation, updateIdeation, ideations, loading, error };
};
