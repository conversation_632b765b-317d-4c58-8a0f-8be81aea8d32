/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useJourneys = () => {
  const [journeys, setJourneys] = useState(null);
  const [journeyDetails, setJourneyDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getJourneys = useCallback(
    async (selectedFunc, provider, managementRole, level) => {
      if (!selectedFunc) {
        console.error('Invalid selected function');
        return;
      }

      // selectedFunc objesinden function değerini al
      const functionValue = selectedFunc.function || selectedFunc.slug;

      if (!functionValue) {
        console.error('Function value not found in selected function object');
        return;
      }

      setLoading(true);
      try {
        let url = `${CONFIG.site.serverUrl}/journey/get?function=${functionValue}`;

        // Provider parametresi varsa URL'ye ekle
        if (provider) {
          url += `&provider=${provider}`;
        }

        // ManagementRole parametresi varsa URL'ye ekle
        if (managementRole) {
          url += `&managementRole=${managementRole}`;
        }

        // Level parametresi varsa URL'ye ekle
        if (level) {
          url += `&levels=${level}`;
        }

        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        setJourneys(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setJourneys(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const addJourney = useCallback(
    async (
      title_english,
      description,
      provider,
      managementRole,
      journeyType,
      newTab,
      closedCard,
      selectedFunctionSpecUseCases,
      ideation,
      course,
      content,
      buttonType,
      buttonText,
      buttonURL,
      fileUrl,
      selectedAreaOfInterest,
      selectedFunction,
      selectedManagementRole,
      selectedLevel,
      percent,
      translations
    ) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/journey/add`,
          {
            title_english,
            description,
            provider,
            managementRole,
            journeyType,
            newTab,
            closedCard,
            selectedFunctionSpecUseCases,
            ideation,
            course,
            content,
            buttonType,
            buttonText,
            buttonURL,
            fileUrl,
            selectedAreaOfInterest,
            selectedFunction,
            selectedManagementRole,
            selectedLevel,
            percent,
            translations,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJourneys(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        const errorMessage = err.response?.data.message;
        setError(errorMessage);

        setJourneys(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteJourney = useCallback(
    async (journeyId) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/journey/delete/${journeyId}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        console.error('Journey silme hatası:', err);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateJourneyOrder = useCallback(
    async (journey) => {
      setLoading(true);
      try {
        // Journey verilerinde _id ve journeyType özelliklerinin olduğunu kontrol et
        const journeyWithTypes = journey.map((item) => {
          if (!item._id) {
            console.error('Journey item is missing _id:', item);
          }
          // journeyType özelliğinin var olduğundan emin ol
          return {
            _id: item._id,
            order: item.order,
            journeyType: item.journeyType || '',
            title_english: item.title_english, // Geriye dönük uyumluluk için
          };
        });

        console.log('Sending order update with journey types:', journeyWithTypes);

        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/journey/order-journey-update`,
          {
            journey: journeyWithTypes,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJourneys(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        console.error('Journey order update error:', err);
        setError(err);
        setJourneys(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const getJourneyDetails = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${CONFIG.site.serverUrl}/journey/get-journey-details?_id=${id}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJourneyDetails(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setJourneyDetails(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const editJourney = useCallback(
    async (journeyId, journey) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/journey/edit`,
          {
            journeyId,
            journey,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJourneys(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setJourneys(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    getJourneys,
    addJourney,
    deleteJourney,
    updateJourneyOrder,
    getJourneyDetails,
    editJourney,
    journeys,
    journeyDetails,
    loading,
    error,
  };
};
