/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useForms = () => {
  const [allForms, setAllForms] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getForms = useCallback(
    async (page = 1, limit = 1) => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${CONFIG.site.serverUrl}/forms/list?page=${page}&limit=${limit}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        const formattedData = {
          forms: response.data.data.forms,
          total: response.data.data.pagination.total,
          currentPage: response.data.data.pagination.page,
          totalPages: response.data.data.pagination.pages,
        };

        setAllForms(formattedData);
        setError(null);
        return formattedData;
      } catch (err) {
        setError(err);
        setAllForms(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const updateForm = useCallback(
    async (formId, formData) => {
      setLoading(true);
      try {
        const response = await axios.put(
          `${CONFIG.site.serverUrl}/forms/update/${formId}`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );
        return response.data.data;
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const addForm = useCallback(
    async (formData) => {
      setLoading(true);
      try {
        const response = await axios.post(`${CONFIG.site.serverUrl}/forms/create`, formData, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });
        setError(null);
        return { success: true, data: response.data.data };
      } catch (err) {
        const errorMessage =
          err.response?.data?.data || 'An error occurred while creating the form';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Form çevirilerini getir
  const getFormTranslations = useCallback(
    async (formId) => {
      setLoading(true);
      try {
        const response = await axios.get(`${CONFIG.site.serverUrl}/forms/${formId}/translations`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });
        setError(null);
        return { success: true, data: response.data.data };
      } catch (err) {
        const errorMessage =
          err.response?.data?.data || 'An error occurred while getting form translations';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Belirli bir dildeki form çevirisini getir
  const getFormTranslation = useCallback(
    async (formId, language) => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${CONFIG.site.serverUrl}/forms/${formId}/translations/${language}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );
        setError(null);
        return { success: true, data: response.data.data };
      } catch (err) {
        const errorMessage =
          err.response?.data?.data ||
          `An error occurred while getting form translation for ${language}`;
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Form çevirisini güncelle
  const updateFormTranslation = useCallback(
    async (formId, language, translationData) => {
      setLoading(true);
      try {
        // Form çeviri verisini kopyala
        const processedTranslationData = JSON.parse(JSON.stringify(translationData));

        // Boş label alanlarını doldur
        if (processedTranslationData.topics && processedTranslationData.topics.length > 0) {
          processedTranslationData.topics.forEach((topic) => {
            if (topic.fields && topic.fields.length > 0) {
              topic.fields.forEach((field) => {
                // Label boşsa description değerini veya varsayılan bir değer kullan
                if (!field.label || field.label.trim() === '') {
                  field.label = field.description || field.name || 'Field Label';
                }
              });
            }
          });
        }

        // Geriye dönük uyumluluk için fields kontrolü
        if (processedTranslationData.fields && processedTranslationData.fields.length > 0) {
          processedTranslationData.fields.forEach((field) => {
            if (!field.label || field.label.trim() === '') {
              field.label = field.description || field.name || 'Field Label';
            }
          });
        }

        const response = await axios.put(
          `${CONFIG.site.serverUrl}/forms/${formId}/translations/${language}`,
          processedTranslationData,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );
        setError(null);
        return { success: true, data: response.data.data };
      } catch (err) {
        const errorMessage =
          err.response?.data?.data ||
          `An error occurred while updating form translation for ${language}`;
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Form çevirisini sil
  const deleteFormTranslation = useCallback(
    async (formId, language) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/forms/${formId}/translations/${language}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );
        setError(null);
        return { success: true, data: response.data.data };
      } catch (err) {
        const errorMessage =
          err.response?.data?.data ||
          `An error occurred while deleting form translation for ${language}`;
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Form çevirisi oluştur
  const translateForm = useCallback(
    async (formData, toLanguage, fromLanguage = 'en', saveToDatabase = true) => {
      setLoading(true);
      try {
        // Form verisini kopyala
        const processedFormData = JSON.parse(JSON.stringify(formData));

        // Boş label alanlarını doldur
        if (processedFormData.topics && processedFormData.topics.length > 0) {
          processedFormData.topics.forEach((topic) => {
            if (topic.fields && topic.fields.length > 0) {
              topic.fields.forEach((field) => {
                // Label boşsa description değerini veya varsayılan bir değer kullan
                if (!field.label || field.label.trim() === '') {
                  field.label = field.description || field.name || 'Field Label';
                }
              });
            }
          });
        }

        // Geriye dönük uyumluluk için fields kontrolü
        if (processedFormData.fields && processedFormData.fields.length > 0) {
          processedFormData.fields.forEach((field) => {
            if (!field.label || field.label.trim() === '') {
              field.label = field.description || field.name || 'Field Label';
            }
          });
        }

        // Çeviri içindeki label'ları da kontrol et
        if (processedFormData.translations) {
          Object.keys(processedFormData.translations).forEach((langKey) => {
            const translation = processedFormData.translations[langKey];

            // Translation içindeki topics kontrolü
            if (translation.topics && translation.topics.length > 0) {
              translation.topics.forEach((topic) => {
                if (topic.fields && topic.fields.length > 0) {
                  topic.fields.forEach((field) => {
                    if (!field.label || field.label.trim() === '') {
                      field.label = field.description || field.name || 'Field Label';
                    }
                  });
                }
              });
            }

            // Translation içindeki fields kontrolü
            if (translation.fields && translation.fields.length > 0) {
              translation.fields.forEach((field) => {
                if (!field.label || field.label.trim() === '') {
                  field.label = field.description || field.name || 'Field Label';
                }
              });
            }
          });
        }

        const response = await axios.post(
          `${CONFIG.site.serverUrl}/translator/forms`,
          {
            form: processedFormData,
            to: toLanguage,
            from: fromLanguage,
            saveToDatabase: saveToDatabase,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        // Yanıt verisinde de boş etiketleri kontrol et
        if (response.data && response.data.data && response.data.data.translation) {
          const result = response.data.data;

          // Eğer yanıt bir çeviri içeriyorsa ve boş etiketler varsa düzelt
          if (result.translation) {
            const fixTranslationLabels = (translation) => {
              if (translation.topics && translation.topics.length > 0) {
                translation.topics.forEach((topic) => {
                  if (topic.fields && topic.fields.length > 0) {
                    topic.fields.forEach((field) => {
                      if (!field.label || field.label.trim() === '') {
                        field.label = field.description || field.name || 'Field Label';
                      }
                    });
                  }
                });
              }

              if (translation.fields && translation.fields.length > 0) {
                translation.fields.forEach((field) => {
                  if (!field.label || field.label.trim() === '') {
                    field.label = field.description || field.name || 'Field Label';
                  }
                });
              }

              return translation;
            };

            result.translation = fixTranslationLabels(result.translation);
          }

          // Tüm translations nesnesini kontrol et
          if (result.translations) {
            Object.keys(result.translations).forEach((langKey) => {
              if (result.translations[langKey]) {
                result.translations[langKey] = fixTranslationLabels(result.translations[langKey]);
              }
            });
          }
        }

        setError(null);
        return { success: true, data: response.data.data };
      } catch (err) {
        const errorMessage =
          err.response?.data?.data || `An error occurred while translating form to ${toLanguage}`;
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    getForms,
    updateForm,
    addForm,
    allForms,
    loading,
    error,
    getFormTranslations,
    getFormTranslation,
    updateFormTranslation,
    deleteFormTranslation,
    translateForm,
  };
};
