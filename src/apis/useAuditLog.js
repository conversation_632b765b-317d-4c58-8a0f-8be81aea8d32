/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useAuditLog = () => {
  const [allAuditLogs, setAllAuditLogs] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getAuditLogs = useCallback(
    async (containerName) => {
      setLoading(true);
      try {
        const response = await axios.get(`${CONFIG.site.serverUrl}/auditlog/get`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        setAllAuditLogs(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setAllAuditLogs(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    getAuditLogs,
    allAuditLogs,
    loading,
    error,
  };
};
