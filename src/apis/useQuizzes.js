/* eslint-disable consistent-return */
import axios from 'axios';
import { shuffle } from 'lodash';
import { useCallback, useState } from 'react';
import { CONFIG } from 'src/config-global';

export const useQuizzes = () => {
  const [quizzes, setQuizzes] = useState(null);
  const [quiz, setQuiz] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const formatQuizResponse = (quiz) => {
    if (!quiz) return null;

    // Soruları istenen formata dönüştür
    const formattedQuestions = quiz.questions.map((question, index) => {
      // Temel soru yapısını oluştur
      const formattedQuestion = {
        id: `q${index + 1}`,
        type: question.type,
        question: question.question,
      };

      // Soru tipine göre farklı format uygula
      switch (question.type) {
        case 'multiple-choice':
        case 'single-choice':
        case 'multiple-selection':
          // Eğer options dizisi varsa ve array ise
          if (Array.isArray(question.options)) {
            formattedQuestion.options = question.options.map((opt) => ({
              option: opt.text || opt.option || '',
              correct: opt.isCorrect || opt.correct || false,
            }));
          } else {
            // Options dizisi yoksa veya array değilse boş bir dizi oluştur
            formattedQuestion.options = [];
            console.warn(`Soru ID: ${question._id} için options dizisi bulunamadı veya geçersiz.`);
          }
          break;

        case 'true-false':
          // True-false için correctAnswer alanı ekle
          // Eğer options dizisi varsa ve array ise
          if (Array.isArray(question.options) && question.options.length > 0) {
            const trueOption = question.options.find(
              (opt) =>
                (opt.text === 'True' || opt.option === 'True') && (opt.isCorrect || opt.correct)
            );
            formattedQuestion.correctAnswer = !!trueOption;
          } else {
            formattedQuestion.correctAnswer = false;
            console.warn(`Soru ID: ${question._id} için options dizisi bulunamadı veya geçersiz.`);
          }
          break;

        case 'fill-in-the-blank':
          // Fill-in-the-blank için correctAnswer alanı ekle
          // Eğer options dizisi varsa ve array ise
          if (Array.isArray(question.options) && question.options.length > 0) {
            const correctOption = question.options.find((opt) => opt.isCorrect || opt.correct);
            formattedQuestion.correctAnswer = correctOption
              ? correctOption.text || correctOption.option || ''
              : '';
          } else if (question.correctAnswer) {
            // Eğer doğrudan correctAnswer alanı varsa onu kullan
            formattedQuestion.correctAnswer = question.correctAnswer;
          } else {
            formattedQuestion.correctAnswer = '';
            console.warn(`Soru ID: ${question._id} için doğru cevap bulunamadı.`);
          }
          break;

        case 'matching':
          // Matching için pairs dizisi oluştur
          // Öncelikle question.pairs dizisini kontrol et - çevirilerde önemli
          if (Array.isArray(question.pairs) && question.pairs.length > 0) {
            // Pairs dizisi doğrudan kullanılabilir
            formattedQuestion.pairs = question.pairs;
          }
          // Eğer options dizisi varsa ve array ise - ikincil tercih
          else if (Array.isArray(question.options) && question.options.length > 0) {
            formattedQuestion.pairs = question.options.map((opt) => ({
              left: opt.text || opt.left || '',
              right: opt.match || opt.displayMatch || opt.right || '',
            }));
          }
          // Son çare olarak matchPairs'e bak
          else if (Array.isArray(question.matchPairs) && question.matchPairs.length > 0) {
            formattedQuestion.pairs = question.matchPairs;
          } else {
            // Hiçbir kaynak bulunamadı
            formattedQuestion.pairs = [];
            console.warn(`Soru ID: ${question._id || index} için eşleştirme çiftleri bulunamadı.`);
          }

          // Pairs verisinden options dizisi de oluşturalım
          if (formattedQuestion.pairs && formattedQuestion.pairs.length > 0) {
            if (
              !Array.isArray(formattedQuestion.options) ||
              formattedQuestion.options.length === 0
            ) {
              formattedQuestion.options = formattedQuestion.pairs.map((pair) => ({
                text: pair.left,
                match: pair.right,
                displayMatch: pair.right,
                isCorrect: true,
              }));
            }
          }
          break;

        default:
          // Diğer soru tipleri için options dizisini olduğu gibi kullan
          formattedQuestion.options = Array.isArray(question.options)
            ? question.options.map((opt) => ({
                option: opt.text || opt.option || '',
                correct: opt.isCorrect || opt.correct || false,
              }))
            : [];
      }

      return formattedQuestion;
    });

    // İstenen formatta quiz nesnesini döndür
    return {
      ...quiz,
      quiz: {
        questions: formattedQuestions,
      },
    };
  };

  const getQuizzes = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/quizzes`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      const quizzes = response.data.data.quizzes;

      // Quizleri işle ve formatla
      const processedQuizzes = quizzes.map((quiz) => {
        // Temel quiz yapısını oluştur
        const processedQuiz = { ...quiz };

        // Questions dizileri için destek ekle
        let quizQuestions = [];

        // Ana questions dizisi varsa onu al
        if (Array.isArray(quiz.questions)) {
          quizQuestions = quiz.questions;
        }
        // Yoksa quiz.quiz içinde olabilir
        else if (quiz.quiz && Array.isArray(quiz.quiz.questions)) {
          quizQuestions = quiz.quiz.questions;
        }

        // Quiz nesnesinin var olduğundan emin ol
        if (!processedQuiz.quiz) {
          processedQuiz.quiz = {};
        }

        // Questions dizisinin doğru formatta olduğundan emin ol
        processedQuiz.quiz.questions = quizQuestions.map((question) => {
          // Temel soru yapısını oluştur
          const formattedQuestion = {
            id: question.id || `q${Math.random().toString(36).substring(2, 9)}`,
            type: question.type || 'multiple-choice',
            question: question.question || '',
          };

          // Soru tipine göre gereken alanları ekle
          if (['multiple-choice', 'single-choice', 'multiple-selection'].includes(question.type)) {
            if (!Array.isArray(question.options) || question.options.length === 0) {
              formattedQuestion.options = [];
            } else {
              formattedQuestion.options = question.options.map((opt) => ({
                option: opt.option || opt.text || '',
                correct: opt.correct || opt.isCorrect || false,
                explanation: opt.explanation || '',
              }));
            }
          } else if (question.type === 'true-false') {
            formattedQuestion.correctAnswer = question.correctAnswer || false;
          } else if (question.type === 'fill-in-the-blank') {
            formattedQuestion.correctAnswer = question.correctAnswer || '';
          } else if (question.type === 'matching') {
            formattedQuestion.pairs = Array.isArray(question.pairs)
              ? question.pairs.map((pair) => ({
                  left: pair.left || '',
                  right: pair.right || '',
                }))
              : [];
          }

          return formattedQuestion;
        });

        return processedQuiz;
      });

      setQuizzes(processedQuizzes);
      setError(null);
      return processedQuizzes;
    } catch (err) {
      console.error('Quiz getirme hatası:', err);
      setError(err);
      setQuizzes(null);
      return [];
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const getQuizzesByCategory = useCallback(
    async (category) => {
      setLoading(true);
      try {
        const response = await axios.get(`${CONFIG.site.serverUrl}/quizzes/category/${category}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        // API artık formatlanmış veri döndürdüğü için doğrudan kullan
        const quizzes = response.data.data.quizzes;

        setQuizzes(quizzes);
        setError(null);
        return quizzes;
      } catch (err) {
        console.error('Kategori bazlı quiz getirme hatası:', err);
        setError(err);
        setQuizzes(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const getQuizById = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.get(`${CONFIG.site.serverUrl}/quizzes/${id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        // API'den gelen veriyi formatla
        const rawQuiz = response.data.data.quiz;

        const formattedQuiz = formatQuizResponse(rawQuiz);

        setQuiz(formattedQuiz);
        setError(null);
        return formattedQuiz;
      } catch (err) {
        console.error('Quiz detayı getirme hatası:', err);
        setError(err);
        setQuiz(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const createQuiz = useCallback(
    async (quizData) => {
      setLoading(true);
      try {
        // URL'den dil parametresini kontrol et
        const urlParams = new URLSearchParams(window.location.search);
        const lang = urlParams.get('lang') || 'en'; // Varsayılan dil İngilizce

        // Farklı formattaki quiz verilerini API'nin beklediği formata dönüştür
        let formattedData = { ...quizData };

        // Eğer alternatif format kullanılıyorsa (örnek yapıdaki gibi)
        if (
          quizData.questions &&
          quizData.questions.some((q) => q.id || q.pairs || q.correctAnswer)
        ) {
          formattedData.questions = quizData.questions.map((question) => {
            // Temel soru yapısı
            const formattedQuestion = {
              question: question.question,
              type: question.type,
              points: question.points || 10,
            };

            // Soru tipine göre options yapısını oluştur
            switch (question.type) {
              case 'multiple-choice':
              case 'multiple-selection':
                formattedQuestion.options = question.options.map((opt) => {
                  // Kullanıcının gönderdiği gerçek değerleri kullan
                  const text = opt.text || opt.option || '';
                  const isCorrect =
                    opt.isCorrect !== undefined
                      ? opt.isCorrect
                      : opt.correct !== undefined
                        ? opt.correct
                        : false;

                  return {
                    text: text,
                    isCorrect: isCorrect,
                    explanation: opt.explanation || '',
                  };
                });
                break;

              case 'true-false':
                formattedQuestion.options = [
                  { text: 'True', isCorrect: question.correctAnswer === true, explanation: '' },
                  { text: 'False', isCorrect: question.correctAnswer === false, explanation: '' },
                ];
                break;

              case 'fill-in-the-blank':
                formattedQuestion.options = [
                  { text: question.correctAnswer, isCorrect: true, explanation: '' },
                ];
                break;

              case 'matching':
                formattedQuestion.options = question.pairs.map((pair) => ({
                  text: pair.left,
                  match: pair.right,
                  isCorrect: true,
                  explanation: '',
                }));
                break;

              default:
                formattedQuestion.options = question.options || [];
            }

            return formattedQuestion;
          });
        }

        // Tüm verileri translations altına yerleştir
        const dataToSend = {
          translations: {
            [lang]: formattedData,
          },
          // Temel meta veriler (başlık ve kategori gibi) yine de ana veriye eklenebilir
          title: formattedData.title,
          category: formattedData.category,
        };

        const response = await axios.post(`${CONFIG.site.serverUrl}/quizzes`, dataToSend, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        setQuiz(response.data.data.quiz);
        setError(null);
        return response.data.data.quiz;
      } catch (err) {
        console.error('Error creating quiz:', err);
        setError(err);
        setQuiz(null);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const updateQuiz = useCallback(
    async (id, quizData) => {
      setLoading(true);
      try {
        // URL'den dil parametresini kontrol et
        const urlParams = new URLSearchParams(window.location.search);
        const lang = urlParams.get('lang') || 'en'; // Varsayılan dil İngilizce

        // Farklı formattaki quiz verilerini API'nin beklediği formata dönüştür
        let formattedData = { ...quizData };
        // Eğer alternatif format kullanılıyorsa (örnek yapıdaki gibi)
        if (
          quizData.questions &&
          quizData.questions.some((q) => q.id || q.pairs || q.correctAnswer)
        ) {
          formattedData.questions = quizData.questions.map((question) => {
            // Temel soru yapısı
            const formattedQuestion = {
              question: question.question,
              type: question.type,
              points: question.points || 10,
            };

            // Soru tipine göre options yapısını oluştur
            switch (question.type) {
              case 'multiple-choice':
              case 'multiple-selection':
                formattedQuestion.options = question.options.map((opt) => {
                  // Kullanıcının gönderdiği gerçek değerleri kullan
                  const text = opt.text || opt.option || '';
                  const isCorrect =
                    opt.isCorrect !== undefined
                      ? opt.isCorrect
                      : opt.correct !== undefined
                        ? opt.correct
                        : false;

                  return {
                    text: text,
                    isCorrect: isCorrect,
                    explanation: opt.explanation || '',
                  };
                });
                break;

              case 'true-false':
                formattedQuestion.options = [
                  { text: 'True', isCorrect: question.correctAnswer === true, explanation: '' },
                  { text: 'False', isCorrect: question.correctAnswer === false, explanation: '' },
                ];
                break;

              case 'fill-in-the-blank':
                formattedQuestion.options = [
                  { text: question.correctAnswer, isCorrect: true, explanation: '' },
                ];
                break;

              case 'matching':
                formattedQuestion.options = question.pairs.map((pair) => ({
                  text: pair.left,
                  match: pair.right,
                  isCorrect: true,
                  explanation: '',
                }));
                break;

              default:
                formattedQuestion.options = question.options || [];
            }

            return formattedQuestion;
          });
        }

        // Mevcut quiz verilerini al ve diğer çevirileri koru
        const currentQuiz = await axios.get(`${CONFIG.site.serverUrl}/quizzes/${id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        const existingTranslations = currentQuiz.data.data.quiz.translations || {};

        // İç içe translations'ı önlemek için temizle
        const cleanedData = { ...formattedData };
        delete cleanedData.translations;

        // Veriyi translations altında güncelle
        const dataToSend = {
          translations: {
            ...existingTranslations,
            [lang]: cleanedData,
          },
          category: formattedData.category,
          title: formattedData.title,
          passingScore: formattedData.passingScore,
          timeLimit: formattedData.timeLimit,
          status: formattedData.status,
          visibility: formattedData.visibility,
          maxAttempts: formattedData.maxAttempts,
          showAnswer: formattedData.showAnswer,
          shuffleQuestions: formattedData.shuffleQuestions,
          active: formattedData.active,
          language: formattedData.language,
          updatedAt: new Date().toISOString(),
        };

        const response = await axios.patch(`${CONFIG.site.serverUrl}/quizzes/${id}`, dataToSend, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        setQuiz(response.data.data.quiz);
        setError(null);
        return response.data.data.quiz;
      } catch (err) {
        console.error('Error updating quiz:', err);
        setError(err.response?.data || err);
        setQuiz(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const deleteQuiz = useCallback(
    async (id) => {
      setLoading(true);
      try {
        await axios.delete(`${CONFIG.site.serverUrl}/quizzes/${id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        // Başarılı silme işleminden sonra quizzes listesini güncelle
        const updatedQuizzes = quizzes ? quizzes.filter((q) => q._id !== id) : null;
        setQuizzes(updatedQuizzes);
        setError(null);
        return true;
      } catch (err) {
        setError(err);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [authToken, quizzes]
  );

  // Quiz çevirisi için yeni fonksiyon
  const translateQuiz = useCallback(
    async (quizData, targetLanguage, sourceLanguage = 'en', saveToDatabase = true) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/translator/quiz`,
          {
            quiz: quizData,
            to: targetLanguage,
            from: sourceLanguage,
            saveToDatabase,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setError(null);
        return {
          success: true,
          data: response.data.data.translatedQuiz.translatedContent,
        };
      } catch (err) {
        console.error('Quiz çevirme hatası:', err);
        setError(err);
        return {
          success: false,
          error: err.response?.data?.message || err.message,
        };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Belirli bir çeviriyi almak için fonksiyon
  const getQuizTranslation = useCallback(
    async (quizId, language) => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${CONFIG.site.serverUrl}/quizzes/${quizId}/translations/${language}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setError(null);
        return {
          success: true,
          data: response.data.data.translation,
        };
      } catch (err) {
        console.error('Quiz çevirisi alma hatası:', err);
        setError(err);
        return {
          success: false,
          error: err.response?.data?.message || err.message,
        };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Çeviriyi güncellemek için fonksiyon
  const updateQuizTranslation = useCallback(
    async (quizId, language, translationData) => {
      setLoading(true);
      try {
        const response = await axios.put(
          `${CONFIG.site.serverUrl}/quizzes/${quizId}/translations/${language}`,
          {
            translation: translationData,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setError(null);
        return {
          success: true,
          data: response.data.data,
        };
      } catch (err) {
        console.error('Quiz çevirisi güncelleme hatası:', err);
        setError(err);
        return {
          success: false,
          error: err.response?.data?.message || err.message,
        };
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    getQuizzes,
    getQuizzesByCategory,
    getQuizById,
    createQuiz,
    updateQuiz,
    deleteQuiz,
    translateQuiz,
    getQuizTranslation,
    updateQuizTranslation,
    quizzes,
    quiz,
    loading,
    error,
  };
};
