/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useCourses = () => {
  const [courses, setCourses] = useState(null);
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getCourses = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/courses`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setCourses(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setCourses(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const getCourseById = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.get(`${CONFIG.site.serverUrl}/courses/${id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        setCourse(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setCourse(null);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const addCourse = useCallback(
    async (courseData) => {
      setLoading(true);
      try {
        // Create slug from title if not provided
        if (!courseData.slug) {
          courseData.slug = courseData.title
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-'); // Replace multiple - with single -
        }

        // Add createdAt and updatedAt if not provided
        if (!courseData.createdAt) {
          courseData.createdAt = new Date().toISOString();
        }
        if (!courseData.updatedAt) {
          courseData.updatedAt = new Date().toISOString();
        }

        // Set default values for rating if not provided
        if (!courseData.rating) {
          courseData.rating = {
            average: 0,
            count: 0,
          };
        }

        // Set isPublished if not provided
        if (courseData.isPublished === undefined) {
          courseData.isPublished = false;
        }

        const response = await axios.post(`${CONFIG.site.serverUrl}/courses`, courseData, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        // Update local state
        if (response.data.success) {
          setCourses((prev) => (prev ? [...prev, response.data.data] : [response.data.data]));
        }

        setError(null);
        return response.data.data;
      } catch (err) {
        console.error('Error adding course:', err);
        setError(err.response?.data || err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const deleteCourse = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.delete(`${CONFIG.site.serverUrl}/courses/${id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        // Başarılı silme işleminden sonra yerel state'i güncelle
        setCourses((prevCourses) => prevCourses.filter((course) => course._id !== id));

        setError(null);
        return response.data.data;
      } catch (err) {
        console.error('Error deleting course:', err);
        setError(err);
        throw err; // Hatayı yukarı ilet ki UI'da gösterilebilsin
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const updateCourse = useCallback(
    async (id, courseData) => {
      setLoading(true);
      try {
        // Update updatedAt
        courseData.updatedAt = new Date().toISOString();

        // Check if we're updating a specific language translation
        const urlParams = new URLSearchParams(window.location.search);
        const lang = urlParams.get('lang');

        let dataToSend = courseData;

        // If we have a language parameter, we only update the translations for that language
        if (lang) {
          // First get the current course data to preserve other translations
          const currentCourse = await axios.get(`${CONFIG.site.serverUrl}/courses/${id}`, {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          });

          // Prepare the translations object
          const existingTranslations = currentCourse.data.data.translations || {};

          // Remove any nested translations field from courseData to prevent nesting
          const cleanedCourseData = { ...courseData };
          delete cleanedCourseData.translations;

          // Eğer providerImage main course'dan geliyorsa, temizlenmiş veri içinden kaldır
          if (currentCourse.data.data.providerImage === courseData.providerImage) {
            delete cleanedCourseData.providerImage;
          }
          if (currentCourse.data.data.coverImage === courseData.coverImage) {
            delete cleanedCourseData.coverImage;
          }
          if (currentCourse.data.data.provider === courseData.provider) {
            delete cleanedCourseData.provider;
          }

          // Create or update the translation for the specified language
          dataToSend = {
            translations: {
              ...existingTranslations,
              [lang]: cleanedCourseData, // Use the cleaned courseData as the translation for this language
            },
            updatedAt: courseData.updatedAt,
          };

          // Varsa ana kurs verisindeki providerImage ve coverImage alanlarını koru
          if (courseData.providerImage) {
            dataToSend.providerImage = courseData.providerImage;
          }

          if (courseData.coverImage) {
            dataToSend.coverImage = courseData.coverImage;
          }

          if (courseData.provider) {
            dataToSend.provider = courseData.provider;
          }

          // Don't update the slug when updating translations
        } else {
          // Create slug from title if modified (only for main course data, not translations)
          if (courseData.title) {
            dataToSend.slug = courseData.title
              .toLowerCase()
              .replace(/[^\w\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-');
          }
        }

        const response = await axios.patch(`${CONFIG.site.serverUrl}/courses/${id}`, dataToSend, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        // Update local state
        if (response.data.success) {
          setCourses((prev) =>
            prev ? prev.map((course) => (course._id === id ? response.data.data : course)) : null
          );
        }

        setError(null);
        return response.data.data;
      } catch (err) {
        console.error('Error updating course:', err);
        setError(err.response?.data || err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // New function to translate course content
  const translateCourse = useCallback(
    async (courseId, targetLanguage, sourceLanguage = 'en') => {
      setLoading(true);
      try {
        // Önce kurs verisini al
        let courseData = course;
        if (!courseData || courseData._id !== courseId) {
          const response = await axios.get(`${CONFIG.site.serverUrl}/courses/${courseId}`, {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          });
          courseData = response.data.data;
        }

        // Kaynak dil verisini al
        const sourceData = courseData.translations[sourceLanguage];
        if (!sourceData) {
          throw new Error(`Source language ${sourceLanguage} not found in course translations`);
        }

        // Çeviri API'sini çağır
        const translationResponse = await axios.post(
          `${CONFIG.site.serverUrl}/translator/course`,
          {
            course: sourceData,
            from: sourceLanguage,
            to: targetLanguage,
            language: targetLanguage,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        if (translationResponse.status === 200) {
          const translatedData = translationResponse.data.data;

          // Çeviriyi kurs yapısına ekle
          const updateData = {
            translations: {
              ...courseData.translations,
              [targetLanguage]: {
                ...translatedData.translation,
                _id: courseData._id,
                createdBy: courseData.createdBy,
                defaultLanguage: courseData.defaultLanguage,
                createdAt: courseData.createdAt,
                updatedAt: new Date().toISOString(),
              },
            },
            updatedAt: new Date().toISOString(),
          };

          // Varsa ana kurs verisindeki providerImage ve coverImage alanlarını koru
          if (courseData.providerImage) {
            updateData.providerImage = courseData.providerImage;
          }

          if (courseData.coverImage) {
            updateData.coverImage = courseData.coverImage;
          }

          if (courseData.provider) {
            updateData.provider = courseData.provider;
          }

          // Kursu güncelle
          const updateResponse = await axios.patch(
            `${CONFIG.site.serverUrl}/courses/${courseId}`,
            updateData,
            {
              headers: {
                Authorization: `Bearer ${authToken}`,
                'Content-Type': 'application/json',
              },
            }
          );

          // Yerel state'i güncelle
          if (updateResponse.data.success) {
            setCourse(updateResponse.data.data);
            setCourses((prev) =>
              prev ? prev.map((c) => (c._id === courseId ? updateResponse.data.data : c)) : null
            );
          }

          setError(null);
          return {
            success: true,
            message: 'Course translated successfully',
            data: updateResponse.data.data,
          };
        }

        throw new Error('Translation failed');
      } catch (err) {
        console.error('Course translation error:', err);
        setError(err.response?.data || err);
        return {
          success: false,
          message: err.response?.data?.message || 'Translation failed',
          error: err,
        };
      } finally {
        setLoading(false);
      }
    },
    [authToken, course]
  );

  return {
    getCourses,
    getCourseById,
    addCourse,
    deleteCourse,
    updateCourse,
    translateCourse,
    courses,
    course,
    loading,
    error,
  };
};
