/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useMedia = () => {
  const [allMedias, setAllMedias] = useState(null);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getMedias = useCallback(
    async (containerName) => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${CONFIG.site.serverUrl}/media/listMedia?containerName=${containerName}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        // HLS container'ı için sadece .m3u8 dosyalarını filtrele
        const filteredData =
          containerName === 'hls'
            ? response.data.data.filter((media) => media.name.endsWith('.m3u8'))
            : response.data.data;

        setAllMedias(filteredData);
        setError(null);
        return filteredData;
      } catch (err) {
        setError(err);
        setAllMedias(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteMedia = useCallback(
    async (containerName, blobName) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/media/deleteMedia?containerName=${containerName}&blobName=${blobName}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const uploadMedia = useCallback(
    async (containerName, file, title, description) => {
      setLoading(true);
      try {
        const formData = new FormData();
        formData.append('media', file);
        formData.append('containerName', containerName);

        const response = await axios.post(`${CONFIG.site.serverUrl}/media/addMedia`, formData, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'multipart/form-data',
          },
        });
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Generate subtitles for a video with detailed timing options
  const generateSubtitles = useCallback(
    async (file, options = {}) => {
      setLoading(true);
      try {
        const {
          containerName = 'transcripts',
          language = 'en-US',
          segmentDuration = 2,
          timeOffset = 0,
          onProgress = null,
        } = options;

        // Create FormData for the request
        const formData = new FormData();
        formData.append('media', file);
        formData.append('containerName', containerName);
        formData.append('language', language);
        formData.append('segmentDuration', segmentDuration);
        formData.append('timeOffset', timeOffset);

        console.log('Subtitle generation başlatılıyor:', {
          containerName,
          language,
          segmentDuration,
          timeOffset,
        });

        // Initial progress update
        if (onProgress) {
          onProgress({
            status: 'uploading',
            percent: 0,
            message: 'Preparing upload...',
          });
        }

        // Send the request and handle response as SSE
        return new Promise((resolve, reject) => {
          // This is a single request approach - we'll send the POST and read the SSE response
          fetch(`${CONFIG.site.serverUrl}/media/generateDetailedSRT`, {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
            body: formData,
          })
            .then((response) => {
              if (!response.ok && response.status !== 200) {
                throw new Error(`HTTP error: ${response.status}`);
              }

              // Set up a reader to process the SSE response chunks
              const reader = response.body.getReader();
              const decoder = new TextDecoder();
              let buffer = '';
              let lastCompletedData = null;

              // Function to read and process chunks
              function readChunk() {
                reader
                  .read()
                  .then(({ value, done }) => {
                    if (done) {
                      console.log('Stream tamamlandı');
                      if (lastCompletedData) {
                        resolve(lastCompletedData);
                      }
                      setLoading(false);
                      return;
                    }

                    // Decode the chunk and add to buffer
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    // Process any complete SSE events in the buffer
                    const lines = buffer.split('\n\n');
                    // Keep the last (possibly incomplete) chunk in the buffer
                    buffer = lines.pop() || '';

                    // Process complete events
                    for (const line of lines) {
                      if (line.startsWith('data: ')) {
                        try {
                          const eventData = JSON.parse(line.substring(6));
                          console.log('SSE Event received:', eventData);

                          // Handle the event data
                          if (onProgress) {
                            const progress = {
                              ...eventData,
                              percent: eventData.percent !== undefined ? eventData.percent : 0,
                              message: eventData.message || 'Processing...',
                              segmentInfo:
                                eventData.currentSegment && eventData.totalSegments
                                  ? `Segment ${eventData.currentSegment}/${eventData.totalSegments}`
                                  : null,
                            };

                            onProgress(progress);
                          }

                          // Check if processing is complete
                          if (eventData.status === 'completed') {
                            console.log('Process completed, processing response:', eventData);

                            // Normalize and clean up response data
                            const normalizedData = {
                              ...eventData,
                              // Ensure URL fields are properly set
                              srtUrl: eventData.srtUrl || eventData.response?.srtUrl || '',
                              srtContent:
                                eventData.srtContent || eventData.response?.srtContent || '',
                              containerName: eventData.containerName || containerName,
                              srtFileName:
                                eventData.srtFileName || eventData.response?.srtFileName || '',
                              // Include complete response data if available
                              ...(eventData.response || {}),
                            };

                            // Validate required fields
                            if (!normalizedData.srtContent) {
                              console.warn('SRT içeriği eksik:', normalizedData);
                            }
                            if (!normalizedData.srtUrl) {
                              console.warn('SRT URL eksik:', normalizedData);
                            }

                            console.log('Processed response:', normalizedData);
                            lastCompletedData = normalizedData;
                          } else if (eventData.status === 'error') {
                            console.error('Process error:', eventData);
                            reject(new Error(eventData.message || 'Subtitle generation failed'));
                            setError(eventData.message || 'Subtitle generation failed');
                            setLoading(false);
                            return;
                          }
                        } catch (err) {
                          console.error('SSE data parsing error:', err, line.substring(6));
                        }
                      }
                    }

                    // Continue reading chunks
                    readChunk();
                  })
                  .catch((err) => {
                    console.error('Chunk reading error:', err);
                    reject(err);
                    setError(err.message);
                    setLoading(false);
                  });
              }

              // Start reading chunks
              readChunk();
            })
            .catch((err) => {
              console.error('Fetch error:', err);
              reject(err);
              setError(err.message);
              setLoading(false);
            });
        });
      } catch (err) {
        console.error('General error:', err);
        setError(err);
        setLoading(false);
        throw err;
      }
    },
    [authToken]
  );

  const updateSrtContent = useCallback(
    async (containerName, blobName, srtContent) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/media/updateSrtContent`,
          {
            containerName,
            blobName,
            srtContent,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const convertToHLS = useCallback(
    async (formData, options = {}) => {
      setLoading(true);
      try {
        const { onProgress = null } = options;

        // Initial progress update
        if (onProgress) {
          onProgress({
            status: 'uploading',
            percent: 0,
            message: 'Preparing upload...',
          });
        }

        // Send the request and handle response as SSE
        return new Promise((resolve, reject) => {
          fetch(`${CONFIG.site.serverUrl}/media/convertToHLS`, {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
            body: formData,
          })
            .then((response) => {
              if (!response.ok && response.status !== 200) {
                throw new Error(`HTTP error: ${response.status}`);
              }

              // Set up a reader to process the SSE response chunks
              const reader = response.body.getReader();
              const decoder = new TextDecoder();
              let buffer = '';
              let lastCompletedData = null;

              // Function to read and process chunks
              function readChunk() {
                reader
                  .read()
                  .then(({ value, done }) => {
                    if (done) {
                      console.log('Stream tamamlandı');
                      if (lastCompletedData) {
                        resolve(lastCompletedData);
                      }
                      setLoading(false);
                      return;
                    }

                    // Decode the chunk and add to buffer
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    // Process any complete SSE events in the buffer
                    const lines = buffer.split('\n\n');
                    // Keep the last (possibly incomplete) chunk in the buffer
                    buffer = lines.pop() || '';

                    // Process complete events
                    for (const line of lines) {
                      if (line.startsWith('data: ')) {
                        try {
                          const eventData = JSON.parse(line.substring(6));
                          console.log('SSE Event received:', eventData);

                          // Handle the event data
                          if (onProgress) {
                            const progress = {
                              ...eventData,
                              percent: eventData.percent !== undefined ? eventData.percent : 0,
                              message: eventData.message || 'Processing...',
                            };

                            onProgress(progress);
                          }

                          // Check if processing is complete
                          if (eventData.status === 'completed') {
                            console.log('Process completed, processing response:', eventData);
                            lastCompletedData = eventData;
                          } else if (eventData.status === 'error') {
                            console.error('Process error:', eventData);
                            reject(new Error(eventData.message || 'HLS conversion failed'));
                            setError(eventData.message || 'HLS conversion failed');
                            setLoading(false);
                            return;
                          }
                        } catch (err) {
                          console.error('SSE data parsing error:', err, line.substring(6));
                        }
                      }
                    }

                    // Continue reading chunks
                    readChunk();
                  })
                  .catch((err) => {
                    console.error('Chunk reading error:', err);
                    reject(err);
                    setError(err.message);
                    setLoading(false);
                  });
              }

              // Start reading chunks
              readChunk();
            })
            .catch((err) => {
              console.error('Fetch error:', err);
              reject(err);
              setError(err.message);
              setLoading(false);
            });
        });
      } catch (err) {
        console.error('General error:', err);
        setError(err);
        setLoading(false);
        throw err;
      }
    },
    [authToken]
  );

  return {
    getMedias,
    deleteMedia,
    uploadMedia,
    generateSubtitles,
    updateSrtContent,
    convertToHLS,
    allMedias,
    loading,
    error,
  };
};
