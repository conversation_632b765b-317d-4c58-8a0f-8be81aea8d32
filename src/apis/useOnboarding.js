/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useOnboarding = () => {
  const [industry, setIndustry] = useState(null);
  const [jobRole, setJobRole] = useState(null);
  const [managementRoles, setManagementRoles] = useState(null);
  const [functions, setFunctions] = useState(null);
  const [levels, setLevels] = useState(null);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  // Industry
  const getIndustry = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${CONFIG.site.serverUrl}/onboarding/get-industries${id ? `?_id=${id}` : ''}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setIndustry(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setIndustry(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const editIndustry = useCallback(
    async (_id, description, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/edit-industry`,
          { _id, description, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setIndustry(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const addIndustry = useCallback(
    async (description, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/onboarding/add-industry`,
          { description, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setIndustry(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setIndustry(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteIndustry = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/onboarding/delete-industry?_id=${id}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setIndustry(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setIndustry(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateIndustryOrder = useCallback(
    async (industryList) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/update-industry-order`,
          { industry: industryList },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setIndustry(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setIndustry(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Job Role
  const getJobRole = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/onboarding/get-jobRoles`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setJobRole(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setJobRole(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);
  const addJobRole = useCallback(
    async (description, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/onboarding/add-jobRole`,
          { description, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJobRole(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setJobRole(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteJobRole = useCallback(
    async (id) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/onboarding/delete-jobRole?_id=${id}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJobRole(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setJobRole(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const editJobRole = useCallback(
    async (_id, description, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/edit-jobRole`,
          { _id, description, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJobRole(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setJobRole(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateJobRoleOrder = useCallback(
    async (jobRoleList) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/update-jobRole-order`,
          { jobRole: jobRoleList },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setJobRole(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setJobRole(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Management Role
  const addManagementRole = useCallback(
    async (description, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/onboarding/add-managementRole`,
          { description, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setManagementRoles(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setManagementRoles(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteManagementRole = useCallback(
    async (_id) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/onboarding/delete-managementRole?_id=${_id}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setManagementRoles(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setManagementRoles(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const editManagementRole = useCallback(
    async (_id, description, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/edit-managementRole`,
          { _id, description, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setManagementRoles(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setManagementRoles(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateManagementRoleOrder = useCallback(
    async (managementRoleList) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/update-managementRole-order`,
          { managementRole: managementRoleList },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setManagementRoles(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setManagementRoles(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const getManagementRoles = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/onboarding/get-managementRoles`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setManagementRoles(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setManagementRoles(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  // Function
  const getFunctions = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/onboarding/get-functions`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setFunctions(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setFunctions(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);
  const addFunction = useCallback(
    async (functionDescription, functionSlug, translations) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/onboarding/add-function`,
          { functionDescription, functionSlug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setFunctions(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setFunctions(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteFunction = useCallback(
    async (_id) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/onboarding/delete-function?_id=${_id}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setFunctions(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setFunctions(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateFunctionOrder = useCallback(
    async (functionList) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/update-function-order`,
          { functionData: functionList },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setFunctions(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setFunctions(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const editFunction = useCallback(
    async (_id, description, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/edit-function`,
          { _id, description, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setFunctions(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setFunctions(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  // Level
  const getLevels = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/onboarding/get-levels`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setLevels(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setLevels(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);
  const addLevel = useCallback(
    async (description, nextLevel, isLastLevel, modalContent, slug, translations) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/onboarding/add-level`,
          { description, nextLevel, isLastLevel, modalContent, slug, translations },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setLevels(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        const errorMessage = err.response?.data.message;
        setError(errorMessage);

        setLevels(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const deleteLevel = useCallback(
    async (_id) => {
      setLoading(true);
      try {
        const response = await axios.delete(
          `${CONFIG.site.serverUrl}/onboarding/delete-level?_id=${_id}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setLevels(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setLevels(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateLevelOrder = useCallback(
    async (levelList) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/update-level-order`,
          { levelData: levelList },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setLevels(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setLevels(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const editLevel = useCallback(
    async (_id, description, slug, translations, nextLevel, isLastLevel, modalContent) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/onboarding/edit-level`,
          { _id, description, slug, translations, nextLevel, isLastLevel, modalContent },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setLevels(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setLevels(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    industry,
    jobRole,
    managementRoles,
    functions,
    levels,
    getIndustry,
    addIndustry,
    deleteIndustry,
    editIndustry,
    updateIndustryOrder,
    getJobRole,
    addJobRole,
    deleteJobRole,
    editJobRole,
    updateJobRoleOrder,
    addManagementRole,
    deleteManagementRole,
    editManagementRole,
    updateManagementRoleOrder,
    getManagementRoles,
    getFunctions,
    addFunction,
    deleteFunction,
    updateFunctionOrder,
    editFunction,
    getLevels,
    addLevel,
    deleteLevel,
    updateLevelOrder,
    editLevel,
    loading,
    error,
  };
};
