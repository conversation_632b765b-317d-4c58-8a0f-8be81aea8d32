/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const useAITranslator = () => {
  const [translatedText, setTranslatedText] = useState(null);
  const [platformLanguages, setPlatformLanguages] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getPlatformLanguages = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/platform-lang/get`, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      setPlatformLanguages(response.data.data);
      setError(null);
      return response.data;
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const translateJourney = useCallback(
    async (journey, from, to) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/translator/translateJourney`,
          { journey, from, to },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setTranslatedText(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setTranslatedText(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const translate = useCallback(
    async (formData, from, to) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/translator/translate`,
          { formData, from, to },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setTranslatedText(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setTranslatedText(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    getPlatformLanguages,
    translateJourney,
    translate,
    translatedText,
    platformLanguages,
    loading,
    error,
  };
};
