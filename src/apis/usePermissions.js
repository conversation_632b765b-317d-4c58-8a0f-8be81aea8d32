/* eslint-disable consistent-return */
import axios from 'axios';
import { useCallback, useState } from 'react';

import { CONFIG } from 'src/config-global';

export const usePermissions = () => {
  const [roles, setRoles] = useState(null);
  const [permissions, setPermissions] = useState(null);
  const [users, setUsers] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const getRoles = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/permissions/getRoles`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      setRoles(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setRoles(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);
  const getPermissions = useCallback(
    async (role) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/permissions/getPermissions`,
          { role },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setPermissions(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err.response.data.data);
        setPermissions(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updatePermissions = useCallback(
    async (role, perms) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/permissions/updatePermission`,
          { role, perms },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setPermissions(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
        setPermissions(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const getUsers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${CONFIG.site.serverUrl}/user/all`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });
      setUsers(response.data.data);
      setError(null);
      return response.data.data;
    } catch (err) {
      setError(err);
      setUsers(null);
    } finally {
      setLoading(false);
    }
  }, [authToken]);

  const addRole = useCallback(
    async (role) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/permissions/addRole`,
          { role },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );
        setRoles(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );
  const updateRole = useCallback(
    async (oldRole, newRole) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/permissions/updateRole`,
          { oldRole, newRole },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );
        setRoles(response.data.data);
        setError(null);
        return response.data.data;
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  return {
    getRoles,
    updatePermissions,
    getPermissions,
    getUsers,
    addRole,
    updateRole,
    permissions,
    roles,
    loading,
    error,
    users,
  };
};
