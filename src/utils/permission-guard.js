import PropTypes from 'prop-types';
import { Navigate } from 'react-router-dom';

const PermissionGuard = ({ path, subPath, children }) => {
  const permissions = JSON.parse(localStorage.getItem('permissions'));

  const permissionStatus = permissions?.[path]?.[subPath];

  if (permissionStatus === false) {
    return <Navigate to="/403" />;
  }

  return <>{children}</>;
};
PermissionGuard.propTypes = {
  path: PropTypes.string.isRequired,
  subPath: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};

export default PermissionGuard;
