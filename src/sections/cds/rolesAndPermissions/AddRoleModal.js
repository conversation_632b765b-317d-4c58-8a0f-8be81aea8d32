import {
  Button,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
} from '@mui/material';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { usePermissions } from 'src/apis';

function AddRoleModal({ open, onClose }) {
  const [newRoleName, setNewRoleName] = useState('');
  const { addRole } = usePermissions();

  const handleAddRole = async () => {
    if (newRoleName.trim() !== '') {
      await addRole(newRoleName).then((res) => {
        if (res) {
          setNewRoleName('');
          toast.success('Role added successfully!');
          onClose();
          window.location.reload();
        } else {
          toast.error('This role already exists!');
        }
      });
    } else {
      toast.error('Please enter a role name!');
    }
  };

  const handleClose = () => {
    setNewRoleName('');
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle>Add New Role</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          label="Role Name"
          fullWidth
          variant="outlined"
          value={newRoleName}
          onChange={(e) => setNewRoleName(e.target.value)}
          sx={{ mt: 2 }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleAddRole} variant="contained">
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
}

AddRoleModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default AddRoleModal;
