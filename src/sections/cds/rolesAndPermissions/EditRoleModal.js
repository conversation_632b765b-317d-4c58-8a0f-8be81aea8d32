import { Close as CloseIcon } from '@mui/icons-material';
import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { usePermissions } from 'src/apis';

const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '90%',
  maxHeight: '90%',
  bgcolor: 'background.paper',
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
  overflow: 'auto',
};

export default function EditRoleModal({ open, onClose, selectedRole, onUpdate }) {
  const [roleName, setRoleName] = useState('');
  const [permissions, setPermissions] = useState({});
  const { updatePermissions, updateRole } = usePermissions();

  useEffect(() => {
    if (selectedRole) {
      setRoleName(selectedRole.role);
      const { _id, role, ...permissionData } = selectedRole;
      setPermissions(permissionData);
    }
  }, [selectedRole]);

  const handlePermissionChange = (section, key) => (event) => {
    setPermissions((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: event.target.checked,
      },
    }));
  };

  const handleSave = async () => {
    try {
      const updatedRole = {
        role: roleName,
        ...permissions,
      };
      await updateRole(selectedRole.role, roleName);
      await updatePermissions(roleName, updatedRole).then((res) => {
        if (res) {
          toast.success('Role updated successfully');
          onClose();
          window.location.reload();
        } else {
          toast.error('Failed to update role');
        }
      });
    } catch (error) {
      console.error('Role update error:', error);
    }
  };

  const renderPermissionSection = (sectionName, sectionData) => (
    <Box
      key={sectionName}
      sx={{ mb: 3, border: '1px solid #e0e0e0', borderRadius: 1, boxShadow: 1 }}
    >
      <Typography
        sx={{
          textTransform: 'capitalize',
          fontWeight: 'bold',
          p: 1,

          borderRadius: 1,
          mb: 1,
        }}
      >
        {sectionName}
      </Typography>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          pl: 2,
        }}
      >
        {Object.entries(sectionData).map(([key, value]) => (
          <FormControlLabel
            key={key}
            control={
              <Checkbox
                checked={Boolean(value)}
                onChange={handlePermissionChange(sectionName, key)}
              />
            }
            label={<Typography sx={{ textTransform: 'capitalize' }}>{key}</Typography>}
          />
        ))}
      </Box>
    </Box>
  );

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="edit-role-modal">
      <Box sx={modalStyle}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">Edit Role: {selectedRole?.role}</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <TextField
            label="Role Name"
            value={roleName}
            onChange={(e) => setRoleName(e.target.value)}
            fullWidth
          />

          <Divider sx={{ my: 2 }} />

          <Typography variant="subtitle1" sx={{ mb: 2 }}>
            Permissions
          </Typography>

          <Box sx={{ maxHeight: '50vh', overflow: 'auto' }}>
            {Object.entries(permissions).map(([section, sectionData]) =>
              renderPermissionSection(section, sectionData)
            )}
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
            <Button variant="outlined" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="contained" onClick={handleSave}>
              Save Changes
            </Button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
}
