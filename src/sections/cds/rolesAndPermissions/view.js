import { AdminPanelSettings, EditNote, PersonOutline } from '@mui/icons-material';
import {
  Box,
  Button,
  Card,
  CardContent,
  Container,
  Divider,
  Grid,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { usePermissions } from 'src/apis';
import { useSettingsContext } from 'src/components/settings';
import AddRoleModal from './AddRoleModal';
import EditRoleModal from './EditRoleModal';

function RolesAndPermissionsView() {
  const settings = useSettingsContext();

  const { getRoles, getUsers, getPermissions, roles, users } = usePermissions();
  const [openModal, setOpenModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [openAddModal, setOpenAddModal] = useState(false);

  useEffect(() => {
    getRoles();
    getUsers();
  }, [getRoles, getUsers]);

  const getUserCountByRole = () => {
    const counts = {};
    users?.forEach((user) => {
      counts[user.role] = (counts[user.role] || 0) + 1;
    });
    return counts;
  };

  const userCounts = getUserCountByRole();

  const getIconByRole = (roleName) => {
    const iconProps = {
      sx: {
        fontSize: 50,
      },
    };

    switch (roleName.toLowerCase()) {
      case 'admin':
        return <AdminPanelSettings {...iconProps} />;
      case 'editor':
        return <EditNote {...iconProps} />;
      case 'user':
        return <PersonOutline {...iconProps} />;
      default:
        return <PersonOutline {...iconProps} />;
    }
  };

  const handleEditRole = async (role) => {
    try {
      // Seçilen role göre permissions'ları getir
      await getPermissions(role.role.toLowerCase()).then((res) => {
        if (res) {
          const roleWithPermissions = {
            ...role,
            ...res?.perms[0], // API'den gelen permissions
          };

          setSelectedRole(roleWithPermissions);
          setOpenModal(true);
        }
      });

      // permissions geldiğinde selectedRole'ü güncelle
    } catch (error) {
      console.error('Error fetching permissions:', error);
      // Hata durumunda kullanıcıya bilgi verilebilir
    }
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSelectedRole(null);
  };

  const handleUpdateRole = (updatedRole) => {
    // Permissions'ları yeniden çek
    getPermissions(updatedRole.role.toLowerCase());

    // Roles listesini yeniden çek
    getRoles();
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4" sx={{ mb: 3 }}>
        Roles & Permissions
      </Typography>

      <Grid container spacing={3}>
        {roles?.map((role) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={role._id}>
            <Card>
              <CardContent
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: 3,
                  gap: 2,
                }}
              >
                {getIconByRole(role.role)}

                <Divider
                  orientation="vertical"
                  flexItem
                  sx={{
                    height: '100px', // Divider yüksekliği
                    my: 'auto', // Dikey ortalama
                  }}
                />

                <Box sx={{ flexGrow: 1, ml: 2 }}>
                  <Typography variant="h5">
                    {role.role.charAt(0).toUpperCase() + role.role.slice(1)}
                  </Typography>

                  <Typography variant="subtitle2">
                    Total {userCounts[role.role] || 0} users
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ mt: 2 }}
                    onClick={() => handleEditRole(role)}
                  >
                    Edit Role
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
        <Button
          variant="contained"
          color="primary"
          sx={{ mt: 3 }}
          onClick={() => setOpenAddModal(true)}
        >
          Add Role
        </Button>
      </Box>

      <AddRoleModal open={openAddModal} onClose={() => setOpenAddModal(false)} />

      <EditRoleModal
        open={openModal}
        onClose={handleCloseModal}
        selectedRole={selectedRole}
        onUpdate={handleUpdateRole}
      />
    </Container>
  );
}

export default RolesAndPermissionsView;
