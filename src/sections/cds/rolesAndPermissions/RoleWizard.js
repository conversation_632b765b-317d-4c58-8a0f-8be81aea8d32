import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { usePermissions } from 'src/apis';

function RoleWizard({ open, onClose, onComplete }) {
  const [activeStep, setActiveStep] = useState(0);
  const [roleName, setRoleName] = useState('');
  const [permissions, setPermissions] = useState({});
  const { getPermissions } = usePermissions();

  useEffect(() => {
    const fetchAdminPermissions = async () => {
      try {
        const result = await getPermissions('admin');
        if (result?.perms?.[0]) {
          const { _id, role, __v, ...permissionData } = result.perms[0];

          // Initialize all values as false for each section
          const initialPerms = {};
          Object.entries(permissionData).forEach(([section, sectionData]) => {
            initialPerms[section] = {};
            Object.keys(sectionData).forEach((key) => {
              initialPerms[section][key] = false;
            });
          });

          setPermissions(initialPerms);
        }
      } catch (error) {
        console.error('Error fetching admin permissions:', error);
      }
    };

    if (open && activeStep === 1) {
      fetchAdminPermissions();
    }
  }, [open, activeStep, getPermissions]);

  const handleNext = () => {
    if (activeStep === steps.length - 1) {
      onComplete({ role: roleName, permissions });
      onClose();
    } else {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const handlePermissionChange = (section, key) => (event) => {
    setPermissions((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: event.target.checked,
      },
    }));
  };

  const renderPermissionSection = (sectionName, sectionData) => (
    <Box key={sectionName} sx={{ mb: 3 }}>
      <Typography
        sx={{
          textTransform: 'capitalize',
          fontWeight: 'bold',
          backgroundColor: 'primary.lighter',
          p: 1,
          borderRadius: 1,
          mb: 1,
        }}
      >
        {sectionName}
      </Typography>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          pl: 2,
        }}
      >
        {Object.entries(sectionData).map(([key, value]) => (
          <FormControlLabel
            key={key}
            control={
              <Checkbox
                checked={Boolean(value)}
                onChange={handlePermissionChange(sectionName, key)}
              />
            }
            label={<Typography sx={{ textTransform: 'capitalize' }}>{key}</Typography>}
          />
        ))}
      </Box>
    </Box>
  );

  const steps = ['Role Name', 'Permissions'];

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <TextField
            autoFocus
            margin="dense"
            label="Role Name"
            fullWidth
            value={roleName}
            onChange={(e) => setRoleName(e.target.value)}
          />
        );
      case 1:
        return (
          <Box sx={{ maxHeight: '50vh', overflow: 'auto' }}>
            {Object.entries(permissions).map(([section, sectionData]) =>
              renderPermissionSection(section, sectionData)
            )}
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Role</DialogTitle>
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ my: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {renderStepContent(activeStep)}

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          {activeStep > 0 && (
            <Button onClick={handleBack} sx={{ mr: 1 }}>
              Back
            </Button>
          )}
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={activeStep === 0 && !roleName.trim()}
          >
            {activeStep === steps.length - 1 ? 'Complete' : 'Next'}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
}

export default RoleWizard;
