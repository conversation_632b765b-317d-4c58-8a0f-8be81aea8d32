import { Box, Card, Collapse, Stack, Typography, Chip, LinearProgress, Paper } from '@mui/material';
import { Upload } from 'src/components/upload';
import { LoadingButton } from '@mui/lab';
import Iconify from 'src/components/iconify';
import { useState } from 'react';

export default function ConvertHLSView({
  file,
  handleDrop,
  handleDeleteFile,
  handleSubmit,
  isSubmitting,
  subtitleProgress,
  subtitleResult,
  progressDetails,
  expandedSections,
  copyToClipboard,
}) {
  return (
    <Stack spacing={3}>
      {!file && (
        <Upload
          multiple={false}
          accept={{ 'video/*': [] }}
          onDrop={handleDrop}
          file={file}
          onDelete={handleDeleteFile}
          helperText={'Upload an MP4 video to convert to HLS format'}
          placeholder={'Drop or select video to convert to HLS'}
        />
      )}

      {file?.preview && (
        <Box sx={{ width: '100%', height: 500, position: 'relative', mt: 2 }}>
          <video
            src={file.preview}
            controls
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              borderRadius: 8,
            }}
          />
        </Box>
      )}

      {/* Progress Card */}
      {subtitleProgress && (
        <Card>
          <Box
            sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
          >
            <Typography variant="h6">Processing Status</Typography>
            <Chip
              label={subtitleProgress.status}
              color={
                subtitleProgress.status === 'completed'
                  ? 'success'
                  : subtitleProgress.status === 'error'
                    ? 'error'
                    : 'primary'
              }
            />
          </Box>

          <Collapse in={expandedSections.progress}>
            <Box sx={{ p: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {subtitleProgress.message}
              </Typography>

              <LinearProgress
                variant="determinate"
                value={subtitleProgress.percent || 0}
                sx={{ mb: 2 }}
              />

              {progressDetails.length > 0 && (
                <Paper variant="outlined" sx={{ p: 2, maxHeight: 200, overflow: 'auto' }}>
                  {progressDetails.map((detail, index) => (
                    <Box key={index} sx={{ mb: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(detail.time).toLocaleTimeString()}
                      </Typography>
                      <Typography variant="body2">{detail.message}</Typography>
                    </Box>
                  ))}
                </Paper>
              )}
            </Box>
          </Collapse>
        </Card>
      )}

      {/* Result Card */}
      {subtitleResult && (
        <Card>
          <Box
            sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
          >
            <Typography variant="h6">Generated Files</Typography>
            <Chip
              label={subtitleResult.status}
              color={subtitleResult.status === 'completed' ? 'success' : 'error'}
            />
          </Box>

          <Collapse in={expandedSections.result}>
            <Box sx={{ p: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                HLS Playlist URL
              </Typography>

              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <LoadingButton
                  size="small"
                  onClick={() => copyToClipboard(subtitleResult.mainPlaylistUrl)}
                  startIcon={<Iconify icon="mdi:content-copy" />}
                >
                  Copy URL
                </LoadingButton>
                <LoadingButton
                  size="small"
                  onClick={() => window.open(subtitleResult.mainPlaylistUrl, '_blank')}
                  startIcon={<Iconify icon="mdi:open-in-new" />}
                >
                  Open
                </LoadingButton>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Segment Count: {subtitleResult.segmentCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Folder: {subtitleResult.folderName}
              </Typography>
            </Box>
          </Collapse>
        </Card>
      )}

      <LoadingButton
        type="submit"
        variant="contained"
        size="large"
        loading={isSubmitting}
        startIcon={<Iconify icon="mdi:cloud-upload-outline" />}
        loadingIndicator={'Converting...'}
        disabled={!file || (subtitleProgress && subtitleProgress.status === 'completed')}
        onClick={handleSubmit}
      >
        Convert to HLS
      </LoadingButton>
    </Stack>
  );
}
