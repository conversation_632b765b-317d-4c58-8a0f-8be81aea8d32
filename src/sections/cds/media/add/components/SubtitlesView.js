import { Box, Stack } from '@mui/material';
import { Upload } from 'src/components/upload';
import { LoadingButton } from '@mui/lab';
import Iconify from 'src/components/iconify';
import SubtitleOptions from './SubtitleOptions';
import SubtitleProgress from './SubtitleProgress';
import SubtitleResult from './SubtitleResult';

export default function SubtitlesView({
  file,
  handleDrop,
  handleDeleteFile,
  handleSubmit,
  isSubmitting,
  subtitleProgress,
  subtitleResult,
  language,
  setLanguage,
  segmentDuration,
  setSegmentDuration,
  timeOffset,
  setTimeOffset,
  progressDetails,
  expandedSections,
  toggleSection,
  editedSrtContent,
  setEditedSrtContent,
  isEditing,
  setIsEditing,
  isSaving,
  handleSaveSrtContent,
  copyToClipboard,
  getStatusColor,
}) {
  return (
    <Stack spacing={3}>
      {!file && (
        <Upload
          multiple={false}
          accept={{ 'video/*': [] }}
          onDrop={handleDrop}
          file={file}
          onDelete={handleDeleteFile}
          helperText={'Upload an MP4 video to generate subtitles'}
          placeholder={'Drop or select video to generate subtitles'}
        />
      )}

      {file?.preview && (
        <Box sx={{ width: '100%', height: 500, position: 'relative', mt: 2 }}>
          <video
            src={file.preview}
            controls
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              borderRadius: 8,
            }}
          />
        </Box>
      )}

      {file && (
        <SubtitleOptions
          language={language}
          setLanguage={setLanguage}
          segmentDuration={segmentDuration}
          setSegmentDuration={setSegmentDuration}
          timeOffset={timeOffset}
          setTimeOffset={setTimeOffset}
          expandedSections={expandedSections}
          toggleSection={toggleSection}
        />
      )}

      <SubtitleProgress
        subtitleProgress={subtitleProgress}
        progressDetails={progressDetails}
        expandedSections={expandedSections}
        toggleSection={toggleSection}
        getStatusColor={getStatusColor}
      />

      <SubtitleResult
        subtitleResult={subtitleResult}
        expandedSections={expandedSections}
        toggleSection={toggleSection}
        editedSrtContent={editedSrtContent}
        setEditedSrtContent={setEditedSrtContent}
        isEditing={isEditing}
        setIsEditing={setIsEditing}
        isSaving={isSaving}
        handleSaveSrtContent={handleSaveSrtContent}
        copyToClipboard={copyToClipboard}
      />

      <LoadingButton
        type="submit"
        variant="contained"
        size="large"
        loading={isSubmitting}
        startIcon={<Iconify icon="mdi:subtitles-outline" />}
        loadingIndicator={'Processing...'}
        disabled={!file || subtitleProgress?.status === 'completed'}
        onClick={handleSubmit}
      >
        Generate Subtitles
      </LoadingButton>
    </Stack>
  );
}
