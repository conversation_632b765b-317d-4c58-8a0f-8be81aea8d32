import {
  Box,
  Card,
  Collapse,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
  alpha,
  useTheme,
} from '@mui/material';
import Iconify from 'src/components/iconify';

export default function SubtitleOptions({
  language,
  setLanguage,
  segmentDuration,
  setSegmentDuration,
  timeOffset,
  setTimeOffset,
  expandedSections,
  toggleSection,
}) {
  const theme = useTheme();

  return (
    <Card
      sx={{
        mt: 3,
        overflow: 'visible',
        borderRadius: 2,
        boxShadow: theme.shadows[2],
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: theme.shadows[4],
        },
      }}
    >
      <Box
        sx={{
          p: 2.5,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: expandedSections.options ? '1px solid' : 'none',
          borderColor: 'divider',
          bgcolor: alpha(theme.palette.primary.main, 0.05),
        }}
      >
        <Stack direction="row" spacing={1.5} alignItems="center">
          <Iconify icon="mdi:cog-outline" sx={{ color: 'primary.main' }} />
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Processing Options
          </Typography>
        </Stack>
        <IconButton
          onClick={() => toggleSection('options')}
          size="small"
          sx={{
            bgcolor: alpha(theme.palette.primary.main, 0.1),
            '&:hover': {
              bgcolor: alpha(theme.palette.primary.main, 0.2),
            },
          }}
        >
          <Iconify icon={expandedSections.options ? 'mdi:chevron-up' : 'mdi:chevron-down'} />
        </IconButton>
      </Box>

      <Collapse in={expandedSections.options}>
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="language-label">Language</InputLabel>
                <Select
                  labelId="language-label"
                  value={language}
                  label="Language"
                  onChange={(e) => setLanguage(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: alpha(theme.palette.primary.main, 0.2),
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  }}
                >
                  <MenuItem value="en-US">English (US)</MenuItem>
                  <MenuItem value="tr-TR">Turkish</MenuItem>
                  <MenuItem value="fr-FR">French</MenuItem>
                  <MenuItem value="de-DE">German</MenuItem>
                  <MenuItem value="es-ES">Spanish</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label="Segment Duration (seconds)"
                type="number"
                value={segmentDuration}
                onChange={(e) => setSegmentDuration(parseInt(e.target.value, 10))}
                inputProps={{ min: 2, max: 60, step: 1 }}
                helperText="Shorter segments (2-5s) provide more accurate timing"
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha(theme.palette.primary.main, 0.2),
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Time Offset (seconds)"
                type="number"
                value={timeOffset}
                onChange={(e) => setTimeOffset(parseFloat(e.target.value))}
                inputProps={{ min: -10, max: 10, step: 0.1 }}
                helperText="Adjust timing: positive = delay, negative = advance subtitles"
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha(theme.palette.primary.main, 0.2),
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </Collapse>
    </Card>
  );
}
