import {
  Box,
  Card,
  Chip,
  Collapse,
  IconButton,
  LinearProgress,
  Paper,
  Stack,
  Typography,
  alpha,
  useTheme,
} from '@mui/material';
import Iconify from 'src/components/iconify';

export default function SubtitleProgress({
  subtitleProgress,
  progressDetails,
  expandedSections,
  toggleSection,
  getStatusColor,
}) {
  const theme = useTheme();

  if (!subtitleProgress) return null;

  return (
    <Card
      sx={{
        mt: 3,
        overflow: 'visible',
        borderRadius: 2,
        boxShadow: theme.shadows[2],
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: theme.shadows[4],
        },
      }}
    >
      <Box
        sx={{
          p: 2.5,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: expandedSections.progress ? '1px solid' : 'none',
          borderColor: 'divider',
          bgcolor: alpha(theme.palette.primary.main, 0.05),
        }}
      >
        <Stack direction="row" spacing={1.5} alignItems="center">
          <Iconify icon="mdi:progress-clock" sx={{ color: 'primary.main' }} />
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Processing Status
          </Typography>
        </Stack>
        <Stack direction="row" spacing={1.5} alignItems="center">
          {subtitleProgress.status === 'completed' ? (
            <Chip
              label="Completed"
              color="success"
              size="small"
              sx={{
                fontWeight: 600,
                '& .MuiChip-label': { px: 1.5 },
              }}
            />
          ) : subtitleProgress.status === 'error' ? (
            <Chip
              label="Error"
              color="error"
              size="small"
              sx={{
                fontWeight: 600,
                '& .MuiChip-label': { px: 1.5 },
              }}
            />
          ) : (
            <Chip
              label="Processing"
              color="primary"
              size="small"
              sx={{
                fontWeight: 600,
                '& .MuiChip-label': { px: 1.5 },
              }}
            />
          )}
          <IconButton
            onClick={() => toggleSection('progress')}
            size="small"
            sx={{
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.2),
              },
            }}
          >
            <Iconify icon={expandedSections.progress ? 'mdi:chevron-up' : 'mdi:chevron-down'} />
          </IconButton>
        </Stack>
      </Box>

      <Collapse in={expandedSections.progress}>
        <Box sx={{ p: 3 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 500, maxWidth: '80%' }}>
              {subtitleProgress.message || `Status: ${subtitleProgress.status}`}
            </Typography>

            {subtitleProgress.segmentInfo && (
              <Typography
                variant="caption"
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  px: 1.5,
                  py: 0.75,
                  borderRadius: 1.5,
                }}
              >
                {subtitleProgress.segmentInfo}
              </Typography>
            )}
          </Stack>

          <LinearProgress
            variant="determinate"
            value={subtitleProgress.percent || 0}
            sx={{
              height: 8,
              borderRadius: 4,
              mb: 1.5,
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
              },
            }}
          />

          <Stack direction="row" justifyContent="space-between" sx={{ mb: 2.5 }}>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              {subtitleProgress.status || ''}
            </Typography>
            <Typography variant="caption" sx={{ fontWeight: 600 }}>
              {subtitleProgress.percent || 0}%
            </Typography>
          </Stack>

          <Paper
            variant="outlined"
            sx={{
              mt: 2,
              p: 2,
              height: 150,
              overflow: 'auto',
              bgcolor: alpha(theme.palette.background.default, 0.6),
              borderRadius: 2,
              fontSize: '0.75rem',
              borderColor: alpha(theme.palette.primary.main, 0.2),
            }}
          >
            {progressDetails.map((detail, index) => (
              <Box key={index} sx={{ mb: 1, display: 'flex', alignItems: 'flex-start' }}>
                <Typography
                  variant="caption"
                  sx={{
                    color: 'text.secondary',
                    minWidth: 70,
                    fontWeight: 500,
                  }}
                >
                  {detail.time.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                  })}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    ml: 1.5,
                    flex: 1,
                    color: getStatusColor(detail.status),
                    fontWeight: 500,
                  }}
                >
                  {detail.message || detail.status}
                </Typography>
                {detail.percent !== undefined && (
                  <Typography
                    variant="caption"
                    sx={{
                      ml: 1.5,
                      color: 'text.secondary',
                      fontWeight: 600,
                    }}
                  >
                    {detail.percent}%
                  </Typography>
                )}
              </Box>
            ))}
          </Paper>
        </Box>
      </Collapse>
    </Card>
  );
}
