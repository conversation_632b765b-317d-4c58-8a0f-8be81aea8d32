import { Box, TextField, Stack } from '@mui/material';
import { Upload } from 'src/components/upload';
import { LoadingButton } from '@mui/lab';
import Iconify from 'src/components/iconify';

export default function VideoView({
  file,
  title,
  setTitle,
  handleDrop,
  handleDeleteFile,
  handleSubmit,
  isSubmitting,
}) {
  return (
    <Stack spacing={3}>
      <TextField
        label="Title"
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        fullWidth
        required
      />

      <Upload
        multiple={false}
        accept={{ 'video/*': [] }}
        onDrop={handleDrop}
        file={file}
        onDelete={handleDeleteFile}
        helperText={''}
        placeholder={'Drop or select video'}
      />

      {file?.preview && (
        <Box sx={{ width: '100%', height: 500, position: 'relative', mt: 2 }}>
          <video
            src={file.preview}
            controls
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              borderRadius: 8,
            }}
          />
        </Box>
      )}

      <LoadingButton
        type="submit"
        variant="contained"
        size="large"
        loading={isSubmitting}
        startIcon={<Iconify icon="mdi:cloud-upload-outline" />}
        loadingIndicator={'Uploading...'}
        disabled={!file}
        onClick={handleSubmit}
      >
        Upload Media
      </LoadingButton>
    </Stack>
  );
}
