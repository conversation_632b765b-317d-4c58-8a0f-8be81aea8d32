import {
  Box,
  <PERSON>ton,
  Card,
  Chip,
  Collapse,
  IconButton,
  InputAdornment,
  Paper,
  Stack,
  TextField,
  Tooltip,
  Typography,
  alpha,
  useTheme,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import Iconify from 'src/components/iconify';

export default function SubtitleResult({
  subtitleResult,
  expandedSections,
  toggleSection,
  editedSrtContent,
  setEditedSrtContent,
  isEditing,
  setIsEditing,
  isSaving,
  handleSaveSrtContent,
  copyToClipboard,
}) {
  const theme = useTheme();

  if (!subtitleResult) return null;

  // API yanıtındaki URL'leri hazırla
  const srtUrl = subtitleResult.srtUrl || '';

  // Container bilgilerini hazırla
  const srtContainer = subtitleResult.containerName || 'transcripts';

  return (
    <Card sx={{ mt: 3, overflow: 'visible' }}>
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: expandedSections.result ? '1px solid' : 'none',
          borderColor: 'divider',
        }}
      >
        <Stack direction="row" spacing={1} alignItems="center">
          <Iconify icon="mdi:file-document-outline" />
          <Typography variant="subtitle1">Generated Files</Typography>
        </Stack>
        <IconButton onClick={() => toggleSection('result')} size="small">
          <Iconify icon={expandedSections.result ? 'mdi:chevron-up' : 'mdi:chevron-down'} />
        </IconButton>
      </Box>

      <Collapse in={expandedSections.result}>
        <Box sx={{ p: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            File:{' '}
            <Typography component="span" variant="body2" color="text.secondary">
              {subtitleResult.srtFileName}
            </Typography>
          </Typography>

          {/* SRT URL */}
          {srtUrl && (
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                sx={{ mb: 1 }}
              >
                <Stack direction="row" spacing={1} alignItems="center">
                  <Iconify icon="mdi:file-document-outline" color="primary.main" />
                  <Typography variant="subtitle2">SRT Subtitle File</Typography>
                  <Chip
                    label={`Container: ${srtContainer}`}
                    size="small"
                    variant="outlined"
                    color="default"
                  />
                </Stack>
                <Stack direction="row" spacing={1}>
                  <Tooltip title="Copy URL">
                    <IconButton size="small" onClick={() => copyToClipboard(srtUrl)}>
                      <Iconify icon="mdi:content-copy" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Download">
                    <IconButton size="small" component="a" href={srtUrl} target="_blank">
                      <Iconify icon="mdi:download" />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
              <TextField
                value={srtUrl}
                fullWidth
                size="small"
                InputProps={{
                  readOnly: true,
                  endAdornment: (
                    <InputAdornment position="end">
                      <Tooltip title="Open">
                        <IconButton
                          edge="end"
                          component="a"
                          href={srtUrl}
                          target="_blank"
                          size="small"
                        >
                          <Iconify icon="mdi:open-in-new" />
                        </IconButton>
                      </Tooltip>
                    </InputAdornment>
                  ),
                }}
              />
            </Paper>
          )}

          {/* SRT Content Editor */}
          <Paper variant="outlined" sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="subtitle2">SRT Content</Typography>
                <Stack direction="row" spacing={1}>
                  {isEditing ? (
                    <>
                      <Button
                        size="small"
                        variant="outlined"
                        color="inherit"
                        startIcon={<Iconify icon="mdi:close" />}
                        onClick={() => {
                          setIsEditing(false);
                          setEditedSrtContent(subtitleResult.srtContent);
                        }}
                      >
                        Cancel
                      </Button>
                      <LoadingButton
                        size="small"
                        variant="contained"
                        color="primary"
                        startIcon={<Iconify icon="mdi:content-save" />}
                        loading={isSaving}
                        onClick={handleSaveSrtContent}
                      >
                        Save
                      </LoadingButton>
                    </>
                  ) : (
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<Iconify icon="mdi:pencil" />}
                      onClick={() => setIsEditing(true)}
                    >
                      Edit
                    </Button>
                  )}
                </Stack>
              </Stack>
            </Box>
            {isEditing ? (
              <TextField
                multiline
                fullWidth
                rows={10}
                value={editedSrtContent}
                onChange={(e) => setEditedSrtContent(e.target.value)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 0,
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                }}
                InputProps={{
                  sx: {
                    fontFamily: 'monospace',
                    fontSize: '0.875rem',
                  },
                }}
              />
            ) : (
              <Box
                sx={{
                  maxHeight: 300,
                  overflow: 'auto',
                  p: 2,
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  bgcolor: (theme) =>
                    theme.palette.mode === 'dark' ? 'background.default' : '#f5f5f5',
                }}
              >
                <pre style={{ margin: 0 }}>
                  {editedSrtContent || subtitleResult?.srtContent || 'No SRT content'}
                </pre>
              </Box>
            )}
          </Paper>
        </Box>
      </Collapse>
    </Card>
  );
}
