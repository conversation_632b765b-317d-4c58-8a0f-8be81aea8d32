/* eslint-disable no-nested-ternary */
/* eslint-disable react-hooks/exhaustive-deps */

// @mui
import {
  Box,
  Card,
  Container,
  Divider,
  Stack,
  Tab,
  Tabs,
  Typography,
  alpha,
  useTheme,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useMedia } from 'src/apis/useMedia';

// components
import Iconify from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';

// components
import ImageView from './components/ImageView';
import VideoView from './components/VideoView';
import PdfView from './components/PdfView';
import SubtitlesView from './components/SubtitlesView';
import ConvertHLSView from './components/ConvertHLSView';

// ----------------------------------------------------------------------

export default function AddMediaView() {
  const settings = useSettingsContext();
  const { uploadMedia, generateSubtitles, updateSrtContent, convertToHLS } = useMedia();
  const [currentTab, setCurrentTab] = useState('images');
  const [title, setTitle] = useState('');
  const [file, setFile] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [subtitleProgress, setSubtitleProgress] = useState(null);
  const [subtitleResult, setSubtitleResult] = useState(null);
  const [progressDetails, setProgressDetails] = useState([]);
  const [expandedSections, setExpandedSections] = useState({
    progress: true,
    result: true,
  });

  // SRT Editing state
  const [editedSrtContent, setEditedSrtContent] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [videoPlayerOpen, setVideoPlayerOpen] = useState(false);

  // Subtitle generation options
  const [language, setLanguage] = useState('en-US');
  const [segmentDuration, setSegmentDuration] = useState(2);
  const [timeOffset, setTimeOffset] = useState(0);

  const theme = useTheme();

  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleChangeTab = (event, newValue) => {
    setCurrentTab(newValue);
    setFile(null);
    setTitle('');
    setSubtitleProgress(null);
    setSubtitleResult(null);
    setEditedSrtContent('');
    setIsEditing(false);
  };

  const handleDrop = (acceptedFiles) => {
    const newFile = acceptedFiles[0];
    if (newFile) {
      const fileNameWithoutExtension = newFile.name.replace(/\.[^/.]+$/, '');
      setTitle(fileNameWithoutExtension);

      // Video dosyası için önizleme URL'si oluştur
      let previewUrl;
      if (newFile.type.startsWith('video/')) {
        previewUrl = URL.createObjectURL(newFile);
      } else {
        previewUrl = URL.createObjectURL(newFile);
      }

      setFile({
        ...newFile,
        preview: previewUrl,
        path: newFile.name,
        originalFile: newFile,
      });
    }
  };

  const handleDeleteFile = () => {
    setFile(null);
    setTitle('');
    setSubtitleProgress(null);
    setSubtitleResult(null);
    setEditedSrtContent('');
    setIsEditing(false);
  };

  useEffect(
    () => () => {
      if (file?.preview) {
        URL.revokeObjectURL(file.preview);
      }
    },
    [file]
  );

  useEffect(() => {
    if (subtitleResult?.srtContent) {
      setEditedSrtContent(subtitleResult.srtContent);
    }
  }, [subtitleResult]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubtitleProgress(null);
    setSubtitleResult(null);
    setProgressDetails([]);

    try {
      if (currentTab === 'subtitles' || currentTab === 'convert-hls') {
        if (!title) {
          toast.error('Please enter a title');
          setIsSubmitting(false);
          return;
        }
      }

      if (!file || !file.originalFile) {
        toast.error('Please upload a file');
        setIsSubmitting(false);
        return;
      }

      // Check if file is MP4 for HLS conversion
      if (currentTab === 'convert-hls') {
        const fileType = file.originalFile.type;
        if (!fileType.includes('mp4') && !fileType.includes('video')) {
          toast.error('Please upload an MP4 file');
          setIsSubmitting(false);
          return;
        }
      }

      if (currentTab === 'subtitles') {
        const result = await generateSubtitles(file.originalFile, {
          containerName: 'transcripts',
          language,
          segmentDuration,
          timeOffset,
          onProgress: (progress) => {
            setSubtitleProgress(progress);
            if (progress.message) {
              setProgressDetails((prev) => [
                ...prev,
                { time: new Date(), message: progress.message },
              ]);
            }
          },
        });

        setSubtitleResult(result);
      } else if (currentTab === 'convert-hls') {
        const formData = new FormData();
        formData.append('media', file.originalFile);
        formData.append('containerName', 'hls');
        formData.append('language', language);
        formData.append('segmentDuration', segmentDuration);
        formData.append('timeOffset', timeOffset);

        const result = await convertToHLS(formData, {
          onProgress: (progress) => {
            setSubtitleProgress(progress);
            if (progress.message) {
              setProgressDetails((prev) => [
                ...prev,
                { time: new Date(), message: progress.message },
              ]);
            }
          },
        });

        setSubtitleResult(result);
        toast.success('Video converted to HLS successfully');
      } else {
        // Upload media
        await uploadMedia(
          currentTab === 'images' ? 'img' : currentTab === 'videos' ? 'courses' : 'pdf',
          file.originalFile,
          title
        );

        toast.success('Media uploaded successfully');
        setFile(null);
        setTitle('');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error(error.message || 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Save edited SRT content
  const handleSaveSrtContent = async () => {
    try {
      setIsSaving(true);

      // Update the SRT content in Azure Blob Storage
      await updateSrtContent(
        subtitleResult.containerName,
        subtitleResult.srtFileName,
        editedSrtContent
      );

      // Update the local state with the edited content
      setSubtitleResult({
        ...subtitleResult,
        srtContent: editedSrtContent,
      });

      setIsEditing(false);
      toast.success('SRT content updated successfully');
    } catch (error) {
      toast.error('Failed to update SRT content');
    } finally {
      setIsSaving(false);
    }
  };

  // Copy text to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  // Status color helper
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'primary';
    }
  };

  const TABS = [
    {
      value: 'images',
      label: 'Images',
      icon: <Iconify icon="solar:gallery-wide-bold" />,
      accept: { 'image/*': [] },
    },
    {
      value: 'videos',
      label: 'Videos',
      icon: <Iconify icon="solar:videocamera-record-bold" />,
      accept: { 'video/*': [] },
    },
    {
      value: 'pdfs',
      label: 'PDFs',
      icon: <Iconify icon="teenyicons:pdf-outline" />,
      accept: { 'application/pdf': [] },
    },
    {
      value: 'subtitles',
      label: 'Subtitles',
      icon: <Iconify icon="mdi:subtitles-outline" />,
      accept: { 'video/*': [] },
    },
    {
      value: 'convert-hls',
      label: 'Convert HLS',
      icon: <Iconify icon="simple-icons:convertio" />,
      accept: { 'video/*': [] },
    },
  ];

  const renderContent = () => {
    switch (currentTab) {
      case 'images':
        return (
          <ImageView
            file={file}
            title={title}
            setTitle={setTitle}
            handleDrop={handleDrop}
            handleDeleteFile={handleDeleteFile}
            handleSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        );
      case 'videos':
        return (
          <VideoView
            file={file}
            title={title}
            setTitle={setTitle}
            handleDrop={handleDrop}
            handleDeleteFile={handleDeleteFile}
            handleSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        );
      case 'pdfs':
        return (
          <PdfView
            file={file}
            title={title}
            setTitle={setTitle}
            handleDrop={handleDrop}
            handleDeleteFile={handleDeleteFile}
            handleSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        );
      case 'subtitles':
        return (
          <SubtitlesView
            file={file}
            title={title}
            setTitle={setTitle}
            handleDrop={handleDrop}
            handleDeleteFile={handleDeleteFile}
            handleSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            subtitleProgress={subtitleProgress}
            subtitleResult={subtitleResult}
            progressDetails={progressDetails}
            expandedSections={expandedSections}
            toggleSection={toggleSection}
            editedSrtContent={editedSrtContent}
            setEditedSrtContent={setEditedSrtContent}
            isEditing={isEditing}
            setIsEditing={setIsEditing}
            isSaving={isSaving}
            handleSaveSrtContent={handleSaveSrtContent}
            copyToClipboard={copyToClipboard}
            getStatusColor={getStatusColor}
            language={language}
            setLanguage={setLanguage}
            segmentDuration={segmentDuration}
            setSegmentDuration={setSegmentDuration}
            timeOffset={timeOffset}
            setTimeOffset={setTimeOffset}
          />
        );
      case 'convert-hls':
        return (
          <ConvertHLSView
            file={file}
            handleDrop={handleDrop}
            handleDeleteFile={handleDeleteFile}
            handleSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            subtitleProgress={subtitleProgress}
            subtitleResult={subtitleResult}
            progressDetails={progressDetails}
            expandedSections={expandedSections}
            toggleSection={toggleSection}
            copyToClipboard={copyToClipboard}
            getStatusColor={getStatusColor}
            language={language}
            setLanguage={setLanguage}
            segmentDuration={segmentDuration}
            setSegmentDuration={setSegmentDuration}
            timeOffset={timeOffset}
            setTimeOffset={setTimeOffset}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />

      <Box sx={{ mb: 5 }}>
        <Typography
          variant="h4"
          gutterBottom
          sx={{
            fontWeight: 700,
            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
            backgroundClip: 'text',
            textFillColor: 'transparent',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Media Manager
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            fontSize: '1rem',
            opacity: 0.8,
          }}
        >
          Upload and process media files including images, videos, and generate subtitles
        </Typography>
      </Box>

      <Card
        sx={{
          mb: 5,
          borderRadius: 2,
          boxShadow: theme.shadows[2],
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: theme.shadows[4],
          },
        }}
      >
        <Tabs
          value={currentTab}
          onChange={handleChangeTab}
          sx={{
            px: 2,
            bgcolor: alpha(theme.palette.primary.main, 0.05),
            '& .MuiTab-root': {
              minHeight: 64,
              fontWeight: 600,
              '&.Mui-selected': {
                color: 'primary.main',
              },
            },
            '& .MuiTabs-indicator': {
              height: 3,
              borderRadius: '3px 3px 0 0',
            },
          }}
        >
          {TABS.map((tab) => (
            <Tab
              key={tab.value}
              value={tab.value}
              icon={tab.icon}
              label={tab.label}
              iconPosition="start"
            />
          ))}
        </Tabs>

        <Divider />

        <Box sx={{ p: 3 }}>
          <form onSubmit={handleSubmit}>
            <Stack spacing={3}>{renderContent()}</Stack>
          </form>
        </Box>
      </Card>
    </Container>
  );
}
