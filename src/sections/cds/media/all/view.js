/* eslint-disable react-hooks/exhaustive-deps */
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import PropTypes from 'prop-types';

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { Box, Stack, Tab, Tabs } from '@mui/material';
import { useState } from 'react';
import { useSettingsContext } from 'src/components/settings';
import ImageView from './components/ImageView';
import PdfView from './components/PdfView';
import VideoView from './components/VideoView';
import SubtitlesView from './components/SubtitlesView';
import HLSView from './components/HLSView';

export default function AllMediasView() {
  const settings = useSettingsContext();
  const [value, setValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4">All Medias</Typography>
      <Stack
        sx={{
          mt: 5,
          borderRadius: 2,
          flexGrow: 1,
        }}
      >
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
              <Tab label="Images" {...a11yProps(0)} />
              <Tab label="PDFs" {...a11yProps(1)} />
              <Tab label="Videos" {...a11yProps(2)} />
              <Tab label="Subtitles" {...a11yProps(3)} />
              <Tab label="HLS" {...a11yProps(4)} />
            </Tabs>
          </Box>
          <CustomTabPanel value={value} index={0}>
            <ImageView />
          </CustomTabPanel>
          <CustomTabPanel value={value} index={1}>
            <PdfView />
          </CustomTabPanel>
          <CustomTabPanel value={value} index={2}>
            <VideoView />
          </CustomTabPanel>
          <CustomTabPanel value={value} index={3}>
            <SubtitlesView />
          </CustomTabPanel>
          <CustomTabPanel value={value} index={4}>
            <HLSView />
          </CustomTabPanel>
        </Box>
      </Stack>
    </Container>
  );
}
function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}
