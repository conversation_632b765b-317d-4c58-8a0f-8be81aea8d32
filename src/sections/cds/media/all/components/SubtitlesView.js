import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SubtitlesIcon from '@mui/icons-material/Subtitles';
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import CloseIcon from '@mui/icons-material/Close';
import CircularProgress from '@mui/material/CircularProgress';
import SaveIcon from '@mui/icons-material/Save';

import { useMedia } from 'src/apis/useMedia';

export default function SubtitlesView() {
  const { allMedias, getMedias, deleteMedia, updateSrtContent } = useMedia();
  const [hoveredMedia, setHoveredMedia] = useState(null);
  const [viewStyle, setViewStyle] = useState('card');
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editedSrtContent, setEditedSrtContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleClickCardView = () => setViewStyle('card');
  const handleClickListView = () => setViewStyle('list');

  useEffect(() => {
    getMedias('transcripts');
  }, []);

  const handleCopyUrl = async (url) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success('URL copied!', {
        position: 'bottom-right',
        autoClose: 2000,
      });
    } catch (err) {
      toast.error('URL copy failed');
    }
  };

  const handleDeleteClick = (media) => {
    setSelectedMedia(media);
    setDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedMedia) {
      await deleteMedia('transcripts', selectedMedia.name);
      setDeleteConfirm(false);
      setSelectedMedia(null);
      getMedias('transcripts');
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirm(false);
    setSelectedMedia(null);
  };

  const handleEditClick = async (media) => {
    try {
      // SAS token'ı URL'den kaldır
      const baseUrl = media.url.split('?')[0];

      // SRT içeriğini al
      const response = await fetch(baseUrl);
      const srtContent = await response.text();

      setSelectedMedia(media);
      setEditedSrtContent(srtContent);
      setEditDialogOpen(true);
    } catch (error) {
      toast.error('Failed to load SRT content');
    }
  };

  const handleSaveSrtContent = async () => {
    if (!selectedMedia) return;

    try {
      setIsSaving(true);
      await updateSrtContent('transcripts', selectedMedia.name, editedSrtContent);
      toast.success('SRT content updated successfully');
      setEditDialogOpen(false);
      getMedias('transcripts');
    } catch (error) {
      toast.error('Failed to update SRT content');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Box>
      {/* <ListStyle onClickCards={handleClickCardView} onClickList={handleClickListView} /> */}

      {!allMedias || allMedias.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 200,
            backgroundColor: 'background.neutral',
            borderRadius: 1,
          }}
        >
          <Typography variant="subtitle1" color="text.secondary">
            No subtitles have been uploaded yet
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: viewStyle === 'card' ? 'row' : 'column',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          {allMedias &&
            allMedias.map((media) => (
              <Card
                key={media.name}
                onMouseEnter={() => setHoveredMedia(media.name)}
                onMouseLeave={() => setHoveredMedia(null)}
                sx={{
                  ':hover': {
                    backgroundColor: (theme) => theme.palette.action.hover,
                  },
                  width: viewStyle === 'card' ? 220 : '50%',
                  cursor: 'pointer',
                  height: viewStyle === 'card' ? 200 : 80,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: viewStyle === 'card' ? 'center' : 'space-between',
                  flexDirection: viewStyle === 'card' ? 'column' : 'row',
                  position: 'relative',
                }}
              >
                {/* İkonlar */}
                {hoveredMedia === media.name && (
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1,
                      justifyContent: 'flex-end',
                      p: 1,
                      position: 'absolute',
                      ...(viewStyle === 'card'
                        ? {
                            top: 0,
                            right: 0,
                            width: '100%',
                          }
                        : {
                            top: '50%',
                            right: 10,
                            transform: 'translateY(-50%)',
                          }),
                      zIndex: 1,
                    }}
                  >
                    <Tooltip title="Edit SRT">
                      <IconButton
                        onClick={() => handleEditClick(media)}
                        sx={{
                          color: 'primary.main',
                          backgroundColor: 'common.white',
                          '&:hover': { backgroundColor: 'common.white' },
                          padding: '4px',
                        }}
                      >
                        <EditIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="Copy URL">
                      <IconButton
                        onClick={() => handleCopyUrl(media.url)}
                        sx={{
                          color: 'primary.main',
                          backgroundColor: 'common.white',
                          '&:hover': { backgroundColor: 'common.white' },
                          padding: '4px',
                        }}
                      >
                        <ContentCopyIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="Delete">
                      <IconButton
                        onClick={() => handleDeleteClick(media)}
                        sx={{
                          color: 'error.main',
                          backgroundColor: 'common.white',
                          '&:hover': { backgroundColor: 'common.white' },
                          padding: '4px',
                        }}
                      >
                        <DeleteIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )}

                {/* Video İkonu */}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    ml: viewStyle === 'card' ? 0 : 2,
                  }}
                >
                  <SubtitlesIcon sx={{ fontSize: 50, color: 'primary.main' }} />
                </Box>

                {/* Video Adı */}

                <Box sx={{ mr: viewStyle === 'card' ? 0 : 4, mt: viewStyle === 'card' ? 1 : 0 }}>
                  <Typography
                    sx={{
                      textAlign: viewStyle === 'card' ? 'center' : 'left',
                    }}
                    variant="subtitle2"
                  >
                    {media.name}
                  </Typography>
                </Box>
              </Card>
            ))}
        </Box>
      )}

      <Dialog open={deleteConfirm} onClose={handleCancelDelete}>
        <DialogTitle>Delete Subtitle</DialogTitle>
        <DialogContent />
        <DialogActions>
          <Button onClick={handleCancelDelete} color="inherit">
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            minHeight: '80vh',
            maxHeight: '90vh',
          },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: '1px solid',
            borderColor: 'divider',
            pb: 2,
          }}
        >
          <Typography variant="h6">Edit SRT Content</Typography>
          <IconButton onClick={() => setEditDialogOpen(false)} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              File: {selectedMedia?.name}
            </Typography>
          </Box>
          <TextField
            multiline
            fullWidth
            rows={25}
            value={editedSrtContent}
            onChange={(e) => setEditedSrtContent(e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': {
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                lineHeight: 1.5,
              },
            }}
            InputProps={{
              sx: {
                p: 2,
                bgcolor: 'background.neutral',
              },
            }}
          />
        </DialogContent>
        <DialogActions
          sx={{
            px: 3,
            py: 2,
            borderTop: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Button onClick={() => setEditDialogOpen(false)} color="inherit" variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleSaveSrtContent}
            color="primary"
            variant="contained"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
