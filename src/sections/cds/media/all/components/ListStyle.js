import { Button, ButtonGroup } from '@mui/material';
import PropTypes from 'prop-types';
import Iconify from 'src/components/iconify';

function ListStyle({ onClickCards, onClickList }) {
  return (
    <ButtonGroup variant="outlined" sx={{ position: 'absolute', right: 60, zIndex: 9 }}>
      <Button onClick={onClickCards}>
        <Iconify icon="material-symbols:cards" />
      </Button>
      <Button onClick={onClickList}>
        <Iconify icon="material-symbols:list" />
      </Button>
    </ButtonGroup>
  );
}

ListStyle.propTypes = {
  onClickCards: PropTypes.func,
  onClickList: PropTypes.func,
};

export default ListStyle;
