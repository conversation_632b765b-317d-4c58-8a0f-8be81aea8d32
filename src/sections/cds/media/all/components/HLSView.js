import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SubtitlesIcon from '@mui/icons-material/Subtitles';
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Tooltip,
  Typography,
  CircularProgress,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import CloseIcon from '@mui/icons-material/Close';
import SaveIcon from '@mui/icons-material/Save';

import { useMedia } from 'src/apis/useMedia';

export default function HLSView() {
  const { allMedias, getMedias, deleteMedia, updateSrtContent, loading } = useMedia();
  const [hoveredMedia, setHoveredMedia] = useState(null);
  const [viewStyle, setViewStyle] = useState('card');
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editedSrtContent, setEditedSrtContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleClickCardView = () => setViewStyle('card');
  const handleClickListView = () => setViewStyle('list');

  useEffect(() => {
    getMedias('hls');
  }, []);

  const handleCopyUrl = async (url) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success('URL copied!', {
        position: 'bottom-right',
        autoClose: 2000,
      });
    } catch (err) {
      toast.error('URL copy failed');
    }
  };

  const handleDeleteClick = (media) => {
    setSelectedMedia(media);
    setDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedMedia) {
      await deleteMedia('transcripts', selectedMedia.name);
      setDeleteConfirm(false);
      setSelectedMedia(null);
      getMedias('transcripts');
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirm(false);
    setSelectedMedia(null);
  };

  const handleEditClick = async (media) => {
    try {
      // SAS token'ı URL'den kaldır
      const baseUrl = media.url.split('?')[0];

      // SRT içeriğini al
      const response = await fetch(baseUrl);
      const srtContent = await response.text();

      setSelectedMedia(media);
      setEditedSrtContent(srtContent);
      setEditDialogOpen(true);
    } catch (error) {
      toast.error('Failed to load SRT content');
    }
  };

  const handleSaveSrtContent = async () => {
    if (!selectedMedia) return;

    try {
      setIsSaving(true);
      await updateSrtContent('transcripts', selectedMedia.name, editedSrtContent);
      toast.success('SRT content updated successfully');
      setEditDialogOpen(false);
      getMedias('transcripts');
    } catch (error) {
      toast.error('Failed to update SRT content');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Box>
      {/* <ListStyle onClickCards={handleClickCardView} onClickList={handleClickListView} /> */}

      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 200,
            backgroundColor: 'background.neutral',
            borderRadius: 1,
          }}
        >
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress size={40} />
            <Typography variant="subtitle1" color="text.secondary" sx={{ mt: 2 }}>
              Loading HLS files...
            </Typography>
          </Box>
        </Box>
      ) : !allMedias || allMedias.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 200,
            backgroundColor: 'background.neutral',
            borderRadius: 1,
          }}
        >
          <Typography variant="subtitle1" color="text.secondary">
            No HLS files have been uploaded yet
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: viewStyle === 'card' ? 'row' : 'column',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          {allMedias &&
            allMedias.map((media) => (
              <Card
                key={media.name}
                onMouseEnter={() => setHoveredMedia(media.name)}
                onMouseLeave={() => setHoveredMedia(null)}
                sx={{
                  ':hover': {
                    backgroundColor: (theme) => theme.palette.action.hover,
                  },
                  width: viewStyle === 'card' ? 220 : '50%',
                  cursor: 'pointer',
                  height: viewStyle === 'card' ? 200 : 80,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: viewStyle === 'card' ? 'center' : 'space-between',
                  flexDirection: viewStyle === 'card' ? 'column' : 'row',
                  position: 'relative',
                }}
              >
                {/* İkonlar */}
                {hoveredMedia === media.name && (
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1,
                      justifyContent: 'flex-end',
                      p: 1,
                      position: 'absolute',
                      ...(viewStyle === 'card'
                        ? {
                            top: 0,
                            right: 0,
                            width: '100%',
                          }
                        : {
                            top: '50%',
                            right: 10,
                            transform: 'translateY(-50%)',
                          }),
                      zIndex: 1,
                    }}
                  >
                    <Tooltip title="Copy URL">
                      <IconButton
                        onClick={() => handleCopyUrl(media.url)}
                        sx={{
                          color: 'primary.main',
                          backgroundColor: 'common.white',
                          '&:hover': { backgroundColor: 'common.white' },
                          padding: '4px',
                        }}
                      >
                        <ContentCopyIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )}

                {/* Video İkonu */}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    ml: viewStyle === 'card' ? 0 : 2,
                  }}
                >
                  <SubtitlesIcon sx={{ fontSize: 50, color: 'primary.main' }} />
                </Box>

                {/* Video Adı */}

                <Box sx={{ mr: viewStyle === 'card' ? 0 : 4, mt: viewStyle === 'card' ? 1 : 0 }}>
                  <Typography
                    sx={{
                      textAlign: viewStyle === 'card' ? 'center' : 'left',
                    }}
                    variant="subtitle2"
                  >
                    {media.name.split('/')[1]}
                  </Typography>
                </Box>
              </Card>
            ))}
        </Box>
      )}
    </Box>
  );
}
