import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { useMedia } from 'src/apis/useMedia';

export default function VideoView() {
  const { allMedias, getMedias, deleteMedia } = useMedia();
  const [hoveredMedia, setHoveredMedia] = useState(null);
  const [viewStyle, setViewStyle] = useState('card');
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);

  const handleClickCardView = () => setViewStyle('card');
  const handleClickListView = () => setViewStyle('list');

  useEffect(() => {
    getMedias('courses');
  }, []);

  const handleCopyUrl = async (url) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success('URL copied!', {
        position: 'bottom-right',
        autoClose: 2000,
      });
    } catch (err) {
      toast.error('URL copy failed');
    }
  };

  const handleDeleteClick = (media) => {
    setSelectedMedia(media);
    setDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedMedia) {
      await deleteMedia('courses', selectedMedia.name);
      setDeleteConfirm(false);
      setSelectedMedia(null);
      getMedias('courses');
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirm(false);
    setSelectedMedia(null);
  };

  return (
    <Box>
      {/* <ListStyle onClickCards={handleClickCardView} onClickList={handleClickListView} /> */}

      {!allMedias || allMedias.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 200,
            backgroundColor: 'background.neutral',
            borderRadius: 1,
          }}
        >
          <Typography variant="subtitle1" color="text.secondary">
            No videos have been uploaded yet
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: viewStyle === 'card' ? 'row' : 'column',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          {allMedias &&
            allMedias.map((media) => (
              <Card
                key={media.name}
                onMouseEnter={() => setHoveredMedia(media.name)}
                onMouseLeave={() => setHoveredMedia(null)}
                sx={{
                  ':hover': {
                    backgroundColor: (theme) => theme.palette.action.hover,
                  },
                  width: viewStyle === 'card' ? 220 : '50%',
                  cursor: 'pointer',
                  height: viewStyle === 'card' ? 200 : 80,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: viewStyle === 'card' ? 'center' : 'space-between',
                  flexDirection: viewStyle === 'card' ? 'column' : 'row',
                  position: 'relative',
                }}
              >
                {/* İkonlar */}
                {hoveredMedia === media.name && (
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1,
                      justifyContent: 'flex-end',
                      p: 1,
                      position: 'absolute',
                      ...(viewStyle === 'card'
                        ? {
                            top: 0,
                            right: 0,
                            width: '100%',
                          }
                        : {
                            top: '50%',
                            right: 10,
                            transform: 'translateY(-50%)',
                          }),
                      zIndex: 1,
                    }}
                  >
                    <Tooltip title="URL'i Kopyala">
                      <IconButton
                        onClick={() => handleCopyUrl(media.url)}
                        sx={{
                          color: 'primary.main',
                          backgroundColor: 'common.white',
                          '&:hover': { backgroundColor: 'common.white' },
                          padding: '4px',
                        }}
                      >
                        <ContentCopyIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="Delete">
                      <IconButton
                        onClick={() => handleDeleteClick(media)}
                        sx={{
                          color: 'error.main',
                          backgroundColor: 'common.white',
                          '&:hover': { backgroundColor: 'common.white' },
                          padding: '4px',
                        }}
                      >
                        <DeleteIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )}

                {/* Video İkonu */}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    ml: viewStyle === 'card' ? 0 : 2,
                  }}
                >
                  <PlayCircleOutlineIcon sx={{ fontSize: 50, color: 'primary.main' }} />
                </Box>

                {/* Video Adı */}

                <Box sx={{ mr: viewStyle === 'card' ? 0 : 4, mt: viewStyle === 'card' ? 1 : 0 }}>
                  <Typography
                    sx={{
                      textAlign: viewStyle === 'card' ? 'center' : 'left',
                    }}
                    variant="subtitle2"
                  >
                    {media.name}
                  </Typography>
                </Box>
              </Card>
            ))}
        </Box>
      )}

      <Dialog open={deleteConfirm} onClose={handleCancelDelete}>
        <DialogTitle>Delete Video</DialogTitle>
        <DialogContent />
        <DialogActions>
          <Button onClick={handleCancelDelete} color="inherit">
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
