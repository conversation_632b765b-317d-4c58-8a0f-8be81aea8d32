import { alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import React, { useEffect, useState } from 'react';
import { useSettingsContext } from 'src/components/settings';
import {
  Button,
  Card,
  Grid,
  Stack,
  Pagination,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Tooltip,
} from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import { useForms } from 'src/apis';
import EditFormModal from '../components/EditFormModal';
import PreviewFormModal from '../components/PreviewFormModal';
import { Link } from 'react-router-dom';
import { paths } from 'src/routes/paths';

import { Translate as TranslateIcon } from '@mui/icons-material';

export default function AllFormsView() {
  const settings = useSettingsContext();
  const {
    getForms,
    updateForm,
    allForms,
    loading,
    getFormTranslation,
    translateForm,
    updateFormTranslation,
  } = useForms();
  const [open, setOpen] = useState(false);
  const [selectedForm, setSelectedForm] = useState(null);
  const [selectedLanguage, setSelectedLanguage] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewForm, setPreviewForm] = useState(null);
  const [page, setPage] = useState(1);
  const [limit] = useState(12);
  const [translationOpen, setTranslationOpen] = useState(false);
  const [formToTranslate, setFormToTranslate] = useState(null);
  const [targetLanguage, setTargetLanguage] = useState('en');
  const [translating, setTranslating] = useState(false);
  const [loadingTranslation, setLoadingTranslation] = useState(false);
  const authToken = localStorage.getItem('accessToken');

  const languageOptions = [
    { code: 'en', label: 'English' },
    { code: 'es', label: 'Spanish' },
    { code: 'fr', label: 'French' },
    { code: 'de', label: 'German' },
    { code: 'it', label: 'Italian' },
    { code: 'pt', label: 'Portuguese' },
    { code: 'ru', label: 'Russian' },
    { code: 'zh-Hans', label: 'Chinese (Simplified)' },
    { code: 'ja', label: 'Japanese' },
    { code: 'ko', label: 'Korean' },
    { code: 'ar', label: 'Arabic' },
    { code: 'tr', label: 'Turkish' },
  ];

  // Helper function to get language name from code
  const getLanguageName = (code) => {
    const language = languageOptions.find((lang) => lang.code === code);
    return language ? language.label : code;
  };

  useEffect(() => {
    getForms(page, limit);
  }, [page, limit, getForms]);

  const handleOpen = (form, language = null) => {
    if (language) {
      setLoadingTranslation(true);

      getFormTranslation(form._id, language)
        .then((response) => {
          if (response.success) {
            const originalForm = { ...form };

            const translatedForm = {
              ...originalForm,
              translatedContent: response.data,
              selectedLanguage: language,
              title: response.data.title || originalForm.title,
              description: response.data.description || originalForm.description,
              fields: originalForm.fields.map((field, idx) => {
                const translatedField = response.data.fields && response.data.fields[idx];
                if (translatedField) {
                  return {
                    ...field,
                    label: translatedField.label || field.label,
                    description: translatedField.description || field.description,
                    options:
                      field.options?.map((option, optIdx) => {
                        const translatedOption =
                          translatedField.options && translatedField.options[optIdx];
                        if (translatedOption) {
                          return {
                            ...option,
                            label: translatedOption.label || option.label,
                          };
                        }
                        return option;
                      }) || [],
                  };
                }
                return field;
              }),
              topics:
                originalForm.topics?.map((topic, idx) => {
                  const translatedTopic = response.data.topics && response.data.topics[idx];
                  if (translatedTopic) {
                    return {
                      ...topic,
                      title: translatedTopic.title || topic.title,
                      description: translatedTopic.description || topic.description,
                      fields:
                        topic.fields?.map((field, fieldIdx) => {
                          const translatedField =
                            translatedTopic.fields && translatedTopic.fields[fieldIdx];
                          if (translatedField) {
                            return {
                              ...field,
                              label: translatedField.label || field.label,
                              description: translatedField.description || field.description,
                              options:
                                field.options?.map((option, optIdx) => {
                                  const translatedOption =
                                    translatedField.options && translatedField.options[optIdx];
                                  if (translatedOption) {
                                    return {
                                      ...option,
                                      label: translatedOption.label || option.label,
                                    };
                                  }
                                  return option;
                                }) || [],
                            };
                          }
                          return field;
                        }) || [],
                    };
                  }
                  return topic;
                }) || [],
            };

            setSelectedForm(translatedForm);
            setSelectedLanguage(language);
            setOpen(true);
          } else {
            toast.error(
              `Failed to load ${getLanguageName(language)} translation: ${response.error}`
            );
          }
        })
        .catch((error) => {
          console.error('Error fetching translation:', error);
          toast.error(`Error loading translation: ${error.message}`);
        })
        .finally(() => {
          setLoadingTranslation(false);
        });
    } else {
      setSelectedForm(form);
      setSelectedLanguage(null);
      setOpen(true);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedForm(null);
    setSelectedLanguage(null);
  };

  const handleFormUpdate = () => {
    if (selectedLanguage && selectedForm) {
      const translationData = {
        title: selectedForm.title,
        description: selectedForm.description,
        fields: selectedForm.fields.map((field) => ({
          label: field.label,
          description: field.description,
          options:
            field.options?.map((option) => ({
              label: option.label,
              value: option.value,
            })) || [],
        })),
        topics:
          selectedForm.topics?.map((topic) => ({
            title: topic.title,
            description: topic.description,
            fields:
              topic.fields?.map((field) => ({
                label: field.label,
                description: field.description,
                options:
                  field.options?.map((option) => ({
                    label: option.label,
                    value: option.value,
                  })) || [],
              })) || [],
          })) || [],
      };

      updateFormTranslation(selectedForm._id, selectedLanguage, translationData)
        .then((result) => {
          if (result.success) {
            toast.success(
              `Translation in ${getLanguageName(selectedLanguage)} updated successfully`
            );
            getForms(page, limit);
            handleClose();
          } else {
            toast.error(`Failed to update translation: ${result.error}`);
          }
        })
        .catch((error) => {
          console.error('Error updating translation:', error);
          toast.error(`Error updating translation: ${error.message}`);
        });
    } else {
      updateForm(selectedForm._id, selectedForm)
        .then((response) => {
          getForms(page, limit);
          handleClose();
        })
        .catch((error) => {
          console.error('Form updating error:', error);
        });
    }
  };

  const handleFormChange = (updatedForm) => {
    setSelectedForm(updatedForm);
  };

  const handlePreviewOpen = (form) => {
    setPreviewForm(form);
    setPreviewOpen(true);
  };

  const handlePreviewClose = () => {
    setPreviewOpen(false);
    setPreviewForm(null);
  };

  const handlePageChange = (event, newPage) => {
    if (newPage !== page) {
      setPage(newPage);
    }
  };

  const handleTranslateOpen = (form) => {
    setFormToTranslate(form);
    setTranslationOpen(true);
  };

  const handleTranslateClose = () => {
    setTranslationOpen(false);
    setFormToTranslate(null);
  };

  const handleTranslate = async () => {
    if (!formToTranslate || !targetLanguage) {
      toast.error('Please select a form and target language');
      return;
    }

    // İngilizce çeviri kontrolü
    const hasEnTranslation = formToTranslate.translations && 'en' in formToTranslate.translations;
    if (!hasEnTranslation && targetLanguage !== 'en') {
      toast.error(
        'Please translate to English first. English translation is required as a reference for other languages.'
      );
      return;
    }

    setTranslating(true);

    try {
      // Burada doğrudan axios ile API çağrısı yerine, useForms hook'undan translateForm fonksiyonunu kullanıyoruz
      const result = await translateForm(
        formToTranslate,
        targetLanguage,
        'en', // Kaynak dil olarak her zaman İngilizce kullan
        true // veritabanına kaydet
      );

      if (result.success) {
        // Refresh forms list to get updated translations
        await getForms(page, limit);

        toast.success(`Form translated to ${getLanguageName(targetLanguage)} successfully`);
      } else {
        toast.error('Translation failed: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Translation error:', error);
      toast.error(
        'Translation failed: ' + (error.response?.data?.message || error.message || 'Unknown error')
      );
    } finally {
      setTranslating(false);
      handleTranslateClose();
    }
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4"> All Forms </Typography>

      <Stack
        sx={{
          mt: 5,
          borderRadius: 2,
          flexGrow: 1,
        }}
      >
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 5 }}>
            <CircularProgress />
            <Typography variant="body1" sx={{ ml: 2 }}>
              Forms loading...
            </Typography>
          </Box>
        ) : (
          <>
            <Box
              sx={{
                borderColor: 'divider',
                display: 'flex',
                gap: 2,
              }}
            >
              <Grid container spacing={2}>
                {allForms && allForms.forms && allForms.forms.length > 0 ? (
                  allForms.forms.map((form) => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={form._id}>
                      <Card
                        sx={{
                          bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04),
                          display: 'flex',
                          flexDirection: 'column',
                          height: '100%',
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            p: 1,
                          }}
                        >
                          <Typography variant="h6" sx={{ p: 1 }}>
                            {form.title}
                          </Typography>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              color:
                                form.status === 'active'
                                  ? 'green'
                                  : form.status === 'draft'
                                    ? 'orange'
                                    : 'red',
                              borderRadius: 1,
                              px: 2,
                              py: 1,
                            }}
                          >
                            {form.status}
                          </Typography>
                        </Box>

                        <Typography sx={{ pl: 2, pr: 2, maxWidth: '20rem' }} variant="body1">
                          {form.description}
                        </Typography>

                        {/* Display available translations */}
                        {form.translations && Object.keys(form.translations).length > 0 && (
                          <Box sx={{ p: 2, pt: 0 }}>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{ mb: 1, display: 'block' }}
                            >
                              Available Translations:
                            </Typography>
                            <Stack direction="row" spacing={0.5} flexWrap="wrap" gap={0.5}>
                              {Object.keys(form.translations).map((langCode) => (
                                <Tooltip
                                  key={langCode}
                                  title={`Edit ${getLanguageName(langCode)} translation`}
                                >
                                  <Chip
                                    label={langCode.toUpperCase()}
                                    size="small"
                                    color="primary"
                                    variant="outlined"
                                    onClick={() => {
                                      window.location.href = `${paths.cds.formBuilder.edit(form._id)}?lang=${langCode}`;
                                    }}
                                    sx={{
                                      '&:hover': {
                                        cursor: 'pointer',
                                        backgroundColor: (theme) =>
                                          alpha(theme.palette.primary.main, 0.1),
                                      },
                                    }}
                                  />
                                </Tooltip>
                              ))}
                            </Stack>
                          </Box>
                        )}

                        <Stack
                          direction="row"
                          justifyContent="space-between"
                          alignItems="center"
                          sx={{
                            p: 2,
                            mt: 'auto',
                            flexWrap: 'wrap',
                            gap: 1,
                          }}
                        >
                          <Button
                            size="small"
                            color="warning"
                            variant="contained"
                            onClick={() => handleTranslateOpen(form)}
                            startIcon={<TranslateIcon />}
                          >
                            Translate
                          </Button>
                        </Stack>
                      </Card>
                    </Grid>
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        p: 5,
                        borderRadius: 1,
                        bgcolor: 'background.neutral',
                        textAlign: 'center',
                      }}
                    >
                      <Typography variant="h6">No forms found</Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary', mt: 1 }}>
                        Create a new form by clicking the button above.
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Box>

            {allForms && allForms.totalPages > 1 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 5 }}>
                <Pagination
                  count={allForms.totalPages}
                  page={page}
                  onChange={handlePageChange}
                  variant="outlined"
                  color="primary"
                />
              </Box>
            )}
          </>
        )}
      </Stack>

      {/* Edit Form Modal */}
      <EditFormModal
        open={open}
        onClose={handleClose}
        onSave={handleFormUpdate}
        form={selectedForm}
        onChange={handleFormChange}
        isTranslation={!!selectedLanguage}
        translationLanguage={selectedLanguage}
      />

      {/* Preview Form Modal */}
      <PreviewFormModal open={previewOpen} onClose={handlePreviewClose} form={previewForm} />

      {/* Translation Dialog */}
      <Dialog
        open={translationOpen}
        onClose={handleTranslateClose}
        aria-labelledby="translation-dialog-title"
      >
        <DialogTitle id="translation-dialog-title">Translate Form</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 3 }}>
            This will translate all text fields, labels, and options of the form to the selected
            language.
          </Typography>
          <FormControl fullWidth>
            <InputLabel id="target-language-label">Target Language</InputLabel>
            <Select
              labelId="target-language-label"
              id="target-language"
              value={targetLanguage}
              label="Target Language"
              onChange={(e) => setTargetLanguage(e.target.value)}
            >
              {languageOptions.map((option) => (
                <MenuItem key={option.code} value={option.code}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {formToTranslate &&
            formToTranslate.translations &&
            formToTranslate.translations[targetLanguage] && (
              <Typography variant="body2" color="warning.main" sx={{ mt: 2 }}>
                This form already has a translation in this language. It will be updated.
              </Typography>
            )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleTranslateClose} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleTranslate}
            variant="contained"
            color="primary"
            disabled={translating}
          >
            {translating ? 'Translating...' : 'Translate'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
