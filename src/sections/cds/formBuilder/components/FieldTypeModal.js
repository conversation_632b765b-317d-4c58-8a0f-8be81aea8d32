import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  Tab,
  Tabs,
  Box,
  Grid,
  Button,
  IconButton,
  Typography,
  InputAdornment,
} from '@mui/material';
import { useState } from 'react';
import Iconify from 'src/components/iconify';

const FIELD_CATEGORIES = [
  { key: 'all', label: 'All' },
  { key: 'basic', label: 'Basic' },
  { key: 'content', label: 'Content' },
  { key: 'choice', label: 'Choice' },
  { key: 'advanced', label: 'Advanced' },
];

const FIELD_TYPES = {
  all: [
    {
      type: 'text',
      icon: 'mdi:alphabetical',
      label: 'Text',
      description: 'A basic text input for single line text.',
    },
    {
      type: 'number',
      icon: 'mdi:numeric',
      label: 'Number',
      description: 'For numeric values with validation.',
    },
    {
      type: 'email',
      icon: 'mdi:at',
      label: 'Email',
      description: 'Email input with validation.',
    },
    {
      type: 'textarea',
      icon: 'mdi:text-box-outline',
      label: 'Text Area',
      description: 'For longer text content and multi-line input.',
    },
    {
      type: 'select',
      icon: 'mdi:form-dropdown',
      label: 'Select',
      description: 'Dropdown selection from a list of choices.',
    },
    {
      type: 'radio',
      icon: 'mdi:radiobox-marked',
      label: 'Radio',
      description: 'Single choice from multiple options.',
    },
    {
      type: 'checkbox',
      icon: 'mdi:checkbox-marked-outline',
      label: 'Checkbox',
      description: 'Multiple choice selection.',
    },
    {
      type: 'feedback',
      icon: 'mdi:star',
      label: 'Feedback',
      description: 'Rating from 0 to 10 for feedback collection.',
    },
    {
      type: 'group',
      icon: 'mdi:folder-outline',
      label: 'Group',
      description: 'Group multiple fields together.',
    },
    {
      type: 'repeater',
      icon: 'mdi:infinity',
      label: 'Repeater',
      description: 'Create repeatable groups of fields.',
    },
    {
      type:'file',
      icon: 'mdi:file-outline',
      label: 'File',
      description: 'Upload files for submission.',
    }
  ],
  basic: [
    {
      type: 'text',
      icon: 'mdi:alphabetical',
      label: 'Text',
      description: 'A basic text input for single line text.',
    },
    {
      type: 'number',
      icon: 'mdi:numeric',
      label: 'Number',
      description: 'For numeric values with validation.',
    },
    {
      type: 'email',
      icon: 'mdi:at',
      label: 'Email',
      description: 'Email input with validation.',
    },
    {
      type: 'feedback',
      icon: 'mdi:star',
      label: 'Feedback',
      description: 'Rating from 0 to 10 for feedback collection.',
    },
  ],
  content: [
    {
      type: 'textarea',
      icon: 'mdi:text-box-outline',
      label: 'Text Area',
      description: 'For longer text content and multi-line input.',
    },
    {
      type: 'file',
      icon: 'mdi:file-outline',
      label: 'File',
      description: 'Upload files for submission.',
    },
    
  ],
  choice: [
    {
      type: 'select',
      icon: 'mdi:form-dropdown',
      label: 'Select',
      description: 'Dropdown selection from a list of choices.',
    },
    {
      type: 'radio',
      icon: 'mdi:radiobox-marked',
      label: 'Radio',
      description: 'Single choice from multiple options.',
    },
    {
      type: 'checkbox',
      icon: 'mdi:checkbox-marked-outline',
      label: 'Checkbox',
      description: 'Multiple choice selection.',
    },
    {
      type: 'feedback',
      icon: 'mdi:star',
      label: 'Feedback',
      description: 'Rating from 0 to 10 for feedback collection.',
    },
  ],
  advanced: [
    {
      type: 'group',
      icon: 'mdi:folder-outline',
      label: 'Group',
      description: 'Group multiple fields together.',
    },
    {
      type: 'repeater',
      icon: 'mdi:infinity',
      label: 'Repeater',
      description: 'Create repeatable groups of fields.',
    },
  ],
};

export default function FieldTypeModal({ open, onClose, onSelect }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [selectedField, setSelectedField] = useState(null);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleFieldClick = (field) => {
    setSelectedField(field);
  };

  const handleConfirm = () => {
    if (selectedField) {
      onSelect(selectedField);
      onClose();
    }
  };

  const filteredFields = searchQuery
    ? Object.values(FIELD_TYPES)
        .flat()
        .filter(
          (field) =>
            field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
            field.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
    : FIELD_TYPES[activeTab] || [];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        Select Field Type
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        >
          <Iconify icon="mdi:close" />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pb: 2 }}>
        <Box>
          <TextField
            fullWidth
            placeholder="Search fields..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:magnify" />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {!searchQuery && (
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ mb: 2, borderBottom: 1, borderColor: 'divider' }}
          >
            {FIELD_CATEGORIES.map((category) => (
              <Tab key={category.key} label={category.label} value={category.key} />
            ))}
          </Tabs>
        )}

        <Grid container spacing={2}>
          {filteredFields.map((field) => (
            <Grid item xs={12} sm={6} md={4} key={field.type}>
              <Box
                onClick={() => handleFieldClick(field)}
                sx={{
                  p: 2,
                  border: 1,
                  borderRadius: 1,
                  borderColor: selectedField?.type === field.type ? 'primary.main' : 'divider',
                  bgcolor:
                    selectedField?.type === field.type ? 'primary.lighter' : 'background.paper',
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: 'primary.lighter',
                  },
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Iconify icon={field.icon} width={24} height={24} />
                  <Typography variant="subtitle1" sx={{ ml: 1 }}>
                    {field.label}
                  </Typography>
                  {field.isPro && (
                    <Box
                      sx={{
                        ml: 1,
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        bgcolor: 'primary.main',
                        color: 'primary.contrastText',
                      }}
                    >
                      PRO
                    </Box>
                  )}
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {field.description}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
          <Button onClick={onClose}>Cancel</Button>
          <Button variant="contained" onClick={handleConfirm} disabled={!selectedField}>
            Select Field
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
}

FieldTypeModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSelect: PropTypes.func.isRequired,
};
