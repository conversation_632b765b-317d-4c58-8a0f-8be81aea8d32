import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Modal,
  Button,
  TextField,
  Stack,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  IconButton,
  Divider,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Alert,
} from '@mui/material';
import { Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import Iconify from 'src/components/iconify';

export default function EditFormModal({
  open,
  onClose,
  form, // Renamed from selectedForm
  onSave, // Renamed from onUpdate
  onChange,
  isTranslation = false,
  translationLanguage = null,
}) {
  const [expandedTopic, setExpandedTopic] = useState(null);
  const selectedForm = form; // For backward compatibility

  const formHasTopics =
    selectedForm && Array.isArray(selectedForm.topics) && selectedForm.topics.length > 0;

  // Get language display name for UI
  const getLanguageDisplayName = (code) => {
    const languageMap = {
      en: 'English',
      es: 'Spanish',
      fr: 'French',
      de: 'German',
      it: 'Italian',
      pt: 'Portuguese',
      ru: 'Russian',
      'zh-Hans': 'Chinese (Simplified)',
      ja: 'Japanese',
      ko: 'Korean',
      ar: 'Arabic',
      tr: 'Turkish',
    };

    return languageMap[code] || code.toUpperCase();
  };

  const handleFieldChange = (fieldId, updatedValues, topicId = null) => {
    if (topicId) {
      const updatedForm = {
        ...selectedForm,
        topics: selectedForm.topics.map((topic) =>
          topic._id === topicId
            ? {
                ...topic,
                fields: topic.fields.map((field) =>
                  field._id === fieldId ? { ...field, ...updatedValues } : field
                ),
              }
            : topic
        ),
      };
      onChange(updatedForm);
    } else {
      const updatedForm = {
        ...selectedForm,
        fields: selectedForm.fields.map((field) =>
          field._id === fieldId ? { ...field, ...updatedValues } : field
        ),
      };
      onChange(updatedForm);
    }
  };

  const handleStatusChange = (newStatus) => {
    const updatedForm = {
      ...selectedForm,
      status: newStatus,
    };
    onChange(updatedForm);
  };

  const handleTopicChange = (topicId, updatedValues) => {
    const updatedForm = {
      ...selectedForm,
      topics: selectedForm.topics.map((topic) =>
        topic._id === topicId ? { ...topic, ...updatedValues } : topic
      ),
    };
    onChange(updatedForm);
  };

  const handleAddTopic = () => {
    const newTopic = {
      _id: `temp_${Date.now()}`,
      title: `Topic ${selectedForm.topics ? selectedForm.topics.length + 1 : 1}`,
      description: '',
      fields: [],
    };

    const updatedForm = {
      ...selectedForm,
      topics: [...(selectedForm.topics || []), newTopic],
    };
    onChange(updatedForm);
    setExpandedTopic(newTopic._id);
  };

  const handleRemoveTopic = (topicId) => {
    const updatedForm = {
      ...selectedForm,
      topics: selectedForm.topics.filter((topic) => topic._id !== topicId),
    };
    onChange(updatedForm);
  };

  const handleAddField = (topicId = null, fieldType = 'text') => {
    const newField = {
      _id: `temp_${Date.now()}`,
      name: `field_${Date.now()}`,
      label: 'New Field',
      type: fieldType,
      required: false,
      description: '',
      options: [],
      validation: {
        min: fieldType === 'feedback' ? 0 : null,
        max: fieldType === 'feedback' ? 10 : null,
        pattern: '',
        message: '',
      },
      conditional_logic: {
        enabled: false,
        rules: [[{ field: '', operator: 'has_any_value', value: '' }]],
      },
    };

    if (topicId) {
      const updatedForm = {
        ...selectedForm,
        topics: selectedForm.topics.map((topic) =>
          topic._id === topicId
            ? {
                ...topic,
                fields: [...topic.fields, newField],
              }
            : topic
        ),
      };
      onChange(updatedForm);
    } else {
      const updatedForm = {
        ...selectedForm,
        fields: [...selectedForm.fields, newField],
      };
      onChange(updatedForm);
    }
  };

  const handleRemoveField = (fieldId, topicId = null) => {
    if (topicId) {
      const updatedForm = {
        ...selectedForm,
        topics: selectedForm.topics.map((topic) =>
          topic._id === topicId
            ? {
                ...topic,
                fields: topic.fields.filter((field) => field._id !== fieldId),
              }
            : topic
        ),
      };
      onChange(updatedForm);
    } else {
      const updatedForm = {
        ...selectedForm,
        fields: selectedForm.fields.filter((field) => field._id !== fieldId),
      };
      onChange(updatedForm);
    }
  };

  const handleAccordionChange = (topicId) => (event, isExpanded) => {
    setExpandedTopic(isExpanded ? topicId : null);
  };

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="edit-form-modal">
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '80%',
          maxHeight: '90vh',
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 4,
          borderRadius: 2,
          overflow: 'auto',
        }}
      >
        {selectedForm && (
          <>
            <Typography variant="h6" component="h2" sx={{ mb: 3 }}>
              {isTranslation
                ? `Edit ${getLanguageDisplayName(translationLanguage)} Translation`
                : 'Edit Form'}
            </Typography>

            {isTranslation && (
              <Alert severity="info" sx={{ mb: 3 }}>
                You are editing the {getLanguageDisplayName(translationLanguage)} translation of
                this form. Only text content like title, descriptions, labels and options will be
                saved to the translation.
              </Alert>
            )}

            <Stack spacing={3}>
              <TextField
                fullWidth
                label="Form Title"
                value={selectedForm.title}
                onChange={(e) => onChange({ ...selectedForm, title: e.target.value })}
              />

              <TextField
                fullWidth
                multiline
                rows={3}
                label="Form Description"
                value={selectedForm.description}
                onChange={(e) => onChange({ ...selectedForm, description: e.target.value })}
              />

              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={selectedForm.status}
                  label="Status"
                  onChange={(e) => handleStatusChange(e.target.value)}
                  disabled={isTranslation} // Status can't be changed in translation mode
                >
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>

              {/* Form Topics */}
              <Box sx={{ mt: 3 }}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 2,
                  }}
                >
                  <Typography variant="h6">Form Topics</Typography>
                  <Button
                    variant="outlined"
                    startIcon={<Iconify icon="mdi:plus" />}
                    onClick={handleAddTopic}
                    disabled={isTranslation} // Disable adding topics in translation mode
                  >
                    Add Topic
                  </Button>
                </Box>

                {formHasTopics ? (
                  selectedForm.topics.map((topic) => (
                    <Accordion
                      key={topic._id}
                      expanded={expandedTopic === topic._id}
                      onChange={handleAccordionChange(topic._id)}
                    >
                      <AccordionSummary
                        expandIcon={<Iconify icon="material-symbols:expand-more" />}
                        aria-controls="panel1a-content"
                        id="panel1a-header"
                        sx={{ px: 2 }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%',
                          }}
                        >
                          <Typography variant="subtitle1">{topic.title}</Typography>
                          {!isTranslation && ( // Only show remove button when not in translation mode
                            <IconButton
                              size="small"
                              color="error"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveTopic(topic._id);
                              }}
                              sx={{ ml: 2 }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          )}
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Box sx={{ mb: 2 }}>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              mb: 2,
                            }}
                          >
                            <TextField
                              fullWidth
                              label="Topic Title"
                              value={topic.title}
                              onChange={(e) =>
                                handleTopicChange(topic._id, { title: e.target.value })
                              }
                              sx={{ mr: 2 }}
                            />
                          </Box>

                          <TextField
                            fullWidth
                            label="Topic Description"
                            value={topic.description || ''}
                            onChange={(e) =>
                              handleTopicChange(topic._id, { description: e.target.value })
                            }
                            multiline
                            rows={2}
                            placeholder="Enter a description for this topic (optional)"
                            helperText="This description will be shown to users when filling the form"
                            sx={{ mt: 2 }}
                          />

                          <Divider sx={{ my: 2 }} />

                          {/* Topic Fields */}
                          <Box sx={{ mb: 2 }}>
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                mb: 2,
                              }}
                            >
                              <Typography variant="subtitle1">Fields</Typography>
                              <Box
                                sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, mb: 2 }}
                              >
                                <Button
                                  variant="outlined"
                                  color="primary"
                                  startIcon={<Iconify icon="mdi:plus" />}
                                  onClick={() => handleAddField(topic._id)}
                                  disabled={isTranslation} // Disable adding fields in translation mode
                                >
                                  Add Field
                                </Button>
                              </Box>
                            </Box>

                            {topic.fields && topic.fields.length > 0 ? (
                              topic.fields.map((field) => (
                                <Paper
                                  key={field._id}
                                  elevation={1}
                                  sx={{ p: 2, mb: 2, position: 'relative' }}
                                >
                                  <Box
                                    sx={{
                                      position: 'absolute',
                                      top: 8,
                                      right: 8,
                                      display: 'flex',
                                      gap: 1,
                                    }}
                                  >
                                    {!isTranslation && ( // Only show remove button when not in translation mode
                                      <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => handleRemoveField(field._id, topic._id)}
                                      >
                                        <DeleteIcon fontSize="small" />
                                      </IconButton>
                                    )}
                                  </Box>
                                  <Stack spacing={2}>
                                    <TextField
                                      fullWidth
                                      label="Field Name"
                                      value={field.name}
                                      onChange={(e) =>
                                        handleFieldChange(
                                          field._id,
                                          { name: e.target.value },
                                          topic._id
                                        )
                                      }
                                    />
                                    <TextField
                                      fullWidth
                                      label="Field Label"
                                      value={field.label}
                                      onChange={(e) =>
                                        handleFieldChange(
                                          field._id,
                                          { label: e.target.value },
                                          topic._id
                                        )
                                      }
                                    />
                                    <TextField
                                      fullWidth
                                      label="Field Description"
                                      value={field.description || ''}
                                      onChange={(e) =>
                                        handleFieldChange(
                                          field._id,
                                          { description: e.target.value },
                                          topic._id
                                        )
                                      }
                                      multiline
                                      rows={2}
                                      placeholder="Enter a helpful description for this field (optional)"
                                      helperText="This description will be shown to users as help text when filling the form"
                                    />
                                    <FormControl fullWidth>
                                      <InputLabel>Field Type</InputLabel>
                                      <Select
                                        value={field.type}
                                        label="Field Type"
                                        onChange={(e) =>
                                          handleFieldChange(
                                            field._id,
                                            { type: e.target.value },
                                            topic._id
                                          )
                                        }
                                      >
                                        <MenuItem value="text">Text</MenuItem>
                                        <MenuItem value="email">Email</MenuItem>
                                        <MenuItem value="number">Number</MenuItem>
                                        <MenuItem value="textarea">Text Area</MenuItem>
                                        <MenuItem value="select">Select</MenuItem>
                                        <MenuItem value="radio">Radio</MenuItem>
                                        <MenuItem value="checkbox">Checkbox</MenuItem>
                                        <MenuItem value="date">Date</MenuItem>
                                        <MenuItem value="feedback">Feedback</MenuItem>
                                      </Select>
                                    </FormControl>
                                    <FormControlLabel
                                      control={
                                        <Switch
                                          checked={field.required}
                                          onChange={(e) =>
                                            handleFieldChange(
                                              field._id,
                                              { required: e.target.checked },
                                              topic._id
                                            )
                                          }
                                        />
                                      }
                                      label="Required"
                                    />

                                    {/* Options for select, radio, checkbox */}
                                    {['select', 'radio', 'checkbox'].includes(field.type) && (
                                      <Box>
                                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                          Options
                                        </Typography>
                                        {field.options && field.options.length > 0 ? (
                                          field.options.map((option, optionIndex) => (
                                            <Box
                                              key={optionIndex}
                                              sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                mb: 1,
                                                gap: 1,
                                              }}
                                            >
                                              <TextField
                                                size="small"
                                                label="Label"
                                                value={option.label || ''}
                                                onChange={(e) => {
                                                  const updatedOptions = [...field.options];
                                                  updatedOptions[optionIndex] = {
                                                    ...updatedOptions[optionIndex],
                                                    label: e.target.value,
                                                  };
                                                  handleFieldChange(
                                                    field._id,
                                                    { options: updatedOptions },
                                                    topic._id
                                                  );
                                                }}
                                                sx={{ flex: 1 }}
                                              />
                                              <TextField
                                                size="small"
                                                label="Value"
                                                value={option.value || ''}
                                                onChange={(e) => {
                                                  const updatedOptions = [...field.options];
                                                  updatedOptions[optionIndex] = {
                                                    ...updatedOptions[optionIndex],
                                                    value: e.target.value,
                                                  };
                                                  handleFieldChange(
                                                    field._id,
                                                    { options: updatedOptions },
                                                    topic._id
                                                  );
                                                }}
                                                sx={{ flex: 1 }}
                                              />
                                              <IconButton
                                                size="small"
                                                color="error"
                                                onClick={() => {
                                                  const updatedOptions = field.options.filter(
                                                    (_, i) => i !== optionIndex
                                                  );
                                                  handleFieldChange(
                                                    field._id,
                                                    { options: updatedOptions },
                                                    topic._id
                                                  );
                                                }}
                                              >
                                                <Iconify icon="mdi:delete" />
                                              </IconButton>
                                            </Box>
                                          ))
                                        ) : (
                                          <Typography
                                            variant="body2"
                                            color="text.secondary"
                                            sx={{ mb: 1 }}
                                          >
                                            No options added yet.
                                          </Typography>
                                        )}
                                        <Button
                                          size="small"
                                          startIcon={<Iconify icon="mdi:plus" />}
                                          onClick={() => {
                                            const updatedOptions = [
                                              ...(field.options || []),
                                              { label: '', value: '' },
                                            ];
                                            handleFieldChange(
                                              field._id,
                                              { options: updatedOptions },
                                              topic._id
                                            );
                                          }}
                                        >
                                          Add Option
                                        </Button>
                                      </Box>
                                    )}

                                    {/* Validation settings */}
                                    <Box>
                                      <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                        Validation
                                      </Typography>
                                      <Grid container spacing={2}>
                                        {['text', 'number', 'email', 'textarea'].includes(
                                          field.type
                                        ) && (
                                          <>
                                            <Grid item xs={12} sm={6}>
                                              <TextField
                                                fullWidth
                                                size="small"
                                                label="Min Value/Length"
                                                type="number"
                                                value={field.validation?.min || ''}
                                                onChange={(e) => {
                                                  const updatedValidation = {
                                                    ...(field.validation || {}),
                                                    min: e.target.value,
                                                  };
                                                  handleFieldChange(
                                                    field._id,
                                                    { validation: updatedValidation },
                                                    topic._id
                                                  );
                                                }}
                                              />
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                              <TextField
                                                fullWidth
                                                size="small"
                                                label="Max Value/Length"
                                                type="number"
                                                value={field.validation?.max || ''}
                                                onChange={(e) => {
                                                  const updatedValidation = {
                                                    ...(field.validation || {}),
                                                    max: e.target.value,
                                                  };
                                                  handleFieldChange(
                                                    field._id,
                                                    { validation: updatedValidation },
                                                    topic._id
                                                  );
                                                }}
                                              />
                                            </Grid>
                                          </>
                                        )}
                                        <Grid item xs={12} sm={6}>
                                          <TextField
                                            fullWidth
                                            size="small"
                                            label="Pattern (Regex)"
                                            value={field.validation?.pattern || ''}
                                            onChange={(e) => {
                                              const updatedValidation = {
                                                ...(field.validation || {}),
                                                pattern: e.target.value,
                                              };
                                              handleFieldChange(
                                                field._id,
                                                { validation: updatedValidation },
                                                topic._id
                                              );
                                            }}
                                          />
                                        </Grid>
                                        <Grid item xs={12} sm={6}>
                                          <TextField
                                            fullWidth
                                            size="small"
                                            label="Error Message"
                                            value={field.validation?.message || ''}
                                            onChange={(e) => {
                                              const updatedValidation = {
                                                ...(field.validation || {}),
                                                message: e.target.value,
                                              };
                                              handleFieldChange(
                                                field._id,
                                                { validation: updatedValidation },
                                                topic._id
                                              );
                                            }}
                                          />
                                        </Grid>
                                      </Grid>
                                    </Box>

                                    {/* Conditional Logic */}
                                    <Box>
                                      <FormControlLabel
                                        control={
                                          <Switch
                                            checked={field.conditional_logic?.enabled || false}
                                            onChange={(e) => {
                                              const updatedConditionalLogic = {
                                                ...(field.conditional_logic || {}),
                                                enabled: e.target.checked,
                                                rules: field.conditional_logic?.rules || [
                                                  [
                                                    {
                                                      field: '',
                                                      operator: 'has_any_value',
                                                      value: '',
                                                    },
                                                  ],
                                                ],
                                              };
                                              handleFieldChange(
                                                field._id,
                                                { conditional_logic: updatedConditionalLogic },
                                                topic._id
                                              );
                                            }}
                                          />
                                        }
                                        label="Enable Conditional Logic"
                                      />

                                      {field.conditional_logic?.enabled && (
                                        <Box
                                          sx={{
                                            pl: 2,
                                            mt: 1,
                                            borderLeft: '1px solid',
                                            borderColor: 'divider',
                                          }}
                                        >
                                          <Typography
                                            variant="body2"
                                            color="text.secondary"
                                            sx={{ mb: 1 }}
                                          >
                                            Show this field if:
                                          </Typography>

                                          {field.conditional_logic.rules.map(
                                            (ruleGroup, groupIndex) => (
                                              <Box
                                                key={groupIndex}
                                                sx={{
                                                  mb: 2,
                                                  p: 1,
                                                  bgcolor: 'background.paper',
                                                  borderRadius: 1,
                                                }}
                                              >
                                                <Typography
                                                  variant="caption"
                                                  color="text.secondary"
                                                  sx={{ mb: 1, display: 'block' }}
                                                >
                                                  {groupIndex > 0 ? 'OR' : 'IF'}
                                                </Typography>

                                                {ruleGroup.map((rule, ruleIndex) => (
                                                  <Box
                                                    key={ruleIndex}
                                                    sx={{
                                                      display: 'flex',
                                                      alignItems: 'center',
                                                      mb: 1,
                                                      gap: 1,
                                                    }}
                                                  >
                                                    {ruleIndex > 0 && (
                                                      <Typography
                                                        variant="caption"
                                                        sx={{ minWidth: '30px' }}
                                                      >
                                                        AND
                                                      </Typography>
                                                    )}

                                                    <FormControl
                                                      size="small"
                                                      sx={{ minWidth: '120px' }}
                                                    >
                                                      <InputLabel>Field</InputLabel>
                                                      <Select
                                                        value={rule.field || ''}
                                                        label="Field"
                                                        onChange={(e) => {
                                                          const updatedRules = [
                                                            ...field.conditional_logic.rules,
                                                          ];
                                                          updatedRules[groupIndex][ruleIndex] = {
                                                            ...updatedRules[groupIndex][ruleIndex],
                                                            field: e.target.value,
                                                          };
                                                          handleFieldChange(
                                                            field._id,
                                                            {
                                                              conditional_logic: {
                                                                ...field.conditional_logic,
                                                                rules: updatedRules,
                                                              },
                                                            },
                                                            topic._id
                                                          );
                                                        }}
                                                      >
                                                        {topic.fields
                                                          .filter((f) => f._id !== field._id)
                                                          .map((f) => (
                                                            <MenuItem key={f._id} value={f.name}>
                                                              {f.label}
                                                            </MenuItem>
                                                          ))}
                                                      </Select>
                                                    </FormControl>

                                                    <FormControl
                                                      size="small"
                                                      sx={{ minWidth: '120px' }}
                                                    >
                                                      <InputLabel>Operator</InputLabel>
                                                      <Select
                                                        value={rule.operator || 'has_any_value'}
                                                        label="Operator"
                                                        onChange={(e) => {
                                                          const updatedRules = [
                                                            ...field.conditional_logic.rules,
                                                          ];
                                                          updatedRules[groupIndex][ruleIndex] = {
                                                            ...updatedRules[groupIndex][ruleIndex],
                                                            operator: e.target.value,
                                                          };
                                                          handleFieldChange(
                                                            field._id,
                                                            {
                                                              conditional_logic: {
                                                                ...field.conditional_logic,
                                                                rules: updatedRules,
                                                              },
                                                            },
                                                            topic._id
                                                          );
                                                        }}
                                                      >
                                                        <MenuItem value="has_any_value">
                                                          Has Any Value
                                                        </MenuItem>
                                                        <MenuItem value="is_empty">
                                                          Is Empty
                                                        </MenuItem>
                                                        <MenuItem value="==">Equals</MenuItem>
                                                        <MenuItem value="!=">Not Equals</MenuItem>
                                                        <MenuItem value=">">Greater Than</MenuItem>
                                                        <MenuItem value="<">Less Than</MenuItem>
                                                        <MenuItem value="contains">
                                                          Contains
                                                        </MenuItem>
                                                        <MenuItem value="starts_with">
                                                          Starts With
                                                        </MenuItem>
                                                        <MenuItem value="ends_with">
                                                          Ends With
                                                        </MenuItem>
                                                      </Select>
                                                    </FormControl>

                                                    {!['has_any_value', 'is_empty'].includes(
                                                      rule.operator
                                                    ) && (
                                                      <TextField
                                                        size="small"
                                                        label="Value"
                                                        value={rule.value || ''}
                                                        onChange={(e) => {
                                                          const updatedRules = [
                                                            ...field.conditional_logic.rules,
                                                          ];
                                                          updatedRules[groupIndex][ruleIndex] = {
                                                            ...updatedRules[groupIndex][ruleIndex],
                                                            value: e.target.value,
                                                          };
                                                          handleFieldChange(
                                                            field._id,
                                                            {
                                                              conditional_logic: {
                                                                ...field.conditional_logic,
                                                                rules: updatedRules,
                                                              },
                                                            },
                                                            topic._id
                                                          );
                                                        }}
                                                        sx={{ flex: 1 }}
                                                      />
                                                    )}

                                                    <IconButton
                                                      size="small"
                                                      color="error"
                                                      onClick={() => {
                                                        const updatedRules = [
                                                          ...field.conditional_logic.rules,
                                                        ];
                                                        if (updatedRules[groupIndex].length > 1) {
                                                          updatedRules[groupIndex] = updatedRules[
                                                            groupIndex
                                                          ].filter((_, i) => i !== ruleIndex);
                                                        } else {
                                                          updatedRules.splice(groupIndex, 1);
                                                        }
                                                        handleFieldChange(
                                                          field._id,
                                                          {
                                                            conditional_logic: {
                                                              ...field.conditional_logic,
                                                              rules: updatedRules,
                                                            },
                                                          },
                                                          topic._id
                                                        );
                                                      }}
                                                    >
                                                      <Iconify icon="mdi:delete" />
                                                    </IconButton>
                                                  </Box>
                                                ))}

                                                <Button
                                                  size="small"
                                                  startIcon={<Iconify icon="mdi:plus" />}
                                                  onClick={() => {
                                                    const updatedRules = [
                                                      ...field.conditional_logic.rules,
                                                    ];
                                                    updatedRules[groupIndex].push({
                                                      field: '',
                                                      operator: 'has_any_value',
                                                      value: '',
                                                    });
                                                    handleFieldChange(
                                                      field._id,
                                                      {
                                                        conditional_logic: {
                                                          ...field.conditional_logic,
                                                          rules: updatedRules,
                                                        },
                                                      },
                                                      topic._id
                                                    );
                                                  }}
                                                >
                                                  Add AND Condition
                                                </Button>
                                              </Box>
                                            )
                                          )}

                                          <Button
                                            size="small"
                                            variant="outlined"
                                            startIcon={<Iconify icon="mdi:plus" />}
                                            onClick={() => {
                                              const updatedRules = [
                                                ...field.conditional_logic.rules,
                                                [
                                                  {
                                                    field: '',
                                                    operator: 'has_any_value',
                                                    value: '',
                                                  },
                                                ],
                                              ];
                                              handleFieldChange(
                                                field._id,
                                                {
                                                  conditional_logic: {
                                                    ...field.conditional_logic,
                                                    rules: updatedRules,
                                                  },
                                                },
                                                topic._id
                                              );
                                            }}
                                          >
                                            Add OR Condition
                                          </Button>
                                        </Box>
                                      )}
                                    </Box>
                                  </Stack>
                                </Paper>
                              ))
                            ) : (
                              <Box
                                sx={{
                                  textAlign: 'center',
                                  p: 2,
                                  bgcolor: 'background.neutral',
                                  borderRadius: 1,
                                }}
                              >
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                  No fields added to this topic yet.
                                </Typography>
                                <Button
                                  size="small"
                                  startIcon={<Iconify icon="mdi:plus" />}
                                  onClick={() => handleAddField(topic._id)}
                                  disabled={isTranslation} // Disable adding fields in translation mode
                                >
                                  Add Field
                                </Button>
                              </Box>
                            )}
                          </Box>
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                  ))
                ) : (
                  <Box
                    sx={{
                      textAlign: 'center',
                      p: 3,
                      bgcolor: 'background.neutral',
                      borderRadius: 1,
                    }}
                  >
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      No topics added yet. Add a topic to organize your form fields.
                    </Typography>
                    <Button
                      variant="outlined"
                      startIcon={<Iconify icon="mdi:plus" />}
                      onClick={handleAddTopic}
                    >
                      Add First Topic
                    </Button>
                  </Box>
                )}
              </Box>

              {/* Legacy Fields (geriye dönük uyumluluk) */}
              {!formHasTopics && selectedForm.fields && selectedForm.fields.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Form Fields (Legacy)
                  </Typography>

                  {selectedForm.fields.map((field) => (
                    <Box
                      key={field._id}
                      sx={{ border: 1, borderColor: 'grey.300', p: 2, borderRadius: 1, mb: 2 }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="subtitle1">
                          {field.label} ({field.type})
                        </Typography>
                        <IconButton
                          color="error"
                          size="small"
                          onClick={() => handleRemoveField(field._id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>

                      <Stack spacing={2}>
                        <TextField
                          fullWidth
                          label="Field Name"
                          value={field.name}
                          onChange={(e) => handleFieldChange(field._id, { name: e.target.value })}
                        />
                        <TextField
                          fullWidth
                          label="Field Label"
                          value={field.label}
                          onChange={(e) => handleFieldChange(field._id, { label: e.target.value })}
                        />
                        <FormControl fullWidth>
                          <InputLabel>Field Type</InputLabel>
                          <Select
                            value={field.type}
                            label="Field Type"
                            onChange={(e) => handleFieldChange(field._id, { type: e.target.value })}
                          >
                            <MenuItem value="text">Text</MenuItem>
                            <MenuItem value="email">Email</MenuItem>
                            <MenuItem value="number">Number</MenuItem>
                            <MenuItem value="textarea">Text Area</MenuItem>
                            <MenuItem value="select">Select</MenuItem>
                            <MenuItem value="radio">Radio</MenuItem>
                            <MenuItem value="checkbox">Checkbox</MenuItem>
                            <MenuItem value="date">Date</MenuItem>
                            <MenuItem value="feedback">Feedback</MenuItem>
                          </Select>
                        </FormControl>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={field.required}
                              onChange={(e) =>
                                handleFieldChange(field._id, { required: e.target.checked })
                              }
                            />
                          }
                          label="Required"
                        />

                        {/* Options for select, radio, checkbox */}
                        {['select', 'radio', 'checkbox'].includes(field.type) && (
                          <Box>
                            <Typography variant="subtitle2" sx={{ mb: 1 }}>
                              Options
                            </Typography>
                            {field.options && field.options.length > 0 ? (
                              field.options.map((option, optionIndex) => (
                                <Box
                                  key={optionIndex}
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    mb: 1,
                                    gap: 1,
                                  }}
                                >
                                  <TextField
                                    size="small"
                                    label="Label"
                                    value={option.label || ''}
                                    onChange={(e) => {
                                      const updatedOptions = [...field.options];
                                      updatedOptions[optionIndex] = {
                                        ...updatedOptions[optionIndex],
                                        label: e.target.value,
                                      };
                                      handleFieldChange(field._id, { options: updatedOptions });
                                    }}
                                    sx={{ flex: 1 }}
                                  />
                                  <TextField
                                    size="small"
                                    label="Value"
                                    value={option.value || ''}
                                    onChange={(e) => {
                                      const updatedOptions = [...field.options];
                                      updatedOptions[optionIndex] = {
                                        ...updatedOptions[optionIndex],
                                        value: e.target.value,
                                      };
                                      handleFieldChange(field._id, { options: updatedOptions });
                                    }}
                                    sx={{ flex: 1 }}
                                  />
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => {
                                      const updatedOptions = field.options.filter(
                                        (_, i) => i !== optionIndex
                                      );
                                      handleFieldChange(field._id, { options: updatedOptions });
                                    }}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </Box>
                              ))
                            ) : (
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                No options added yet.
                              </Typography>
                            )}
                            <Button
                              size="small"
                              startIcon={<AddIcon />}
                              onClick={() => {
                                const updatedOptions = [
                                  ...(field.options || []),
                                  { label: '', value: '' },
                                ];
                                handleFieldChange(field._id, { options: updatedOptions });
                              }}
                            >
                              Add Option
                            </Button>
                          </Box>
                        )}

                        {/* Validation settings */}
                        <Box>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>
                            Validation
                          </Typography>
                          <Grid container spacing={2}>
                            {['text', 'number', 'email', 'textarea'].includes(field.type) && (
                              <>
                                <Grid item xs={12} sm={6}>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    label="Min Value/Length"
                                    type="number"
                                    value={field.validation?.min || ''}
                                    onChange={(e) => {
                                      const updatedValidation = {
                                        ...(field.validation || {}),
                                        min: e.target.value,
                                      };
                                      handleFieldChange(field._id, {
                                        validation: updatedValidation,
                                      });
                                    }}
                                  />
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    label="Max Value/Length"
                                    type="number"
                                    value={field.validation?.max || ''}
                                    onChange={(e) => {
                                      const updatedValidation = {
                                        ...(field.validation || {}),
                                        max: e.target.value,
                                      };
                                      handleFieldChange(field._id, {
                                        validation: updatedValidation,
                                      });
                                    }}
                                  />
                                </Grid>
                              </>
                            )}
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                size="small"
                                label="Pattern (Regex)"
                                value={field.validation?.pattern || ''}
                                onChange={(e) => {
                                  const updatedValidation = {
                                    ...(field.validation || {}),
                                    pattern: e.target.value,
                                  };
                                  handleFieldChange(field._id, { validation: updatedValidation });
                                }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                size="small"
                                label="Error Message"
                                value={field.validation?.message || ''}
                                onChange={(e) => {
                                  const updatedValidation = {
                                    ...(field.validation || {}),
                                    message: e.target.value,
                                  };
                                  handleFieldChange(field._id, { validation: updatedValidation });
                                }}
                              />
                            </Grid>
                          </Grid>
                        </Box>

                        {/* Conditional Logic */}
                        <Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={field.conditional_logic?.enabled || false}
                                onChange={(e) => {
                                  const updatedConditionalLogic = {
                                    ...(field.conditional_logic || {}),
                                    enabled: e.target.checked,
                                    rules: field.conditional_logic?.rules || [
                                      [{ field: '', operator: 'has_any_value', value: '' }],
                                    ],
                                  };
                                  handleFieldChange(field._id, {
                                    conditional_logic: updatedConditionalLogic,
                                  });
                                }}
                              />
                            }
                            label="Enable Conditional Logic"
                          />

                          {field.conditional_logic?.enabled && (
                            <Box
                              sx={{ pl: 2, mt: 1, borderLeft: '1px solid', borderColor: 'divider' }}
                            >
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                Show this field if:
                              </Typography>

                              {field.conditional_logic.rules.map((ruleGroup, groupIndex) => (
                                <Box
                                  key={groupIndex}
                                  sx={{ mb: 2, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}
                                >
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{ mb: 1, display: 'block' }}
                                  >
                                    {groupIndex > 0 ? 'OR' : 'IF'}
                                  </Typography>

                                  {ruleGroup.map((rule, ruleIndex) => (
                                    <Box
                                      key={ruleIndex}
                                      sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        mb: 1,
                                        gap: 1,
                                      }}
                                    >
                                      {ruleIndex > 0 && (
                                        <Typography variant="caption" sx={{ minWidth: '30px' }}>
                                          AND
                                        </Typography>
                                      )}

                                      <FormControl size="small" sx={{ minWidth: '120px' }}>
                                        <InputLabel>Field</InputLabel>
                                        <Select
                                          value={rule.field || ''}
                                          label="Field"
                                          onChange={(e) => {
                                            const updatedRules = [...field.conditional_logic.rules];
                                            updatedRules[groupIndex][ruleIndex] = {
                                              ...updatedRules[groupIndex][ruleIndex],
                                              field: e.target.value,
                                            };
                                            handleFieldChange(field._id, {
                                              conditional_logic: {
                                                ...field.conditional_logic,
                                                rules: updatedRules,
                                              },
                                            });
                                          }}
                                        >
                                          {selectedForm.fields
                                            .filter((f) => f._id !== field._id)
                                            .map((f) => (
                                              <MenuItem key={f._id} value={f.name}>
                                                {f.label}
                                              </MenuItem>
                                            ))}
                                        </Select>
                                      </FormControl>

                                      <FormControl size="small" sx={{ minWidth: '120px' }}>
                                        <InputLabel>Operator</InputLabel>
                                        <Select
                                          value={rule.operator || 'has_any_value'}
                                          label="Operator"
                                          onChange={(e) => {
                                            const updatedRules = [...field.conditional_logic.rules];
                                            updatedRules[groupIndex][ruleIndex] = {
                                              ...updatedRules[groupIndex][ruleIndex],
                                              operator: e.target.value,
                                            };
                                            handleFieldChange(field._id, {
                                              conditional_logic: {
                                                ...field.conditional_logic,
                                                rules: updatedRules,
                                              },
                                            });
                                          }}
                                        >
                                          <MenuItem value="has_any_value">Has Any Value</MenuItem>
                                          <MenuItem value="is_empty">Is Empty</MenuItem>
                                          <MenuItem value="==">Equals</MenuItem>
                                          <MenuItem value="!=">Not Equals</MenuItem>
                                          <MenuItem value=">">Greater Than</MenuItem>
                                          <MenuItem value="<">Less Than</MenuItem>
                                          <MenuItem value="contains">Contains</MenuItem>
                                          <MenuItem value="starts_with">Starts With</MenuItem>
                                          <MenuItem value="ends_with">Ends With</MenuItem>
                                        </Select>
                                      </FormControl>

                                      {!['has_any_value', 'is_empty'].includes(rule.operator) && (
                                        <TextField
                                          size="small"
                                          label="Value"
                                          value={rule.value || ''}
                                          onChange={(e) => {
                                            const updatedRules = [...field.conditional_logic.rules];
                                            updatedRules[groupIndex][ruleIndex] = {
                                              ...updatedRules[groupIndex][ruleIndex],
                                              value: e.target.value,
                                            };
                                            handleFieldChange(field._id, {
                                              conditional_logic: {
                                                ...field.conditional_logic,
                                                rules: updatedRules,
                                              },
                                            });
                                          }}
                                          sx={{ flex: 1 }}
                                        />
                                      )}

                                      <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => {
                                          const updatedRules = [...field.conditional_logic.rules];
                                          if (updatedRules[groupIndex].length > 1) {
                                            updatedRules[groupIndex] = updatedRules[
                                              groupIndex
                                            ].filter((_, i) => i !== ruleIndex);
                                          } else {
                                            updatedRules.splice(groupIndex, 1);
                                          }
                                          handleFieldChange(field._id, {
                                            conditional_logic: {
                                              ...field.conditional_logic,
                                              rules: updatedRules,
                                            },
                                          });
                                        }}
                                      >
                                        <DeleteIcon />
                                      </IconButton>
                                    </Box>
                                  ))}

                                  <Button
                                    size="small"
                                    startIcon={<Iconify icon="mdi:plus" />}
                                    onClick={() => {
                                      const updatedRules = [...field.conditional_logic.rules];
                                      updatedRules[groupIndex].push({
                                        field: '',
                                        operator: 'has_any_value',
                                        value: '',
                                      });
                                      handleFieldChange(field._id, {
                                        conditional_logic: {
                                          ...field.conditional_logic,
                                          rules: updatedRules,
                                        },
                                      });
                                    }}
                                  >
                                    Add AND Condition
                                  </Button>
                                </Box>
                              ))}

                              <Button
                                size="small"
                                variant="outlined"
                                startIcon={<Iconify icon="mdi:plus" />}
                                onClick={() => {
                                  const updatedRules = [
                                    ...field.conditional_logic.rules,
                                    [
                                      {
                                        field: '',
                                        operator: 'has_any_value',
                                        value: '',
                                      },
                                    ],
                                  ];
                                  handleFieldChange(field._id, {
                                    conditional_logic: {
                                      ...field.conditional_logic,
                                      rules: updatedRules,
                                    },
                                  });
                                }}
                              >
                                Add OR Condition
                              </Button>
                            </Box>
                          )}
                        </Box>
                      </Stack>
                    </Box>
                  ))}

                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => handleAddField()}
                    sx={{ mt: 1 }}
                  >
                    Add Field
                  </Button>
                </Box>
              )}

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: 2,
                  mt: 4,
                }}
              >
                <Button variant="outlined" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  color={isTranslation ? 'success' : 'primary'}
                  onClick={() => {
                    const prepareFormData = (form) => {
                      // Deep clone form object to avoid modifications
                      const clonedForm = JSON.parse(JSON.stringify(form));

                      // Process fields
                      if (clonedForm.fields) {
                        clonedForm.fields = clonedForm.fields.map((field) => {
                          const { _id, ...rest } = field;
                          // Label boşsa description değerini veya varsayılan bir değer kullan
                          if (!rest.label || rest.label.trim() === '') {
                            rest.label = rest.description || rest.name || 'Field Label';
                          }
                          return {
                            ...rest,
                            _id: _id.startsWith('temp_') ? undefined : _id,
                          };
                        });
                      }

                      // Process topics and their fields
                      if (clonedForm.topics) {
                        clonedForm.topics = clonedForm.topics.map((topic) => {
                          const { _id, ...topicRest } = topic;
                          const topicResult = {
                            ...topicRest,
                            _id: _id.startsWith('temp_') ? undefined : _id,
                          };

                          if (topic.fields) {
                            topicResult.fields = topic.fields.map((field) => {
                              const { _id, ...fieldRest } = field;
                              // Label boşsa description değerini veya varsayılan bir değer kullan
                              if (!fieldRest.label || fieldRest.label.trim() === '') {
                                fieldRest.label =
                                  fieldRest.description || fieldRest.name || 'Field Label';
                              }
                              return {
                                ...fieldRest,
                                _id: _id.startsWith('temp_') ? undefined : _id,
                              };
                            });
                          }

                          return topicResult;
                        });
                      }

                      return clonedForm;
                    };

                    onSave(prepareFormData(selectedForm));
                  }}
                >
                  {isTranslation ? 'Save Translation' : 'Save Changes'}
                </Button>
              </Box>
            </Stack>
          </>
        )}
      </Box>
    </Modal>
  );
}

EditFormModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  form: PropTypes.object,
  onSave: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
  isTranslation: PropTypes.bool,
  translationLanguage: PropTypes.string,
};
