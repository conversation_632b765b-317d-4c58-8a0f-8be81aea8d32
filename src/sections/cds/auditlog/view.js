import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Container,
  FormControl,
  Grid,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAuditLog } from 'src/apis';
import { useSettingsContext } from 'src/components/settings';

export default function AuditLogView() {
  const settings = useSettingsContext();
  const { getAuditLogs, allAuditLogs } = useAuditLog();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatusCode, setSelectedStatusCode] = useState('all');
  const [searchEmail, setSearchEmail] = useState('');

  useEffect(() => {
    getAuditLogs();
  }, []);

  const categories = ['all', ...new Set(allAuditLogs?.map((log) => log.category) || [])];
  const statusCodes = ['all', ...new Set(allAuditLogs?.map((log) => log.statusCode) || [])];

  const filteredLogs = allAuditLogs
    ?.filter((log) => (selectedCategory === 'all' ? true : log.category === selectedCategory))
    ?.filter((log) => log.email?.toLowerCase().includes(searchEmail?.toLowerCase()));

  const filteredLogsByStatusCode = filteredLogs?.filter((log) =>
    selectedStatusCode === 'all' ? true : log.statusCode === selectedStatusCode
  );

  const renderTranslationChanges = (translations) => {
    const changes = [];

    if (translations?.old && translations?.new) {
      Object.keys(translations.old).forEach((language) => {
        const oldTranslation = translations.old[language];
        const newTranslation = translations.new[language];

        Object.keys(oldTranslation).forEach((field) => {
          if (JSON.stringify(oldTranslation[field]) !== JSON.stringify(newTranslation[field])) {
            changes.push(
              <Typography key={`${language}-${field}`} variant="body2" sx={{ mb: 1 }}>
                <strong>
                  {language.charAt(0).toUpperCase() + language.slice(1)} - {field}:
                </strong>{' '}
                changed from <strong style={{ color: 'red' }}>{oldTranslation[field]}</strong> to{' '}
                <strong style={{ color: 'green' }}>{newTranslation[field]}</strong>
              </Typography>
            );
          }
        });
      });
    }

    return changes.length > 0 ? changes : 'No changes in translations';
  };

  const renderBodyContent = (body) => {
    return Object.entries(body)
      .map(([key, value]) => {
        if (key === 'translations') {
          return (
            <Box key={key}>
              <Typography variant="subtitle2" sx={{ mb: 1, color: 'primary.main' }}>
                Translation Changes
              </Typography>
              {renderTranslationChanges(value)}
            </Box>
          );
        }

        let displayValue;
        if (typeof value === 'object' && value?.old !== undefined && value?.new !== undefined) {
          displayValue = (
            <>
              changed from <strong style={{ color: 'red' }}>{String(value.old)}</strong> to{' '}
              <strong style={{ color: 'green' }}>{String(value.new)}</strong>
            </>
          );
        } else {
          displayValue =
            typeof value === 'object'
              ? JSON.stringify(value, null, 2)
              : Array.isArray(value)
              ? value.join(', ')
              : String(value);
        }

        if (
          displayValue === null ||
          displayValue === undefined ||
          displayValue === 'null' ||
          displayValue === 'undefined'
        ) {
          return null;
        }

        const formattedKey = key
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');

        return (
          <Typography key={key} variant="body2" sx={{ mb: 1, whiteSpace: 'pre-wrap' }}>
            <strong>{formattedKey}:</strong> {displayValue}
          </Typography>
        );
      })
      .filter(Boolean);
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box
            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}
          >
            <Grid container alignItems="center">
              <Grid item xs={12} md={4}>
                <Typography variant="h4">Audit Log</Typography>
              </Grid>

              <Grid item xs={12} md={8}>
                <Grid container spacing={2} justifyContent="flex-end">
                  <Grid item xs={12} sm={3}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="Search by email..."
                      value={searchEmail}
                      onChange={(e) => setSearchEmail(e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        size="small"
                      >
                        {categories.map((category) => (
                          <MenuItem key={category} value={category}>
                            {category === 'all' ? 'All Categories' : `${category}`}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Select
                        value={selectedStatusCode}
                        onChange={(e) => setSelectedStatusCode(e.target.value)}
                        size="small"
                      >
                        {statusCodes.map((code) => (
                          <MenuItem key={code} value={code}>
                            {code === 'all' ? 'All Status Codes' : `${code}`}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </Grid>

        <Grid item xs={12}>
          {filteredLogsByStatusCode?.length === 0 ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '200px',
                backgroundColor: 'background.neutral',
                borderRadius: 1,
              }}
            >
              <Typography variant="subtitle1" color="text.secondary">
                No results found for the selected filters.
              </Typography>
            </Box>
          ) : (
            <Grid container spacing={2}>
              {filteredLogsByStatusCode?.map((auditLog) => (
                <Grid item xs={12} key={auditLog._id}>
                  <Accordion
                    sx={{
                      '&:before': { display: 'none' },
                      borderRadius: '8px',
                      boxShadow: (theme) => theme.shadows[2],
                      '&.Mui-expanded': {
                        borderRadius: '8px',
                      },
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{
                        '& .MuiAccordionSummary-content': {
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          width: '100%',
                        },
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          width: '100%',
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          color="primary"
                          sx={{ fontWeight: 'medium', width: '50%' }}
                        >
                          {auditLog.email} - <strong>{auditLog.action}</strong> operation was
                          performed.
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          color={
                            auditLog.statusCode >= 200 && auditLog.statusCode < 300
                              ? 'success.main'
                              : 'error.main'
                          }
                          sx={{ fontWeight: 'medium' }}
                        >
                          Status code: {auditLog.statusCode}
                        </Typography>

                        <Typography variant="caption" color="text.secondary">
                          {new Date(auditLog.timestamp).toLocaleString('tr-TR')}
                        </Typography>
                      </Box>
                    </AccordionSummary>

                    <AccordionDetails>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                            <Typography variant="body2">
                              <strong>User:</strong> {auditLog.email}
                            </Typography>
                            <Typography variant="body2">
                              <strong>Path:</strong> {auditLog.path}
                            </Typography>
                            <Typography variant="body2">
                              <strong>Status Code:</strong> {auditLog.statusCode}
                            </Typography>
                          </Box>
                        </Grid>

                        {auditLog?.body ? (
                          <Grid item xs={12}>
                            <Typography variant="subtitle2" sx={{ mb: 1.5, color: 'primary.main' }}>
                              Operation Details
                            </Typography>
                            <Box
                              sx={{
                                pl: 2,
                                py: 1.5,
                                borderLeft: 2,
                                borderColor: 'primary.main',
                                bgcolor: (theme) => theme.palette.background.neutral,
                                borderRadius: 1,
                              }}
                            >
                              {renderBodyContent(auditLog.body)}
                            </Box>
                          </Grid>
                        ) : null}
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              ))}
            </Grid>
          )}
        </Grid>
      </Grid>
    </Container>
  );
}
