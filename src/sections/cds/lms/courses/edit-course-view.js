import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Typography,
  Button,
  Grid,
  Stack,
  Box,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast } from 'react-toastify';
import { paths } from 'src/routes/paths';
import { useCourses } from 'src/apis/useCourses';
import { useQuizzes } from 'src/apis/useQuizzes';
import { useMedia } from 'src/apis/useMedia';
import { useUsecases } from 'src/apis/useUsecases';
import { Editor } from '@tinymce/tinymce-react';
import { BasicInformation } from './components/BasicInformation';
import { CourseMedia } from './components/CourseMedia';
import { CourseSettings } from './components/CourseSettings';
import { Instructors } from './components/Instructors';
import { ChaptersAndTopics } from './components/ChaptersAndTopics';
import BundledEditor from 'src/components/text-editor/editor';
import { Translate as TranslateIcon, Delete as DeleteIcon } from '@mui/icons-material';

const CONTENT_BLOCK_TYPES = [
  'text',
  'video',
  'quiz',
  'usecase',
  'pdf',
  'playground-gpt',
  'playground-dalle',
  'playground-heygen',
  'form',
];

const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'de', name: 'German' },
  { code: 'fr', name: 'French' },
  { code: 'es', name: 'Spanish' },
  { code: 'it', name: 'Italian' },
  { code: 'tr', name: 'Turkish' },
  { code: 'ar', name: 'Arabic' },
  { code: 'zh-Hans', name: 'Chinese (Simplified)' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'ru', name: 'Russian' },
];

// Dil kodundan dil adını bulan yardımcı fonksiyon
const getLanguageName = (code) => {
  const language = LANGUAGES.find((lang) => lang.code === code);
  return language ? language.name : code;
};

const generateId = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export function EditCourseView() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { getCourseById, updateCourse, translateCourse } = useCourses();
  const { getQuizzes } = useQuizzes();
  const { getUsecases } = useUsecases();
  const { uploadMedia } = useMedia();
  const [courseData, setCourseData] = useState(null);
  const [originalCourseData, setOriginalCourseData] = useState(null);
  const [quizzes, setQuizzes] = useState([]);
  const [loadingQuizzes, setLoadingQuizzes] = useState(false);
  const [errors, setErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);
  const editorRefs = React.useRef({});

  // URL'den dil parametresini al
  const queryParams = new URLSearchParams(location.search);
  const langParam = queryParams.get('lang');
  const [currentLanguage, setCurrentLanguage] = useState(langParam || 'original');

  // Translation state
  const [translateDialogOpen, setTranslateDialogOpen] = useState(false);
  const [targetLanguage, setTargetLanguage] = useState('');
  const [translating, setTranslating] = useState(false);

  // Usecase ile ilgili state'ler
  const [usecases, setUsecases] = useState([]);
  const [loadingUsecases, setLoadingUsecases] = useState(false);
  const [usecaseSearch, setUsecaseSearch] = useState('');
  const [usecaseSearchDebounced, setUsecaseSearchDebounced] = useState('');
  const [usecaseInputValues, setUsecaseInputValues] = useState({});
  const [usecasePage, setUsecasePage] = useState(1);
  const [usecaseOptions, setUsecaseOptions] = useState([]);
  const [hasMoreUsecases, setHasMoreUsecases] = useState(true);
  const ITEMS_PER_PAGE = 300;

  // Çeviri silme state'leri ve fonksiyonlar
  const [deleteTranslationDialogOpen, setDeleteTranslationDialogOpen] = useState(false);
  const [selectedLanguageToDelete, setSelectedLanguageToDelete] = useState('');
  const [availableTranslations, setAvailableTranslations] = useState([]);
  const [deletingTranslation, setDeletingTranslation] = useState(false);

  useEffect(() => {
    const fetchCourse = async () => {
      try {
        const data = await getCourseById(id);
        setOriginalCourseData(data);

        // Playground tiplerini düzelt
        if (data.chapters && Array.isArray(data.chapters)) {
          // Chapter order değerlerini kontrol et
          data.chapters.forEach((chapter, chapterIndex) => {
            if (!chapter.order) {
              chapter.order = chapterIndex + 1;
            }

            if (chapter.topics && Array.isArray(chapter.topics)) {
              // Topic order değerlerini kontrol et
              chapter.topics.forEach((topic, topicIndex) => {
                if (!topic.order) {
                  topic.order = topicIndex + 1;
                }

                if (topic.contentBlocks && Array.isArray(topic.contentBlocks)) {
                  topic.contentBlocks.forEach((block) => {
                    // "playground" tipini düzelt
                    if (block.type === 'playground') {
                      block.type = 'text'; // Tipi 'text' olarak değiştir
                      block.displayType = 'playground'; // displayType'ı playground olarak belirle

                      // Eğer playground_type yoksa ve belirli bir tipe sahip değilse varsayılan olarak gpt ata
                      if (!block.playground_type) {
                        block.playground_type = 'gpt';
                      }
                    }

                    // Usecase tiplerini düzelt
                    if (
                      block.type === 'Usecase' ||
                      block.type === 'USECASE' ||
                      block.type === 'usecase'
                    ) {
                      block.type = 'text';
                      block.displayType = 'usecase';
                    }
                  });
                }
              });
            }
          });
        } else {
          // Eğer chapters yoksa veya dizi değilse, boş bir dizi olarak başlat
          data.chapters = [];
        }

        // Quiz verilerini yükle
        const quizData = await getQuizzes();
        setQuizzes(quizData);

        // Quizleri id'lerle eşleştirmek için harita oluştur
        const quizMap = {};
        quizData.forEach((quiz) => {
          if (quiz._id) {
            quizMap[quiz._id] = quiz;
          }
        });

        // Kurs verisindeki tüm quizleri doldur
        const processedData = { ...data };

        // Chapters içindeki quiz içeriklerini doldur
        if (processedData.chapters && Array.isArray(processedData.chapters)) {
          processedData.chapters = processedData.chapters.map((chapter) => {
            if (chapter.topics && Array.isArray(chapter.topics)) {
              chapter.topics = chapter.topics.map((topic) => {
                if (topic.contentBlocks && Array.isArray(topic.contentBlocks)) {
                  topic.contentBlocks = topic.contentBlocks.map((block) => {
                    // Eğer bu bir quiz ise ve id varsa, içeriğini doldur
                    if (block.type === 'quiz' && block.quiz) {
                      // Quiz id'sini bul (farklı alanlarda olabilir)
                      const quizId = block.quiz._id || block.quiz.id || block.quiz.quiz_id;

                      if (quizId) {
                        // Quiz yapısını sadeleştir - sadece quiz_id'yi tut
                        block.quiz = {
                          quiz_id: quizId,
                        };
                      } else {
                        block.quiz = { quiz_id: '' };
                      }
                    }
                    return block;
                  });
                }
                return topic;
              });
            }
            return chapter;
          });
        }

        // Translations verilerinden quiz id'lerini ana yapıya aktarma işlemi
        if (processedData.translations) {
          Object.keys(processedData.translations).forEach((lang) => {
            if (processedData.translations[lang].chapters) {
              processedData.translations[lang].chapters.forEach((chapter) => {
                if (chapter.topics) {
                  chapter.topics.forEach((topic) => {
                    if (topic.contentBlocks) {
                      topic.contentBlocks.forEach((block) => {
                        if (block.type === 'quiz' && block.quiz) {
                          const quizId = block.quiz._id || block.quiz.id || block.quiz.quiz_id;
                          if (quizId) {
                            block.quiz = {
                              quiz_id: quizId,
                            };
                          } else {
                            block.quiz = { quiz_id: '' };
                          }
                        }
                      });
                    }
                  });
                }
              });
            }
          });
        }

        // Eğer dil parametresi varsa ve çevirisi mevcutsa, o dildeki çeviriyi göster
        if (langParam && processedData.translations && processedData.translations[langParam]) {
          // Orijinal veriyi koru, ancak çevirisi olan alanları güncelle
          const translatedData = {
            ...processedData,
            ...processedData.translations[langParam],
            // Çevirisi olmayan alanları koru
            _id: processedData._id,
            slug: processedData.slug,
            createdAt: processedData.createdAt,
            updatedAt: processedData.updatedAt,
            isPublished: processedData.isPublished,
            certificateAvailable: processedData.certificateAvailable,
            rating: processedData.rating,
            coverImage: processedData.coverImage,
            providerImage:
              processedData.providerImage || processedData.translations[langParam].providerImage,
            provider: processedData.provider || processedData.translations[langParam].provider,
            translations: processedData.translations,
          };
          setCourseData(translatedData);
        } else {
          setCourseData(processedData);
        }
      } catch (err) {
        console.error('Kurs yükleme hatası:', err);
        toast.error('Error fetching course');
        navigate(paths.cds.lms.courses.root);
      }
    };

    const loadUsecases = async () => {
      setLoadingUsecases(true);
      try {
        const response = await getUsecases(1, '', '', '', -1);
        if (response?.data?.useCases) {
          setUsecases(response.data.useCases);
          setUsecaseOptions(response.data.useCases);
          setHasMoreUsecases(response.data.useCases.length === ITEMS_PER_PAGE);
        }
      } catch (error) {
        console.error('Error loading usecases:', error);
        toast.error('Failed to load usecases');
      } finally {
        setLoadingUsecases(false);
      }
    };

    fetchCourse();
    loadUsecases();
  }, [getCourseById, getQuizzes, getUsecases, id, navigate, langParam]);

  // Çeviri listesini güncelle
  useEffect(() => {
    if (originalCourseData?.translations) {
      setAvailableTranslations(Object.keys(originalCourseData.translations));
    }
  }, [originalCourseData]);

  // Translation handlers
  const openTranslateDialog = () => {
    setTranslateDialogOpen(true);
  };

  const closeTranslateDialog = () => {
    setTranslateDialogOpen(false);
    setTargetLanguage('');
  };

  const handleLanguageChange = (event) => {
    setTargetLanguage(event.target.value);
  };

  const handleTranslate = async () => {
    if (!targetLanguage) {
      toast.error('Please select a language');
      return;
    }

    setTranslating(true);
    try {
      let dataToSend = { ...courseData };

      // Outcomes için text alanını kontrol et
      if (dataToSend.outcomes && Array.isArray(dataToSend.outcomes)) {
        dataToSend.outcomes = dataToSend.outcomes.map((outcome) => {
          if (!outcome.text || outcome.text.trim() === '') {
            return { ...outcome, text: 'Learning outcome' };
          }
          return outcome;
        });
      }
      const result = await translateCourse(id, targetLanguage);
      if (result.success) {
        toast.success('Course translated successfully');
        // Update the course data with the new translations
        setOriginalCourseData(result.data);

        // Eğer şu an çevirisi yapılan dili görüntülüyorsak, içeriği güncelle
        if (currentLanguage === targetLanguage) {
          const translatedData = {
            ...result.data,
            ...result.data.translations[targetLanguage],
            // Çevirisi olmayan alanları koru
            _id: result.data._id,
            slug: result.data.slug,
            createdAt: result.data.createdAt,
            updatedAt: result.data.updatedAt,
            isPublished: result.data.isPublished,
            certificateAvailable: result.data.certificateAvailable,
            rating: result.data.rating,
            coverImage: result.data.coverImage,
            providerImage:
              result.data.providerImage || result.data.translations[targetLanguage].providerImage,
            provider: result.data.provider || result.data.translations[targetLanguage].provider,
            translations: result.data.translations,
          };
          setCourseData(translatedData);
        } else {
          setCourseData(result.data);
        }

        closeTranslateDialog();
      } else {
        toast.error(result.message || 'Translation failed');
      }
    } catch (error) {
      toast.error('Error translating course');
      console.error('Translation error:', error);
    } finally {
      setTranslating(false);
    }
  };

  const handleBasicInfoChange = (e) => {
    const { name, value } = e.target;

    // Çeviri modu kontrolü
    if (langParam && courseData.translations && courseData.translations[langParam]) {
      setCourseData((prev) => {
        // Çeviri nesnesini güncelle
        const updatedTranslations = {
          ...prev.translations,
          [langParam]: {
            ...prev.translations[langParam],
            [name]: value,
          },
        };

        // Ana veri için her zaman korunması gereken alanlar varsa onları yönet
        // (Bu kısım gereksinime göre değiştirilebilir)
        if (
          name === 'provider' ||
          name === 'providerImage' ||
          name === 'coverImage' ||
          name === 'title' ||
          name === 'description' ||
          name === 'category' ||
          name === 'level' ||
          name === 'duration' ||
          name === 'skills' ||
          name === 'outcomes'
        ) {
          return {
            ...prev,
            [name]: value, // Ana veride de güncelle
            translations: updatedTranslations,
          };
        }

        return {
          ...prev,
          translations: updatedTranslations,
        };
      });
    } else {
      // Çeviri modu değilse doğrudan ana veriyi güncelle
      setCourseData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }

    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleChapterAdd = () => {
    setCourseData((prev) => ({
      ...prev,
      chapters: [
        ...prev.chapters,
        {
          title: '',
          description: '',
          order: prev.chapters.length + 1,
          isLocked: true,
          topics: [
            {
              title: '',
              description: '',
              order: 1,
              contentBlocks: [],
            },
          ],
        },
      ],
    }));
  };

  const handleChapterChange = (index, field, value) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, i) =>
        i === index ? { ...chapter, [field]: value } : chapter
      ),
    }));

    if (errors.chapters && errors.chapters[index] && errors.chapters[index][field]) {
      const newErrors = { ...errors };
      newErrors.chapters[index][field] = undefined;
      setErrors(newErrors);
    }
  };

  const handleChapterDelete = (chapterIndex) => {
    setCourseData((prev) => {
      const newChapters = prev.chapters.filter((_, idx) => idx !== chapterIndex);
      return {
        ...prev,
        chapters: newChapters.map((chapter, idx) => ({
          ...chapter,
          order: idx + 1,
        })),
      };
    });
  };

  const handleTopicAdd = (chapterIndex) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, idx) => {
        if (idx === chapterIndex) {
          return {
            ...chapter,
            topics: [
              ...chapter.topics,
              {
                title: '',
                description: '',
                order: chapter.topics.length + 1,
                contentBlocks: [],
              },
            ],
          };
        }
        return chapter;
      }),
    }));
  };

  const handleTopicChange = (chapterIndex, topicIndex, field, value) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, chIdx) => {
        if (chIdx === chapterIndex) {
          return {
            ...chapter,
            topics: chapter.topics.map((topic, topIdx) => {
              if (topIdx === topicIndex) {
                return { ...topic, [field]: value };
              }
              return topic;
            }),
          };
        }
        return chapter;
      }),
    }));

    if (
      errors.chapters &&
      errors.chapters[chapterIndex] &&
      errors.chapters[chapterIndex].topics &&
      errors.chapters[chapterIndex].topics[topicIndex] &&
      errors.chapters[chapterIndex].topics[topicIndex][field]
    ) {
      const newErrors = { ...errors };
      newErrors.chapters[chapterIndex].topics[topicIndex][field] = undefined;
      setErrors(newErrors);
    }
  };

  const handleTopicDelete = (chapterIndex, topicIndex) => {
    setCourseData((prev) => {
      const newChapters = prev.chapters.map((chapter, idx) => {
        if (idx === chapterIndex) {
          const newTopics = chapter.topics.filter((_, tIdx) => tIdx !== topicIndex);
          return {
            ...chapter,
            topics: newTopics.map((topic, idx) => ({
              ...topic,
              order: idx + 1,
            })),
          };
        }
        return chapter;
      });
      return {
        ...prev,
        chapters: newChapters,
      };
    });
  };

  const handleContentBlockAdd = (chapterIndex, topicIndex, blockType = 'text') => {
    // Eğer playground bloğu eklenmek isteniyorsa, gerekli değişiklikleri yap
    let finalBlockType = blockType;
    let displayType = null;
    let playground_type = null;

    // Playground tiplerini kontrol et
    if (
      blockType === 'playground-gpt' ||
      blockType === 'playground-dalle' ||
      blockType === 'playground-heygen'
    ) {
      finalBlockType = 'text'; // Type'ı her zaman 'text' olarak ayarla
      displayType = 'playground';

      // playground_type'ı belirle
      if (blockType === 'playground-gpt') {
        playground_type = 'gpt';
      } else if (blockType === 'playground-dalle') {
        playground_type = 'dalle';
      } else if (blockType === 'playground-heygen') {
        playground_type = 'heygen';
      }
    } else if (blockType === 'usecase') {
      finalBlockType = 'text';
      displayType = 'usecase';
    }

    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, chIdx) => {
        if (chIdx === chapterIndex) {
          return {
            ...chapter,
            topics: chapter.topics.map((topic, topIdx) => {
              if (topIdx === topicIndex) {
                return {
                  ...topic,
                  contentBlocks: [
                    ...topic.contentBlocks,
                    {
                      type: finalBlockType,
                      displayType: displayType,
                      title: '',
                      icon: 'default',
                      description: '',
                      order: topic.contentBlocks.length,
                      textContent:
                        finalBlockType === 'text'
                          ? {
                              content: '',
                              formatting: 'markdown',
                            }
                          : null,
                      videoContent:
                        finalBlockType === 'video'
                          ? {
                              type: 'youtube',
                              youtubeUrl: '',
                              hlsData: null,
                              thumbnail: '',
                            }
                          : null,
                      quiz:
                        finalBlockType === 'quiz'
                          ? {
                              id: '',
                              questions: [],
                            }
                          : null,
                      usecase_id: null,
                      usecase_slug: null,
                      usecase_type: blockType === 'usecase' ? 'text' : null,
                      pdfContent:
                        finalBlockType === 'pdf'
                          ? {
                              url: '',
                              fileName: '',
                              fileSize: 0,
                            }
                          : null,
                      playground_type: playground_type,
                      initialPrompt:
                        blockType === 'playground-gpt' || blockType === 'playground-dalle'
                          ? ''
                          : null,
                      temperature: blockType === 'playground-gpt' ? 0.7 : null,
                      topP: blockType === 'playground-gpt' ? 1 : null,
                      frequencyPenalty: blockType === 'playground-gpt' ? 0 : null,
                      presencePenalty: blockType === 'playground-gpt' ? 0 : null,
                      heygen_id: blockType === 'playground-heygen' ? '' : null,
                      form_id: finalBlockType === 'form' ? null : null,
                    },
                  ],
                };
              }
              return topic;
            }),
          };
        }
        return chapter;
      }),
    }));
  };

  const handleContentBlockChange = (chapterIndex, topicIndex, blockIndex, field, value) => {
    const updatedChapters = [...courseData.chapters];
    const block = updatedChapters[chapterIndex].topics[topicIndex].contentBlocks[blockIndex];

    // Özel durum: displayType değişikliği
    if (field === 'displayType') {
      block.displayType = value;

      // Eğer displayType usecase ise ve type text değilse, type'ı text olarak ayarla
      if (value === 'usecase' && block.type !== 'text') {
        block.type = 'text';
      }

      // Eğer displayType playground ise ve playground_type yoksa, playground_type'ı gpt olarak ayarla
      if (value === 'playground' && !block.playground_type) {
        block.playground_type = 'gpt';
      }

      setCourseData({ ...courseData, chapters: updatedChapters });
      return;
    }

    // Özel durum: usecase değişikliği
    if (field === 'usecase') {
      // Tüm usecase alanlarını güncelle
      block.usecase_slug = value.usecase_slug;
      block.usecase_type = value.usecase_type;
      block.type = value.type || 'text';
      block.displayType = value.displayType || 'usecase';

      // Eğer usecase seçildiyse, usecase_id'yi de ayarla
      if (value.usecase_slug) {
        // usecase_id'yi bul
        const usecase = usecases.find((u) => u.slug === value.usecase_slug);
        if (usecase) {
          block.usecase_id = usecase._id;
        }
      } else {
        block.usecase_id = null;
      }

      setCourseData({ ...courseData, chapters: updatedChapters });
      return;
    }

    // Tip değişikliği kontrolü
    if (field === 'type') {
      const contentType = value;

      // Playground tipi kontrolü
      if (contentType.startsWith('playground-')) {
        const playgroundType = contentType.replace('playground-', '');

        block.type = 'text';
        block.displayType = 'playground';
        block.playground_type = playgroundType;
        block.textContent = { content: '', formatting: 'markdown' };
        block.videoContent = null;
        block.quiz = null;
        block.usecase_id = null;
        block.usecase_slug = null;
        block.usecase_type = null;
        block.pdfContent = null;
        block.initialPrompt = '';
        block.temperature = 0.7;
        block.topP = 1;
        block.frequencyPenalty = 0;
        block.presencePenalty = 0;
        block.heygen_id = null;
        block.form_id = null;
        block.form_data = null;
        setCourseData({ ...courseData, chapters: updatedChapters });
        return;
      }

      // Usecase tipi kontrolü
      if (contentType === 'usecase') {
        block.type = 'text';
        block.displayType = 'usecase';
        block.textContent = { content: '', formatting: 'markdown' };
        block.videoContent = null;
        block.quiz = null;
        block.usecase_id = null;
        block.usecase_slug = null;
        block.usecase_type = null;
        block.pdfContent = null;
        block.playground_type = null;
        block.initialPrompt = null;
        block.temperature = null;
        block.topP = null;
        block.frequencyPenalty = null;
        block.presencePenalty = null;
        block.heygen_id = null;
        block.form_id = null;
        block.form_data = null;
        setCourseData({ ...courseData, chapters: updatedChapters });
        return;
      }

      // Normal içerik tipi seçildi

      block.type = contentType;
      block.displayType = null;
      block.textContent = { content: '', formatting: 'markdown' };
      block.videoContent = null;
      block.quiz = null;
      block.usecase_id = null;
      block.usecase_slug = null;
      block.usecase_type = null;
      block.pdfContent = null;
      block.playground_type = null;
      block.initialPrompt = null;
      block.temperature = null;
      block.topP = null;
      block.frequencyPenalty = null;
      block.presencePenalty = null;
      block.heygen_id = null;
      block.form_id = null;
      block.form_data = null;
      setCourseData({ ...courseData, chapters: updatedChapters });
      return;
    }

    // Diğer alan değişiklikleri
    if (field === 'textContent') {
      block.textContent = value;
    } else if (field === 'videoContent') {
      block.videoContent = value;
    } else if (field === 'quiz') {
      // Quiz yapısını sadeleştir - sadece quiz_id'yi tut
      const quizId = value._id || value.id || value.quiz_id;
      block.quiz = {
        quiz_id: quizId || '',
      };
    } else if (field === 'pdfContent') {
      block.pdfContent = value;
    } else if (field === 'playground_type') {
      block.playground_type = value;
    } else if (field === 'initialPrompt') {
      block.initialPrompt = value;
    } else if (field === 'temperature') {
      block.temperature = value;
    } else if (field === 'topP') {
      block.topP = value;
    } else if (field === 'frequencyPenalty') {
      block.frequencyPenalty = value;
    } else if (field === 'presencePenalty') {
      block.presencePenalty = value;
    } else if (field === 'heygen_id') {
      block.heygen_id = value;
    } else if (field === 'form_id') {
      block.form_id = value;
    } else if (field === 'form_data') {
      block.form_data = value;
    } else {
      block[field] = value;
    }

    setCourseData({ ...courseData, chapters: updatedChapters });
  };

  const handleContentBlockDelete = (chapterIndex, topicIndex, blockIndex) => {
    setCourseData((prev) => {
      const updatedCourseData = { ...prev };
      updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks.splice(
        blockIndex,
        1
      );
      return updatedCourseData;
    });
  };

  // Usecase autocomplete için gerekli fonksiyonlar
  const updateUsecaseInputValue = (chapterIndex, topicIndex, blockIndex, value) => {
    setUsecaseInputValues((prev) => ({
      ...prev,
      [`${chapterIndex}-${topicIndex}-${blockIndex}`]: value,
    }));
  };

  const getUsecaseInputValue = (chapterIndex, topicIndex, blockIndex) => {
    const key = `${chapterIndex}-${topicIndex}-${blockIndex}`;
    return usecaseInputValues[key] || '';
  };

  const handleUsecaseSelectScroll = () => {
    if (!hasMoreUsecases || loadingUsecases) return;

    const nextPage = usecasePage + 1;
    setUsecasePage(nextPage);

    const fetchMoreUsecases = async () => {
      try {
        setLoadingUsecases(true);
        const response = await getUsecases(nextPage, usecaseSearchDebounced, '', '', -1);

        if (response?.data?.useCases?.length) {
          setUsecases((prev) => [...prev, ...response.data.useCases]);
          setUsecaseOptions((prev) => [...prev, ...response.data.useCases]);
          setHasMoreUsecases(response.data.useCases.length > 0);
        } else {
          setHasMoreUsecases(false);
        }
      } catch (error) {
        console.error('Error loading more usecases:', error);
        toast.error('Error loading more usecases');
      } finally {
        setLoadingUsecases(false);
      }
    };

    fetchMoreUsecases();
  };

  // usecase search için debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setUsecaseSearchDebounced(usecaseSearch);
    }, 500);

    return () => clearTimeout(timer);
  }, [usecaseSearch]);

  // usecase search değiştiğinde yeni arama yap
  useEffect(() => {
    const searchUsecases = async () => {
      if (usecaseSearchDebounced === '') return;

      setLoadingUsecases(true);
      try {
        const response = await getUsecases(1, usecaseSearchDebounced, '', '', -1);
        if (response?.data?.useCases) {
          setUsecaseOptions(response.data.useCases);
          setHasMoreUsecases(response.data.useCases.length === ITEMS_PER_PAGE);
        }
      } catch (error) {
        console.error('Error searching usecases:', error);
      } finally {
        setLoadingUsecases(false);
      }
    };

    searchUsecases();
  }, [usecaseSearchDebounced, getUsecases]);

  const validateForm = () => {
    const newErrors = {};

    if (!courseData.title.trim()) {
      newErrors.title = 'Course title is required';
    }

    if (!courseData.description.trim()) {
      newErrors.description = 'Course description is required';
    }

    if (!courseData.category) {
      newErrors.category = 'Category selection is required';
    }

    if (!courseData.level) {
      newErrors.level = 'Level selection is required';
    }
    console.log('courseData', courseData.duration);
    if (!courseData.duration.trim()) {
      newErrors.duration = 'Course duration is required';
    }

    // Chapter and topic validations
    const chapterErrors = [];
    courseData.chapters.forEach((chapter, chapterIndex) => {
      const chapterError = {};
      if (!chapter.title.trim()) {
        chapterError.title = 'Chapter title is required';
      }

      const topicErrors = [];
      chapter.topics.forEach((topic, topicIndex) => {
        const topicError = {};
        if (!topic.title.trim()) {
          topicError.title = 'Topic title is required';
        }
        if (Object.keys(topicError).length > 0) {
          topicErrors[topicIndex] = topicError;
        }
      });

      if (topicErrors.length > 0) {
        chapterError.topics = topicErrors;
      }

      if (Object.keys(chapterError).length > 0) {
        chapterErrors[chapterIndex] = chapterError;
      }
    });

    if (chapterErrors.length > 0) {
      newErrors.chapters = chapterErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Göndermeden önce tüm içerik bloklarında "playground" tipini kontrol et ve düzelt
    const dataToSend = { ...courseData };
    if (dataToSend.chapters && Array.isArray(dataToSend.chapters)) {
      dataToSend.chapters.forEach((chapter, chapterIndex) => {
        // Chapter order değerini ekle
        if (!chapter.order) {
          chapter.order = chapterIndex + 1;
        }

        if (chapter.topics && Array.isArray(chapter.topics)) {
          chapter.topics.forEach((topic, topicIndex) => {
            // Topic order değerini ekle
            if (!topic.order) {
              topic.order = topicIndex + 1;
            }

            if (topic.contentBlocks && Array.isArray(topic.contentBlocks)) {
              topic.contentBlocks.forEach((block) => {
                // "playground" tipini düzelt
                if (block.type === 'playground') {
                  block.type = 'text'; // Tipi 'text' olarak değiştir
                  block.displayType = 'playground'; // displayType'ı playground olarak belirle

                  // Eğer playground_type yoksa ve belirli bir tipe sahip değilse varsayılan olarak gpt ata
                  if (!block.playground_type) {
                    block.playground_type = 'gpt';
                  }
                }

                // Usecase tipini düzelt (büyük/küçük harf duyarlılığı hatası)
                if (
                  block.type === 'Usecase' ||
                  block.type === 'USECASE' ||
                  block.type === 'usecase'
                ) {
                  block.type = 'text'; // Tipi 'text' olarak değiştir
                  block.displayType = 'usecase'; // displayType'ı usecase olarak belirle
                }

                // DisplayType usecase ise, type'ın text olduğundan emin ol
                if (block.displayType === 'usecase' && block.type !== 'text') {
                  block.type = 'text';
                }
              });
            }
          });
        }
      });
    }

    setSubmitting(true);

    try {
      // Kursun üst bilgilerini güncelle

      await updateCourse(id, dataToSend);
      toast.success('Course updated successfully');
      //navigate(paths.cds.lms.courses.root);
    } catch (error) {
      console.error('Error updating course:', error);
      let errorMessage = 'Failed to update course';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // Silme dialogunu açma
  const openDeleteTranslationDialog = () => {
    setDeleteTranslationDialogOpen(true);
  };

  // Silme dialogunu kapatma
  const closeDeleteTranslationDialog = () => {
    setDeleteTranslationDialogOpen(false);
    setSelectedLanguageToDelete('');
  };

  // Silinecek dili değiştirme
  const handleDeleteLanguageChange = (event) => {
    setSelectedLanguageToDelete(event.target.value);
  };

  // Çeviri silme işlemi
  const handleDeleteTranslation = async () => {
    if (!selectedLanguageToDelete) {
      toast.error('Please select a language to delete');
      return;
    }

    setDeletingTranslation(true);
    try {
      // Mevcut kurs verisini al
      const updatedTranslations = { ...originalCourseData.translations };

      // Seçilen dili translations nesnesinden kaldır
      delete updatedTranslations[selectedLanguageToDelete];

      // Güncelleme için veri hazırla
      const updateData = {
        translations: updatedTranslations,
      };

      // API'ye gönder
      const result = await updateCourse(id, updateData);

      if (result) {
        toast.success(
          `${getLanguageName(selectedLanguageToDelete)} translation deleted successfully`
        );

        // Eğer silinmiş dili görüntülüyorsak, orijinale dön
        if (currentLanguage === selectedLanguageToDelete) {
          setCourseData(originalCourseData);
          navigate(`${paths.cds.lms.courses.edit(id)}`);
        } else {
          // Sadece state'i güncelle
          setOriginalCourseData(result);
        }

        // Çeviriler listesini güncelle
        setAvailableTranslations(Object.keys(updatedTranslations));

        closeDeleteTranslationDialog();
      } else {
        toast.error('Failed to delete translation');
      }
    } catch (error) {
      console.error('Error deleting translation:', error);
      toast.error('An error occurred while deleting the translation');
    } finally {
      setDeletingTranslation(false);
    }
  };

  if (!courseData) {
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
        <Typography variant="h4">
          {currentLanguage === 'original'
            ? 'Edit Course'
            : `Edit ${getLanguageName(currentLanguage)} Translation`}
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<TranslateIcon />}
            onClick={openTranslateDialog}
          >
            Translate
          </Button>
          {availableTranslations.length > 0 && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={openDeleteTranslationDialog}
            >
              Delete Translation
            </Button>
          )}
          <Button variant="outlined" onClick={() => navigate(paths.cds.lms.courses.root)}>
            Cancel
          </Button>
        </Stack>
      </Stack>

      {originalCourseData?.translations &&
        Object.keys(originalCourseData.translations).length > 0 && (
          <Box mb={3}>
            <Typography variant="subtitle1" gutterBottom>
              Available Translations:
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap">
              {Object.keys(originalCourseData.translations)
                .filter((langCode) => langCode !== currentLanguage)
                .map((langCode) => (
                  <Chip
                    key={langCode}
                    label={getLanguageName(langCode)}
                    color="default"
                    onClick={() =>
                      (window.location.href = `${paths.cds.lms.courses.edit(courseData._id)}?lang=${langCode}`)
                    }
                    sx={{ mb: 1 }}
                  />
                ))}
            </Stack>
          </Box>
        )}

      {currentLanguage !== 'original' && (
        <Alert severity="info" sx={{ mb: 3 }}>
          You are editing the {getLanguageName(currentLanguage)} translation. Changes will only
          affect the translated content.
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <BasicInformation
              courseData={courseData}
              handleBasicInfoChange={handleBasicInfoChange}
              errors={errors}
            />
          </Grid>

          <Grid item xs={12}>
            <CourseMedia courseData={courseData} handleBasicInfoChange={handleBasicInfoChange} />
          </Grid>

          {/* <Grid item xs={12}>
            <CourseSettings courseData={courseData} setCourseData={setCourseData} />
          </Grid> */}

          <Grid item xs={12}>
            <Instructors courseData={courseData} setCourseData={setCourseData} />
          </Grid>

          <Grid item xs={12}>
            <ChaptersAndTopics
              courseData={courseData}
              handleChapterAdd={handleChapterAdd}
              handleChapterChange={handleChapterChange}
              handleChapterDelete={handleChapterDelete}
              handleTopicAdd={handleTopicAdd}
              handleTopicChange={handleTopicChange}
              handleTopicDelete={handleTopicDelete}
              handleContentBlockAdd={handleContentBlockAdd}
              handleContentBlockChange={handleContentBlockChange}
              handleContentBlockDelete={handleContentBlockDelete}
              quizzes={quizzes}
              loadingQuizzes={loadingQuizzes}
              errors={errors}
              editorRefs={editorRefs}
              uploadMedia={uploadMedia}
              renderEditor={(chapterIndex, topicIndex, blockIndex, block) => (
                <Box sx={{ mt: 2, border: '1px solid #e0e0e0', minHeight: '300px' }}>
                  <BundledEditor
                    onInit={(_evt, editorInstance) => {
                      editorRefs.current[`editor-${chapterIndex}-${topicIndex}-${blockIndex}`] =
                        editorInstance;
                    }}
                    value={block.textContent?.content || ''}
                    init={{
                      height: 500,
                      menubar: false,
                      plugins: [
                        'advlist',
                        'anchor',
                        'autolink',
                        'image',
                        'media',
                        'link',
                        'lists',
                        'searchreplace',
                        'codesample',
                        'table',
                        'wordcount',
                      ],
                      toolbar:
                        'undo redo | blocks | ' +
                        'bold italic forecolor | codesample | alignleft aligncenter ' +
                        'alignright alignjustify | bullist numlist outdent indent | ' +
                        'removeformat | image | media ',
                      content_style:
                        'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                      base_url: '/tinymce',
                      skin: 'oxide',
                      skin_url: '/tinymce/skins/ui/oxide',
                      content_css: '/tinymce/skins/content/default/content.css',
                      image_advtab: true,
                      image_dimensions: true,
                      media_alt_source: true,
                      media_poster: true,
                      inline: false,
                      dialog_type: 'window',
                      media_filter_html: false,
                      extended_valid_elements:
                        'iframe[src|frameborder|style|scrolling|class|width|height|name|align]',
                      convert_urls: false,
                      relative_urls: false,
                      remove_script_host: false,
                    }}
                    onEditorChange={(content) => {
                      handleContentBlockChange(
                        chapterIndex,
                        topicIndex,
                        blockIndex,
                        'textContent',
                        {
                          content,
                          formatting: 'html',
                        }
                      );
                    }}
                  />
                </Box>
              )}
              usecases={usecases}
              loadingUsecases={loadingUsecases}
              usecaseOptions={usecaseOptions}
              updateUsecaseInputValue={updateUsecaseInputValue}
              getUsecaseInputValue={getUsecaseInputValue}
              handleUsecaseSelectScroll={handleUsecaseSelectScroll}
              setUsecaseSearch={setUsecaseSearch}
            />
          </Grid>

          <Grid item xs={12}>
            <Stack direction="row" justifyContent="flex-end" spacing={2}>
              <Button variant="outlined" onClick={() => navigate(paths.cds.lms.courses.root)}>
                Cancel
              </Button>
              <LoadingButton loading={submitting} type="submit" variant="contained" color="primary">
                {currentLanguage === 'original'
                  ? 'Update Course'
                  : `Update ${getLanguageName(currentLanguage)} Translation`}
              </LoadingButton>
            </Stack>
          </Grid>
        </Grid>
      </form>

      {/* Translation Dialog */}
      <Dialog open={translateDialogOpen} onClose={closeTranslateDialog}>
        <DialogTitle>Translate Course</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Select a target language to translate the course content. This will translate all text
            content including title, description, chapters, topics, and content blocks.
          </DialogContentText>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth>
              <InputLabel id="target-language-label">Target Language</InputLabel>
              <Select
                labelId="target-language-label"
                value={targetLanguage}
                onChange={handleLanguageChange}
                label="Target Language"
              >
                {LANGUAGES.map((lang) => (
                  <MenuItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeTranslateDialog} disabled={translating}>
            Cancel
          </Button>
          <LoadingButton
            onClick={handleTranslate}
            loading={translating}
            variant="contained"
            disabled={!targetLanguage}
            startIcon={<TranslateIcon />}
          >
            Translate
          </LoadingButton>
        </DialogActions>
      </Dialog>

      {/* Çeviri Silme Dialog */}
      <Dialog open={deleteTranslationDialogOpen} onClose={closeDeleteTranslationDialog}>
        <DialogTitle>Delete Translation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Please select the language translation you want to delete. This action cannot be undone.
          </DialogContentText>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel id="delete-language-select-label">Language</InputLabel>
            <Select
              labelId="delete-language-select-label"
              id="delete-language-select"
              value={selectedLanguageToDelete}
              label="Language"
              onChange={handleDeleteLanguageChange}
            >
              {availableTranslations.map((langCode) => (
                <MenuItem key={langCode} value={langCode}>
                  {getLanguageName(langCode)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteTranslationDialog}>Cancel</Button>
          <LoadingButton
            loading={deletingTranslation}
            variant="contained"
            color="error"
            onClick={handleDeleteTranslation}
            disabled={!selectedLanguageToDelete}
          >
            Delete
          </LoadingButton>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
