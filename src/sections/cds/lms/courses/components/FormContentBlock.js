import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Autocomplete,
  CircularProgress,
  Paper,
  Divider,
} from '@mui/material';
import axios from 'axios';
import { CONFIG } from 'src/config-global';

export function FormContentBlock({ contentBlock, onChange, chapterIndex, topicIndex, blockIndex }) {
  const [forms, setForms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectedForm, setSelectedForm] = useState(null);
  const authToken = localStorage.getItem('accessToken');
  const urlParams = new URLSearchParams(window.location.search);

  // Form verilerini yükle
  useEffect(() => {
    const loadForms = async () => {
      setLoading(true);
      try {
        const response = await axios.get(`${CONFIG.site.serverUrl}/forms/list?page=1&limit=100`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });
        setForms(response.data.data.forms);
      } catch (error) {
        console.error('Error loading forms:', error);
      } finally {
        setLoading(false);
      }
    };

    loadForms();
  }, [authToken]);

  // Seçili form değiştiğinde
  useEffect(() => {
    if (contentBlock.form_id) {
      const form = forms.find((f) => f._id === contentBlock.form_id);
      if (form) {
        setSelectedForm(form);
      }
    }
  }, [contentBlock.form_id, forms]);

  // Form seçildiğinde
  const handleFormChange = (event, newValue) => {
    if (newValue) {
      onChange(chapterIndex, topicIndex, blockIndex, 'form_id', newValue._id);
      setSelectedForm(newValue);
    } else {
      onChange(chapterIndex, topicIndex, blockIndex, 'form_id', null);
      setSelectedForm(null);
    }
  };

  return (
    <Box sx={{ mt: 2 }}>
      <TextField
        fullWidth
        size="small"
        label="Block Title"
        value={contentBlock.title || ''}
        onChange={(e) => onChange(chapterIndex, topicIndex, blockIndex, 'title', e.target.value)}
        sx={{ mb: 2, ml: 2 }}
      />

      <TextField
        fullWidth
        size="small"
        label="Block Description"
        value={contentBlock.description || ''}
        onChange={(e) =>
          onChange(chapterIndex, topicIndex, blockIndex, 'description', e.target.value)
        }
        multiline
        rows={2}
        sx={{ mb: 2, ml: 2 }}
      />

      <Autocomplete
        fullWidth
        size="small"
        options={forms}
        loading={loading}
        getOptionLabel={(option) => option.title}
        value={selectedForm}
        onChange={handleFormChange}
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Select Form"
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {loading ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        sx={{ mb: 2, ml: 2 }}
      />

      {selectedForm && (
        <Paper variant="outlined" sx={{ p: 2, mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Form Preview
          </Typography>
          <Typography variant="h6">
            {urlParams.get('lang')
              ? selectedForm?.translations[urlParams.get('lang')]?.title
              : selectedForm.title}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {urlParams.get('lang')
              ? selectedForm?.translations[urlParams.get('lang')]?.description
              : selectedForm.description}
          </Typography>

          <Divider sx={{ my: 2 }} />

          {selectedForm.topics && selectedForm.topics.length > 0 ? (
            selectedForm.topics.map((topic, index) => (
              <Box key={topic._id || index} sx={{ mb: 2 }}>
                <Typography variant="subtitle2">{topic.title}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {topic.fields?.length || 0} fields
                </Typography>
              </Box>
            ))
          ) : selectedForm.fields && selectedForm.fields.length > 0 ? (
            <Typography variant="body2">{selectedForm.fields.length} fields available</Typography>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No fields or topics found in this form
            </Typography>
          )}
        </Paper>
      )}
    </Box>
  );
}
