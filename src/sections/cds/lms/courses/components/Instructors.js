import React from 'react';
import {
  <PERSON><PERSON>,
  TextField,
  IconButton,
  Typography,
  Card,
  CardContent,
  Paper,
  Box,
  Button,
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';

export function Instructors({ courseData, setCourseData }) {
  const handleAddInstructor = () => {
    setCourseData((prev) => ({
      ...prev,
      instructors: [
        ...prev.instructors,
        {
          name: '',
          role: '',
          company: '',
          bio: '',
        },
      ],
    }));
  };

  const handleRemoveInstructor = (index) => {
    setCourseData((prev) => ({
      ...prev,
      instructors: prev.instructors.filter((_, i) => i !== index),
    }));
  };

  const handleInstructorChange = (index, field, value) => {
    const newInstructors = [...courseData.instructors];
    newInstructors[index] = {
      ...newInstructors[index],
      [field]: value,
    };
    setCourseData((prev) => ({
      ...prev,
      instructors: newInstructors,
    }));
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">Instructors</Typography>
          <Button startIcon={<AddIcon />} onClick={handleAddInstructor}>
            Add Instructor
          </Button>
        </Box>

        {courseData.instructors.map((instructor, index) => (
          <Paper key={index} elevation={2} sx={{ p: 2, mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle1">Instructor {index + 1}</Typography>
              <IconButton
                onClick={() => handleRemoveInstructor(index)}
                disabled={courseData.instructors.length === 1}
                color="error"
              >
                <DeleteIcon />
              </IconButton>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Name"
                  value={instructor.name}
                  onChange={(e) => handleInstructorChange(index, 'name', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Role"
                  value={instructor.role}
                  onChange={(e) => handleInstructorChange(index, 'role', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Company"
                  value={instructor.company}
                  onChange={(e) => handleInstructorChange(index, 'company', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Bio"
                  value={instructor.bio}
                  multiline
                  rows={2}
                  onChange={(e) => handleInstructorChange(index, 'bio', e.target.value)}
                />
              </Grid>
            </Grid>
          </Paper>
        ))}
      </CardContent>
    </Card>
  );
}
