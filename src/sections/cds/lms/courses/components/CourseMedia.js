import React, { useEffect } from 'react';
import {
  Grid,
  TextField,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { useSelector } from 'react-redux';

export function CourseMedia({ courseData, handleBasicInfoChange }) {
  const queryParams = new URLSearchParams(window.location.search);
  const langParam = queryParams.get('lang');

  // Doğru değerleri al: Eğer çeviri modu ve çeviri varsa çeviriden al, yoksa ana kurs verisinden al
  const getValue = (fieldName) => {
    if (
      langParam &&
      courseData.translations &&
      courseData.translations[langParam] &&
      courseData.translations[langParam][fieldName] !== undefined
    ) {
      return courseData.translations[langParam][fieldName];
    }
    return courseData[fieldName] || '';
  };

  // Değişikliği izlemek için wrapper
  const handleChange = (e) => {
    handleBasicInfoChange(e);
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 3 }}>
          Course Media
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Cover Image URL"
              name="coverImage"
              value={getValue('coverImage')}
              onChange={handleChange}
              helperText="Provide a URL for the course cover image"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Provider Image URL"
              name="providerImage"
              value={getValue('providerImage')}
              onChange={handleChange}
              helperText="Provide a URL for the provider logo"
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel id="provider-label">Provider</InputLabel>
              <Select
                labelId="provider-label"
                name="provider"
                value={getValue('provider')}
                onChange={handleChange}
                label="Provider"
              >
                <MenuItem value="ai-business-school">AI Business School</MenuItem>
                <MenuItem value="microsoft">Microsoft</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
