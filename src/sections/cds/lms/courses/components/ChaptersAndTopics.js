import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  <PERSON>rid,
  TextField,
  IconButton,
  Typography,
  Card,
  CardContent,
  Paper,
  Box,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Divider,
  FormHelperText,
  Autocomplete,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  CloudUpload as CloudUploadIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { useUsecases } from 'src/apis/useUsecases';
import { FormContentBlock } from './FormContentBlock';

const CONTENT_BLOCK_TYPES = [
  'text',
  'video',
  'quiz',
  'usecase',
  'pdf',
  'form',
  'playground-gpt',
  'playground-dalle',
  'playground-heygen',
];

export function ChaptersAndTopics({
  courseData,
  handleChapterAdd,
  handleChapter<PERSON>hange,
  handleChapterDelete,
  handleTopicAdd,
  handleTopicChange,
  handleTopicDelete,
  handleContentBlockAdd,
  handleContentBlockChange,
  handleContentBlockDelete,
  quizzes,
  loadingQuizzes,
  errors,
  uploadMedia,
  renderEditor,
  contentBlockTypes = CONTENT_BLOCK_TYPES,
  usecases,
  loadingUsecases,
}) {
  const { getUsecases } = useUsecases();

  const ITEMS_PER_PAGE = 300;
  const [usecaseOptions, setUsecaseOptions] = useState([]);
  const [usecaseSearch, setUsecaseSearch] = useState('');

  const handleUsecaseSearch = useCallback(
    async (searchValue) => {
      try {
        const response = await getUsecases(1, searchValue, '', '', parseInt(ITEMS_PER_PAGE, 10));

        if (response?.data?.useCases) {
          setUsecaseOptions(response.data.useCases);
        }
      } catch (error) {
        console.error('Error searching usecases:', error);
      }
    },
    [getUsecases]
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      handleUsecaseSearch(usecaseSearch);
    }, 300);

    return () => clearTimeout(timer);
  }, [usecaseSearch, handleUsecaseSearch]);

  const determineUsecaseType = (usecase) => {
    if (!usecase) return 'text';

    let type = usecase.api_type || usecase.use_case_type;

    if (Array.isArray(type)) {
      type = type[0] || '';
    }

    type = String(type).toLowerCase();

    if (type.includes('text')) return 'text';
    if (type.includes('image') || type.includes('dalle')) return 'image';
    if (type.includes('video') || type.includes('heygen')) return 'video';
    if (type.includes('assistant')) return 'assistant';

    return 'text';
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">Chapters and Topics</Typography>
          <Button startIcon={<AddIcon />} onClick={handleChapterAdd}>
            Add Chapter
          </Button>
        </Box>

        {courseData.chapters.map((chapter, chapterIndex) => (
          <Paper key={chapterIndex} elevation={2} sx={{ p: 2, mb: 3, position: 'relative' }}>
            <Box sx={{ position: 'absolute', top: 10, right: 10 }}>
              <IconButton
                onClick={() => handleChapterDelete(chapterIndex)}
                disabled={courseData.chapters.length === 1}
                color="error"
                size="small"
              >
                <DeleteIcon />
              </IconButton>
            </Box>

            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Chapter {chapterIndex + 1}
            </Typography>

            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Chapter Title"
                  value={chapter.title}
                  onChange={(e) => handleChapterChange(chapterIndex, 'title', e.target.value)}
                  error={
                    errors.chapters &&
                    errors.chapters[chapterIndex] &&
                    !!errors.chapters[chapterIndex].title
                  }
                  helperText={
                    errors.chapters &&
                    errors.chapters[chapterIndex] &&
                    errors.chapters[chapterIndex].title
                  }
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Chapter Description"
                  value={chapter.description}
                  onChange={(e) => handleChapterChange(chapterIndex, 'description', e.target.value)}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle2">Topics</Typography>
              <Button
                size="small"
                startIcon={<AddIcon />}
                onClick={() => handleTopicAdd(chapterIndex)}
              >
                Add Topic
              </Button>
            </Box>

            {chapter.topics.map((topic, topicIndex) => (
              <Paper key={topicIndex} variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Topic {topicIndex + 1}</Typography>
                  <IconButton
                    onClick={() => handleTopicDelete(chapterIndex, topicIndex)}
                    disabled={chapter.topics.length === 1}
                    color="error"
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Topic Title"
                      value={topic.title}
                      onChange={(e) =>
                        handleTopicChange(chapterIndex, topicIndex, 'title', e.target.value)
                      }
                      error={
                        errors.chapters &&
                        errors.chapters[chapterIndex] &&
                        errors.chapters[chapterIndex].topics &&
                        errors.chapters[chapterIndex].topics[topicIndex] &&
                        !!errors.chapters[chapterIndex].topics[topicIndex].title
                      }
                      helperText={
                        errors.chapters &&
                        errors.chapters[chapterIndex] &&
                        errors.chapters[chapterIndex].topics &&
                        errors.chapters[chapterIndex].topics[topicIndex] &&
                        errors.chapters[chapterIndex].topics[topicIndex].title
                      }
                      size="small"
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Topic Description"
                      value={topic.description}
                      onChange={(e) =>
                        handleTopicChange(chapterIndex, topicIndex, 'description', e.target.value)
                      }
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Video Duration (e.g., 10:00)"
                      value={topic.video?.duration || '00:00'}
                      onChange={(e) =>
                        handleTopicChange(chapterIndex, topicIndex, 'video', {
                          duration: e.target.value,
                        })
                      }
                      size="small"
                      placeholder="MM:SS"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <TextField
                        fullWidth
                        label="Topic type"
                        value={topic.type}
                        onChange={(e) =>
                          handleTopicChange(chapterIndex, topicIndex, 'type', e.target.value)
                        }
                        size="small"
                        sx={{ flexGrow: 1 }}
                      />
                      <TextField
                        select
                        label="Topic Icon"
                        value={topic.icon || 'default'}
                        onChange={(e) =>
                          handleTopicChange(chapterIndex, topicIndex, 'icon', e.target.value)
                        }
                        size="small"
                        sx={{ width: 'auto', minWidth: '120px' }}
                      >
                        <MenuItem value="default">Default</MenuItem>
                        <MenuItem value="text">Text</MenuItem>
                        <MenuItem value="video">Video</MenuItem>
                        <MenuItem value="image">Image</MenuItem>
                        <MenuItem value="form">Form</MenuItem>
                        <MenuItem value="quiz">Quiz</MenuItem>
                        <MenuItem value="pdf">PDF</MenuItem>
                        <MenuItem value="usecase">Use case</MenuItem>
                        <MenuItem value="playground">Playground</MenuItem>
                      </TextField>
                    </Box>
                  </Grid>
                </Grid>

                <Box sx={{ mt: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="body2">Content Blocks</Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={() => handleContentBlockAdd(chapterIndex, topicIndex)}
                      >
                        Add Content Block
                      </Button>
                    </Box>
                  </Box>

                  {topic.contentBlocks.map((block, blockIndex) => (
                    <Paper key={blockIndex} variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="body2">Content Block {blockIndex + 1}</Typography>
                        <IconButton
                          onClick={() =>
                            handleContentBlockDelete(chapterIndex, topicIndex, blockIndex)
                          }
                          color="error"
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>

                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <FormControl fullWidth size="small">
                            <InputLabel>Content Type</InputLabel>
                            <Select
                              value={
                                block.displayType === 'playground'
                                  ? `playground-${block.playground_type}`
                                  : block.displayType === 'usecase' ||
                                      (block.type === 'text' && block.displayType === 'usecase') ||
                                      (block.type === 'text' && block.usecase_slug)
                                    ? 'usecase'
                                    : block.displayType || block.type || 'text'
                              }
                              onChange={(e) => {
                                const newType = e.target.value;

                                if (newType === 'usecase') {
                                  // Usecase için displayType ve type değerlerini ayarla
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'type',
                                    'text' // Type 'text' olmalı
                                  );
                                  // Bir süre bekleyip displayType'ı ayarla
                                  setTimeout(() => {
                                    handleContentBlockChange(
                                      chapterIndex,
                                      topicIndex,
                                      blockIndex,
                                      'displayType',
                                      'usecase'
                                    );
                                  }, 100);
                                } else {
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'type',
                                    newType
                                  );
                                }
                              }}
                              label="Content Type"
                            >
                              {contentBlockTypes.map((type) => (
                                <MenuItem key={type} value={type}>
                                  {type === 'text'
                                    ? 'Text'
                                    : type === 'video'
                                      ? 'Video'
                                      : type === 'quiz'
                                        ? 'Quiz'
                                        : type === 'usecase'
                                          ? 'Use case'
                                          : type === 'pdf'
                                            ? 'PDF'
                                            : type === 'form'
                                              ? 'Form'
                                              : type === 'playground-gpt'
                                                ? 'Playground GPT'
                                                : type === 'playground-dalle'
                                                  ? 'Playground Dalle'
                                                  : 'Playground Heygen'}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            size="small"
                            label="Content Title"
                            value={block.title}
                            onChange={(e) =>
                              handleContentBlockChange(
                                chapterIndex,
                                topicIndex,
                                blockIndex,
                                'title',
                                e.target.value
                              )
                            }
                          />
                        </Grid>
                        {(() => {
                          // Önce usecase kontrolü yap
                          const isUsecase =
                            block.displayType === 'usecase' ||
                            (block.type === 'text' && block.displayType === 'usecase') ||
                            block.type === 'usecase' ||
                            (block.type === 'text' && block.usecase_slug);

                          if (isUsecase) {
                            return (
                              <Grid item xs={12}>
                                {loadingUsecases ? (
                                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                    <CircularProgress size={24} />
                                  </Box>
                                ) : (
                                  <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                                    <Autocomplete
                                      id={`usecase-autocomplete-${chapterIndex}-${topicIndex}-${blockIndex}`}
                                      options={usecaseOptions}
                                      getOptionLabel={(option) =>
                                        option?.title || 'Untitled Usecase'
                                      }
                                      value={
                                        usecaseOptions.find(
                                          (option) => option.slug === block.usecase_slug
                                        ) || null
                                      }
                                      onChange={(event, newValue) => {
                                        if (!newValue) {
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'usecase',
                                            {
                                              usecase_slug: '',
                                              usecase_type: '',
                                              type: 'text',
                                              displayType: 'usecase',
                                            }
                                          );
                                          return;
                                        }
                                        handleContentBlockChange(
                                          chapterIndex,
                                          topicIndex,
                                          blockIndex,
                                          'usecase',
                                          {
                                            usecase_slug: newValue.slug,
                                            usecase_type: determineUsecaseType(newValue),
                                            type: 'text',
                                            displayType: 'usecase',
                                          }
                                        );
                                      }}
                                      renderInput={(params) => (
                                        <TextField
                                          {...params}
                                          label="Select Usecase"
                                          placeholder="Search usecases..."
                                          error={
                                            !!errors?.chapters?.[chapterIndex]?.topics?.[topicIndex]
                                              ?.contentBlocks?.[blockIndex]?.usecase_slug
                                          }
                                          helperText={
                                            errors?.chapters?.[chapterIndex]?.topics?.[topicIndex]
                                              ?.contentBlocks?.[blockIndex]?.usecase_slug
                                          }
                                        />
                                      )}
                                      noOptionsText="No usecases found"
                                      loading={loadingUsecases}
                                      loadingText="Loading usecases..."
                                      isOptionEqualToValue={(option, value) =>
                                        option.slug === value.slug
                                      }
                                      disablePortal={false}
                                      blurOnSelect={false}
                                      filterOptions={(options, { inputValue }) => {
                                        return options.filter((option) =>
                                          option.title
                                            .toLowerCase()
                                            .includes(inputValue.toLowerCase())
                                        );
                                      }}
                                      renderOption={(props, option) => (
                                        <li {...props} key={option.slug}>
                                          {option.title}
                                        </li>
                                      )}
                                      onBlur={(event) => {
                                        // Blur olayında seçimi korumak için
                                        event.preventDefault();
                                      }}
                                    />
                                  </FormControl>
                                )}
                              </Grid>
                            );
                          }

                          // Diğer tiplerin kontrolü
                          switch (block.type) {
                            case 'text':
                              return (
                                block.displayType !== 'usecase' &&
                                block.displayType !== 'playground' &&
                                renderEditor(chapterIndex, topicIndex, blockIndex, block)
                              );
                            case 'video':
                              return (
                                <>
                                  <Grid item xs={12}>
                                    <FormControl fullWidth size="small">
                                      <InputLabel>Video Type</InputLabel>
                                      <Select
                                        value={block.videoContent?.type || 'youtube'}
                                        onChange={(e) =>
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'videoContent',
                                            {
                                              ...block.videoContent,
                                              type: e.target.value,
                                              youtubeUrl:
                                                e.target.value === 'youtube'
                                                  ? block.videoContent?.youtubeUrl || ''
                                                  : '',
                                              hlsData:
                                                e.target.value === 'hls'
                                                  ? { streamingUrls: [] }
                                                  : null,
                                            }
                                          )
                                        }
                                        label="Video Type"
                                      >
                                        <MenuItem value="youtube">YouTube</MenuItem>
                                        <MenuItem value="hls">HLS</MenuItem>
                                      </Select>
                                    </FormControl>
                                  </Grid>
                                  <Grid item xs={12}>
                                    <TextField
                                      fullWidth
                                      size="small"
                                      label={
                                        block.videoContent?.type === 'hls'
                                          ? 'HLS Video URL'
                                          : 'YouTube URL'
                                      }
                                      value={
                                        block.videoContent?.type === 'hls'
                                          ? block.videoContent?.hlsData?.streamingUrls?.[0]
                                              ?.paths?.[0] || ''
                                          : block.videoContent?.youtubeUrl || ''
                                      }
                                      onChange={(e) => {
                                        const newValue = e.target.value;
                                        if (block.videoContent?.type === 'hls') {
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'videoContent',
                                            {
                                              ...block.videoContent,
                                              type: 'hls',
                                              hlsData: {
                                                streamingUrls: [
                                                  {
                                                    _id:
                                                      block.videoContent?.hlsData
                                                        ?.streamingUrls?.[0]?._id ||
                                                      Math.random().toString(36).substr(2, 9),
                                                    paths: [newValue],
                                                  },
                                                ],
                                              },
                                            }
                                          );
                                        } else {
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'videoContent',
                                            {
                                              ...block.videoContent,
                                              type: 'youtube',
                                              youtubeUrl: newValue,
                                              hlsData: null,
                                            }
                                          );
                                        }
                                      }}
                                    />
                                  </Grid>
                                  <Grid item xs={12}>
                                    <TextField
                                      fullWidth
                                      size="small"
                                      label="Thumbnail URL"
                                      value={block.videoContent?.thumbnail || ''}
                                      onChange={(e) =>
                                        handleContentBlockChange(
                                          chapterIndex,
                                          topicIndex,
                                          blockIndex,
                                          'videoContent',
                                          {
                                            ...block.videoContent,
                                            thumbnail: e.target.value,
                                          }
                                        )
                                      }
                                    />
                                  </Grid>
                                </>
                              );
                            case 'quiz':
                              return (
                                <Grid item xs={12}>
                                  {loadingQuizzes ? (
                                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                      <CircularProgress size={24} />
                                    </Box>
                                  ) : (
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        id={`quiz-autocomplete-${chapterIndex}-${topicIndex}-${blockIndex}`}
                                        options={quizzes}
                                        getOptionLabel={(option) => {
                                          // URL'den dil parametresini al
                                          const urlParams = new URLSearchParams(
                                            window.location.search
                                          );
                                          const selectedLanguage = urlParams.get('lang');

                                          // Çeviriyi kontrol et ve başlığı belirle
                                          let quizTitle = option.title;
                                          if (
                                            selectedLanguage &&
                                            option.translations &&
                                            option.translations[selectedLanguage] &&
                                            option.translations[selectedLanguage].title
                                          ) {
                                            quizTitle = option.translations[selectedLanguage].title;
                                          }

                                          return quizTitle;
                                        }}
                                        value={
                                          quizzes.find(
                                            (q) => q._id === (block.quiz?.quiz_id || block.quiz?.id)
                                          ) || null
                                        }
                                        onChange={(event, newValue) => {
                                          if (!newValue) return;

                                          const quizId = newValue._id;

                                          // Content block'u güncelle
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'type',
                                            'quiz'
                                          );

                                          // Quiz verilerini güncelle
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'quiz',
                                            {
                                              quiz_id: quizId,
                                              questions: newValue.questions || [],
                                            }
                                          );
                                        }}
                                        renderInput={(params) => (
                                          <TextField
                                            {...params}
                                            label="Select Quiz"
                                            placeholder="Search quizzes..."
                                            InputProps={{
                                              ...params.InputProps,
                                              endAdornment: (
                                                <>
                                                  {loadingQuizzes ? (
                                                    <CircularProgress size={20} />
                                                  ) : null}
                                                  {params.InputProps.endAdornment}
                                                </>
                                              ),
                                            }}
                                          />
                                        )}
                                        noOptionsText="No quizzes found"
                                        disablePortal={false}
                                        blurOnSelect={false}
                                        onBlur={(event) => {
                                          // Blur olayında seçimi korumak için
                                          event.preventDefault();
                                        }}
                                      />
                                    </FormControl>
                                  )}
                                </Grid>
                              );
                            case 'pdf':
                              return (
                                <Box
                                  sx={{
                                    mt: 2,
                                    p: 3,
                                    border: '2px dashed',
                                    borderColor: 'divider',
                                    borderRadius: 1,
                                    textAlign: 'center',
                                    cursor: 'pointer',
                                    '&:hover': {
                                      borderColor: 'primary.main',
                                      bgcolor: 'action.hover',
                                    },
                                  }}
                                  onClick={() => {
                                    const input = document.createElement('input');
                                    input.setAttribute('type', 'file');
                                    input.setAttribute('accept', '.pdf');

                                    input.onchange = async function () {
                                      const file = this.files[0];
                                      if (file) {
                                        try {
                                          const uploadedFile = await uploadMedia('pdf', file);
                                          if (uploadedFile) {
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'pdfContent',
                                              {
                                                url: uploadedFile.url,
                                                fileName: file.name,
                                                fileSize: file.size,
                                              }
                                            );
                                          }
                                        } catch (error) {
                                          console.error('Error uploading PDF:', error);
                                          // Hata mesajını göster
                                        }
                                      }
                                    };

                                    input.click();
                                  }}
                                >
                                  {block.pdfContent?.url ? (
                                    <Box>
                                      <Box
                                        sx={{
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          mb: 1,
                                        }}
                                      >
                                        <PdfIcon color="primary" sx={{ fontSize: 40, mr: 1 }} />
                                        <Typography variant="body1" fontWeight="bold">
                                          {block.pdfContent.fileName || 'PDF Dosyası'}
                                        </Typography>
                                      </Box>
                                      <Typography variant="body2" color="text.secondary">
                                        {block.pdfContent.fileSize
                                          ? `${Math.round(block.pdfContent.fileSize / 1024)} KB`
                                          : ''}
                                      </Typography>
                                      <Box sx={{ mt: 1 }}>
                                        <Button
                                          size="small"
                                          variant="outlined"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            window.open(block.pdfContent.url, '_blank');
                                          }}
                                        >
                                          Preview
                                        </Button>
                                        <Button
                                          size="small"
                                          color="error"
                                          sx={{ ml: 1 }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'pdfContent',
                                              {
                                                url: '',
                                                fileName: '',
                                                fileSize: 0,
                                              }
                                            );
                                          }}
                                        >
                                          Remove
                                        </Button>
                                      </Box>
                                    </Box>
                                  ) : (
                                    <Box>
                                      <CloudUploadIcon
                                        color="primary"
                                        sx={{ fontSize: 40, mb: 1 }}
                                      />
                                      <Typography variant="body1">Upload PDF File</Typography>
                                      <Typography variant="body2" color="text.secondary">
                                        Drag and drop or click to upload
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                              );
                            case 'form':
                              return (
                                <FormContentBlock
                                  contentBlock={block}
                                  onChange={handleContentBlockChange}
                                  chapterIndex={chapterIndex}
                                  topicIndex={topicIndex}
                                  blockIndex={blockIndex}
                                />
                              );
                            default:
                              return null;
                          }
                        })()}

                        {block.displayType === 'playground' && block.playground_type === 'gpt' && (
                          <>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                multiline
                                rows={4}
                                size="small"
                                label="Initial Prompt"
                                value={block.initialPrompt || ''}
                                onChange={(e) =>
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'initialPrompt',
                                    e.target.value
                                  )
                                }
                              />
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                type="number"
                                size="small"
                                label="Temperature"
                                value={block.temperature || 0.7}
                                onChange={(e) =>
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'temperature',
                                    parseFloat(e.target.value)
                                  )
                                }
                                inputProps={{ min: 0, max: 1, step: 0.1 }}
                              />
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                type="number"
                                size="small"
                                label="Top P"
                                value={block.topP || 1}
                                onChange={(e) =>
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'topP',
                                    parseFloat(e.target.value)
                                  )
                                }
                                inputProps={{ min: 0, max: 1, step: 0.1 }}
                              />
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                type="number"
                                size="small"
                                label="Frequency Penalty"
                                value={block.frequencyPenalty || 0}
                                onChange={(e) =>
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'frequencyPenalty',
                                    parseFloat(e.target.value)
                                  )
                                }
                                inputProps={{ min: 0, max: 2, step: 0.1 }}
                              />
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                type="number"
                                size="small"
                                label="Presence Penalty"
                                value={block.presencePenalty || 0}
                                onChange={(e) =>
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'presencePenalty',
                                    parseFloat(e.target.value)
                                  )
                                }
                                inputProps={{ min: 0, max: 2, step: 0.1 }}
                              />
                            </Grid>
                          </>
                        )}

                        {block.displayType === 'playground' &&
                          block.playground_type === 'dalle' && (
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                multiline
                                rows={4}
                                size="small"
                                label="Initial Prompt"
                                value={block.initialPrompt || ''}
                                onChange={(e) =>
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'initialPrompt',
                                    e.target.value
                                  )
                                }
                              />
                            </Grid>
                          )}

                        {block.displayType === 'playground' &&
                          block.playground_type === 'heygen' && (
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                size="small"
                                label="Heygen Video ID"
                                value={block.heygen_id || ''}
                                onChange={(e) =>
                                  handleContentBlockChange(
                                    chapterIndex,
                                    topicIndex,
                                    blockIndex,
                                    'heygen_id',
                                    e.target.value
                                  )
                                }
                              />
                            </Grid>
                          )}
                      </Grid>
                    </Paper>
                  ))}
                </Box>
              </Paper>
            ))}
          </Paper>
        ))}
      </CardContent>
    </Card>
  );
}
