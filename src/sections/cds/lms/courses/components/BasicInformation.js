import React from 'react';
import {
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Typography,
  Card,
  CardContent,
  Chip,
  Box,
  Paper,
  Autocomplete,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
  IconButton,
  Button,
  Stack,
  Divider,
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import BundledEditor from 'src/components/text-editor/editor';

const COURSE_LEVELS = ['beginner', 'intermediate', 'advanced'];
const COURSE_CATEGORIES = ['Programming', 'Design', 'Business', 'Marketing', 'Other'];

// Örnek skill listesi - gerçek uygulamada API'den alınabilir
const SUGGESTED_SKILLS = [
  'Generative AI Proficiency',
  'Content Creation with AI',
  'Data Extraction and Analysis',
  'Creative Problem-Solving',
  'Prompt Engineering',
  'Deep Learning',
  'Machine Learning',
  'Natural Language Processing',
  'Computer Vision',
  'Python Programming',
  'Data Visualization',
  'Decision Making',
  'Strategic Planning',
  'Critical Thinking',
];

export function BasicInformation({ courseData, handleBasicInfoChange, errors }) {
  // editorRefs referansını ekliyoruz
  const editorRefs = React.useRef({});

  // URL'den dil parametresini al
  const queryParams = new URLSearchParams(window.location.search);
  const langParam = queryParams.get('lang');

  // Doğru değerleri al: Eğer çeviri modu ve çeviri varsa çeviriden al, yoksa ana kurs verisinden al
  const getValue = (fieldName) => {
    if (
      langParam &&
      courseData.translations &&
      courseData.translations[langParam] &&
      courseData.translations[langParam][fieldName] !== undefined
    ) {
      return courseData.translations[langParam][fieldName];
    }
    return courseData[fieldName] || '';
  };

  // Değişikliği izlemek için wrapper
  const handleChange = (e) => {
    handleBasicInfoChange(e);
  };

  // Skills için handleChange fonksiyonu
  const handleSkillsChange = (event, newValue) => {
    // Bu fonksiyon doğrudan courseData'yı güncellemek yerine bir event oluşturur
    const fakeEvent = {
      target: {
        name: 'skills',
        value: newValue,
      },
    };
    handleBasicInfoChange(fakeEvent);
  };

  // Yeni bir outcome eklemek için fonksiyon
  const handleAddOutcome = () => {
    const newOutcomes = [
      ...(courseData.outcomes || []),
      {
        text: '',
        imageUrl: '',
        imagePosition: 'right',
        formatting: 'html',
      },
    ];

    const fakeEvent = {
      target: {
        name: 'outcomes',
        value: newOutcomes,
      },
    };
    handleBasicInfoChange(fakeEvent);
  };

  // Bir outcome'u silmek için fonksiyon
  const handleRemoveOutcome = (index) => {
    const newOutcomes = [...(courseData.outcomes || [])];
    newOutcomes.splice(index, 1);

    const fakeEvent = {
      target: {
        name: 'outcomes',
        value: newOutcomes,
      },
    };
    handleBasicInfoChange(fakeEvent);
  };

  // Outcome'lardaki değişiklikleri izlemek için fonksiyon
  const handleOutcomeChange = (index, field, value) => {
    const newOutcomes = [...(courseData.outcomes || [])];
    if (field === 'text') {
      // HTML formatını desteklemesi için formatting alanını ekliyoruz
      newOutcomes[index] = {
        ...newOutcomes[index],
        [field]: value,
        formatting: 'html',
      };
    } else {
      newOutcomes[index] = {
        ...newOutcomes[index],
        [field]: value,
      };
    }

    const fakeEvent = {
      target: {
        name: 'outcomes',
        value: newOutcomes,
      },
    };
    handleBasicInfoChange(fakeEvent);
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 3 }}>
          Basic Information
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Course Title"
              name="title"
              value={getValue('title')}
              onChange={handleChange}
              error={!!errors.title}
              helperText={errors.title}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 1 }}>
              Course Description
            </Typography>
            <Box
              sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                minHeight: 250,
                mb: 2,
              }}
            >
              <BundledEditor
                onInit={(evt, editor) => {
                  editorRefs.current['description'] = editor;
                }}
                value={getValue('description')}
                onEditorChange={(content) => {
                  const fakeEvent = {
                    target: {
                      name: 'description',
                      value: content,
                    },
                  };
                  handleBasicInfoChange(fakeEvent);
                }}
                init={{
                  height: 250,
                  menubar: false,
                  plugins: [
                    'advlist',
                    'autolink',
                    'lists',
                    'link',
                    'image',
                    'charmap',
                    'preview',
                    'anchor',
                    'searchreplace',
                    'visualblocks',
                    'code',
                    'fullscreen',
                    'insertdatetime',
                    'media',
                    'table',
                    'help',
                    'wordcount',
                  ],
                  toolbar:
                    'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
                  content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                }}
              />
            </Box>
            {errors.description && <FormHelperText error>{errors.description}</FormHelperText>}
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth error={!!errors.category} required>
              <InputLabel id="category-label">Category</InputLabel>
              <Select
                labelId="category-label"
                name="category"
                value={getValue('category')}
                onChange={handleChange}
                label="Category"
              >
                {COURSE_CATEGORIES.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
              {errors.category && <FormHelperText>{errors.category}</FormHelperText>}
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth error={!!errors.level} required>
              <InputLabel id="level-label">Level</InputLabel>
              <Select
                labelId="level-label"
                name="level"
                value={getValue('level')}
                onChange={handleChange}
                label="Level"
              >
                {COURSE_LEVELS.map((level) => (
                  <MenuItem key={level} value={level}>
                    {level === 'beginner'
                      ? 'Beginner'
                      : level === 'intermediate'
                        ? 'Intermediate'
                        : 'Advanced'}
                  </MenuItem>
                ))}
              </Select>
              {errors.level && <FormHelperText>{errors.level}</FormHelperText>}
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Duration (e.g., 45m, 1h 30m)"
              name="duration"
              value={getValue('duration')}
              onChange={handleChange}
              error={!!errors.duration}
              helperText={errors.duration}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Skills Gained
            </Typography>
            <Autocomplete
              multiple
              id="skills-select"
              options={SUGGESTED_SKILLS}
              freeSolo
              value={getValue('skills') || []}
              onChange={handleSkillsChange}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    variant="outlined"
                    label={option}
                    color="primary"
                    {...getTagProps({ index })}
                  />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder="Add skills (e.g., Content Creation with AI)"
                  helperText="Enter the skills that students will gain through this course"
                  fullWidth
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Learning Outcomes
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Add what students will achieve after completing this course
              </Typography>

              {(courseData.outcomes || []).map((outcome, index) => (
                <Paper key={index} variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{ mb: 2 }}
                  >
                    <Typography variant="subtitle2">Outcome {index + 1}</Typography>
                    <IconButton
                      color="error"
                      onClick={() => handleRemoveOutcome(index)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Stack>

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        Outcome Description
                      </Typography>
                      <Box
                        sx={{
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          minHeight: 250,
                          mb: 2,
                        }}
                      >
                        <BundledEditor
                          onInit={(evt, editor) => {
                            editorRefs.current[index] = editor;
                          }}
                          value={outcome.text || ''}
                          onEditorChange={(content) => handleOutcomeChange(index, 'text', content)}
                          init={{
                            height: 250,
                            menubar: false,
                            plugins: [
                              'advlist',
                              'autolink',
                              'lists',
                              'link',
                              'image',
                              'charmap',
                              'preview',
                              'anchor',
                              'searchreplace',
                              'visualblocks',
                              'code',
                              'fullscreen',
                              'insertdatetime',
                              'media',
                              'table',
                              'code',
                              'help',
                              'wordcount',
                            ],
                            toolbar:
                              'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
                            content_style:
                              'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                          }}
                        />
                      </Box>
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Image URL"
                        value={outcome.imageUrl || ''}
                        onChange={(e) => handleOutcomeChange(index, 'imageUrl', e.target.value)}
                        placeholder="Add an image URL to illustrate this outcome"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControl component="fieldset">
                        <FormLabel component="legend">Image Position</FormLabel>
                        <RadioGroup
                          row
                          value={outcome.imagePosition || 'right'}
                          onChange={(e) =>
                            handleOutcomeChange(index, 'imagePosition', e.target.value)
                          }
                        >
                          <FormControlLabel value="left" control={<Radio />} label="Left" />
                          <FormControlLabel value="right" control={<Radio />} label="Right" />
                        </RadioGroup>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Paper>
              ))}

              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={handleAddOutcome}
                sx={{ mt: 1 }}
              >
                Add Outcome
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
