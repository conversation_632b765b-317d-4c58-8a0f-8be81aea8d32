import React from 'react';
import {
  Grid,
  FormControl,
  FormControlLabel,
  Switch,
  Typography,
  Card,
  CardContent,
} from '@mui/material';

export function CourseSettings({ courseData, setCourseData }) {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 3 }}>
          Course Settings
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={courseData.certificateAvailable}
                  onChange={(e) =>
                    setCourseData((prev) => ({
                      ...prev,
                      certificateAvailable: e.target.checked,
                    }))
                  }
                />
              }
              label="Certificate Available"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={courseData.isPublished}
                  onChange={(e) =>
                    setCourseData((prev) => ({
                      ...prev,
                      isPublished: e.target.checked,
                    }))
                  }
                />
              }
              label="Published"
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
