import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Typo<PERSON>,
  Button,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Stack,
  TextField,
  InputAdornment,
  Box,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Translate as TranslateIcon,
} from '@mui/icons-material';
import { paths } from 'src/routes/paths';
import { useCourses } from 'src/apis/useCourses';

import { LoadingScreen } from 'src/components/loading-screen';
import { toast } from 'react-toastify';

const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'de', name: 'German' },
  { code: 'fr', name: 'French' },
  { code: 'es', name: 'Spanish' },
  { code: 'it', name: 'Italian' },
  { code: 'tr', name: 'Turkish' },
  { code: 'ar', name: 'Arabic' },
  { code: 'zh-Hans', name: 'Chinese (Simplified)' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'ru', name: 'Russian' },
];

// Dil kodundan dil adını bulan yardımcı fonksiyon
const getLanguageName = (code) => {
  const language = LANGUAGES.find((lang) => lang.code === code);
  return language ? language.name : code;
};

export function AllCoursesView() {
  const { getCourses, deleteCourse, translateCourse, courses, loading } = useCourses();
  const [filteredCourses, setFilteredCourses] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [levelFilter, setLevelFilter] = useState('');

  // Translation state
  const [translateDialogOpen, setTranslateDialogOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [targetLanguage, setTargetLanguage] = useState('');
  const [translating, setTranslating] = useState(false);

  useEffect(() => {
    const fetchCourses = async () => {
      await getCourses();
    };
    fetchCourses();
  }, [getCourses]);

  useEffect(() => {
    if (courses) {
      let filtered = [...courses];

      if (searchTerm) {
        filtered = filtered.filter((course) => {
          const defaultLang = course.defaultLanguage || 'en';
          const translation = course.translations?.[defaultLang] || {};
          return (
            course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (translation.description &&
              translation.description.toLowerCase().includes(searchTerm.toLowerCase()))
          );
        });
      }

      if (categoryFilter) {
        filtered = filtered.filter((course) => course.category === categoryFilter);
      }

      if (levelFilter) {
        filtered = filtered.filter((course) => course.level === levelFilter);
      }

      setFilteredCourses(filtered);
    }
  }, [courses, searchTerm, categoryFilter, levelFilter]);

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Translation handlers
  const openTranslateDialog = (course) => {
    setSelectedCourse(course);
    setTranslateDialogOpen(true);
  };

  const closeTranslateDialog = () => {
    setTranslateDialogOpen(false);
    setSelectedCourse(null);
    setTargetLanguage('');
  };

  const handleLanguageChange = (event) => {
    setTargetLanguage(event.target.value);
  };

  const handleTranslate = async () => {
    if (!selectedCourse || !targetLanguage) {
      toast.error('Please select a language');
      return;
    }

    setTranslating(true);
    try {
      const result = await translateCourse(selectedCourse._id, targetLanguage);
      if (result.success) {
        toast.success('Course translated successfully');
        closeTranslateDialog();
      } else {
        toast.error(result.message || 'Translation failed');
      }
    } catch (error) {
      toast.error('Error translating course');
      console.error('Translation error:', error);
    } finally {
      setTranslating(false);
    }
  };

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          All Courses
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            component={RouterLink}
            to={paths.cds.lms.courses.new}
            variant="contained"
            startIcon={<AddIcon />}
          >
            New Course
          </Button>
        </Stack>
      </Box>

      <Card sx={{ mb: 3 }}>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          alignItems="center"
          sx={{ p: 2 }}
        >
          <TextField
            fullWidth
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Search courses..."
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Stack>
      </Card>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Title</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Translations</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredCourses.length > 0 ? (
              filteredCourses.map((course) => (
                <TableRow key={course._id}>
                  <TableCell>
                    {course.translations?.en?.title ||
                      course.translations?.de?.title ||
                      course.title}
                  </TableCell>

                  <TableCell>{course.duration}</TableCell>

                  <TableCell>
                    <Chip
                      label={course.isPublished ? 'Published' : 'Draft'}
                      color={course.isPublished ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {course.translations && Object.keys(course.translations).length > 0 ? (
                      <Stack direction="row" spacing={0.5} flexWrap="wrap" gap={0.5}>
                        {Object.keys(course.translations).map((langCode) => (
                          <Tooltip
                            key={langCode}
                            title={`Edit ${getLanguageName(langCode)} translation`}
                          >
                            <Chip
                              label={langCode.toUpperCase()}
                              size="small"
                              color="primary"
                              variant="outlined"
                              component={RouterLink}
                              to={`${paths.cds.lms.courses.edit(course._id)}?lang=${langCode}`}
                              sx={{
                                '&:hover': {
                                  cursor: 'pointer',
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </Stack>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No translations
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      onClick={() => openTranslateDialog(course)}
                      color="primary"
                      size="small"
                      title="Translate"
                    >
                      <TranslateIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  {courses && courses.length > 0
                    ? 'No courses match the filters'
                    : 'No courses available yet'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Translation Dialog */}
      <Dialog open={translateDialogOpen} onClose={closeTranslateDialog}>
        <DialogTitle>Translate Course</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Select a target language to translate the course content. This will translate all text
            content including title, description, chapters, topics, and content blocks.
          </DialogContentText>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth>
              <InputLabel id="target-language-label">Target Language</InputLabel>
              <Select
                labelId="target-language-label"
                value={targetLanguage}
                onChange={handleLanguageChange}
                label="Target Language"
              >
                {LANGUAGES.map((lang) => (
                  <MenuItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeTranslateDialog} disabled={translating}>
            Cancel
          </Button>
          <Button
            onClick={handleTranslate}
            variant="contained"
            disabled={!targetLanguage || translating}
            startIcon={translating ? <CircularProgress size={20} /> : <TranslateIcon />}
          >
            {translating ? 'Translating...' : 'Translate'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
