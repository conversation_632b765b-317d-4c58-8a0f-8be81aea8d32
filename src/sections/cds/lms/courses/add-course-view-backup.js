import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  Stack,
  Box,
  MenuItem,
  IconButton,
  Divider,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  FormControlLabel,
  Switch,
  CircularProgress,
  Autocomplete,
  FormLabel,
  RadioGroup,
  Radio,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  DragHandle as DragHandleIcon,
  CloudUpload as CloudUploadIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { paths } from 'src/routes/paths';
import { useCourses } from 'src/apis/useCourses';
import { useQuizzes } from 'src/apis/useQuizzes';
import { toast } from 'react-toastify';
import { LoadingButton } from '@mui/lab';
import { useUsecases } from 'src/apis/useUsecases';
import BundledEditor from 'src/components/text-editor/editor';
import { useMedia } from 'src/apis/useMedia';
import { CONFIG } from 'src/config-global';

const COURSE_LEVELS = ['beginner', 'intermediate', 'advanced'];
const COURSE_CATEGORIES = ['Programming', 'Design', 'Business', 'Marketing', 'Other'];
const CONTENT_BLOCK_TYPES = [
  'text',
  'video',
  'quiz',
  'usecase',
  'pdf',
  'playground-gpt',
  'playground-dalle',
  'playground-heygen',
  'form',
];

// UUID oluşturmak için yardımcı fonksiyon
const generateId = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export default function AddCourseView() {
  const navigate = useNavigate();
  const { addCourse } = useCourses();
  const { getQuizzes } = useQuizzes();
  const { getUsecases } = useUsecases();
  const { uploadMedia } = useMedia();
  const [loading, setLoading] = useState(false);
  const [quizzes, setQuizzes] = useState([]);
  const [loadingQuizzes, setLoadingQuizzes] = useState(false);
  const [coverPhoto, setCoverPhoto] = useState(null);
  const [errors, setErrors] = useState({});
  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    category: '',
    level: 'beginner',
    duration: '',
    prerequisites: [''],
    objectives: [''],
    tags: [''],
    skills: [],
    provider: 'ai-business-school', // Varsayılan olarak AI Business School seçili
    outcomes: [],
    coverImage: '',
    providerImage: '',
    certificateAvailable: false,
    instructors: [
      {
        name: '',
        role: '',
        company: '',
        bio: '',
      },
    ],
    rating: {
      average: 0,
      count: 0,
    },
    chapters: [
      {
        title: '',
        description: '',
        order: 1,
        isLocked: true,
        topics: [
          {
            title: '',
            description: '',
            duration: 0,
            video: {
              duration: '00:00',
            },
            order: 1,
            contentBlocks: [],
          },
        ],
      },
    ],
  });
  const [usecases, setUsecases] = useState([]);
  const [loadingUsecases, setLoadingUsecases] = useState(false);
  const [usecaseSearch, setUsecaseSearch] = useState('');
  const [usecaseSearchDebounced, setUsecaseSearchDebounced] = useState('');
  const [usecaseInputValues, setUsecaseInputValues] = useState({});
  const [usecasePage, setUsecasePage] = useState(1);
  const [usecaseOptions, setUsecaseOptions] = useState([]);
  const [hasMoreUsecases, setHasMoreUsecases] = useState(true);

  const editorRefs = React.useRef({});

  const validateForm = () => {
    const newErrors = {};

    if (!courseData.title.trim()) {
      newErrors.title = 'Course title is required';
    }

    if (!courseData.description.trim()) {
      newErrors.description = 'Course description is required';
    }

    if (!courseData.category) {
      newErrors.category = 'Category selection is required';
    }

    if (!courseData.level) {
      newErrors.level = 'Level selection is required';
    }

    if (!courseData.duration.trim()) {
      newErrors.duration = 'Course duration is required';
    }

    // Chapter and topic validations
    const chapterErrors = [];
    courseData.chapters.forEach((chapter, chapterIndex) => {
      const chapterError = {};
      if (!chapter.title.trim()) {
        chapterError.title = 'Chapter title is required';
      }

      const topicErrors = [];
      chapter.topics.forEach((topic, topicIndex) => {
        const topicError = {};
        if (!topic.title.trim()) {
          topicError.title = 'Topic title is required';
        }
        if (Object.keys(topicError).length > 0) {
          topicErrors[topicIndex] = topicError;
        }
      });

      if (topicErrors.length > 0) {
        chapterError.topics = topicErrors;
      }

      if (Object.keys(chapterError).length > 0) {
        chapterErrors[chapterIndex] = chapterError;
      }
    });

    if (chapterErrors.length > 0) {
      newErrors.chapters = chapterErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBasicInfoChange = (e) => {
    const { name, value } = e.target;
    setCourseData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Hata varsa temizle
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleArrayFieldAdd = (field) => {
    setCourseData((prev) => ({
      ...prev,
      [field]: [...prev[field], ''],
    }));
  };

  const handleArrayFieldChange = (field, index, value) => {
    setCourseData((prev) => ({
      ...prev,
      [field]: prev[field].map((item, i) => (i === index ? value : item)),
    }));
  };

  const handleArrayFieldRemove = (field, index) => {
    setCourseData((prev) => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index),
    }));
  };

  const handleChapterAdd = () => {
    setCourseData((prev) => ({
      ...prev,
      chapters: [
        ...prev.chapters,
        {
          title: '',
          description: '',
          order: prev.chapters.length + 1,
          isLocked: true,
          topics: [
            {
              title: '',
              description: '',
              order: 1,
              contentBlocks: [],
            },
          ],
        },
      ],
    }));
  };

  const handleChapterChange = (index, field, value) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, i) =>
        i === index ? { ...chapter, [field]: value } : chapter
      ),
    }));

    // Hata varsa temizle
    if (errors.chapters && errors.chapters[index] && errors.chapters[index][field]) {
      const newErrors = { ...errors };
      newErrors.chapters[index][field] = undefined;
      setErrors(newErrors);
    }
  };

  const handleTopicAdd = (chapterIndex) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, idx) => {
        if (idx === chapterIndex) {
          return {
            ...chapter,
            topics: [
              ...chapter.topics,
              {
                title: '',
                description: '',
                order: chapter.topics.length + 1,
                contentBlocks: [],
              },
            ],
          };
        }
        return chapter;
      }),
    }));
  };

  const handleTopicChange = (chapterIndex, topicIndex, field, value) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, chIdx) => {
        if (chIdx === chapterIndex) {
          return {
            ...chapter,
            topics: chapter.topics.map((topic, topIdx) => {
              if (topIdx === topicIndex) {
                return { ...topic, [field]: value };
              }
              return topic;
            }),
          };
        }
        return chapter;
      }),
    }));

    // Hata varsa temizle
    if (
      errors.chapters &&
      errors.chapters[chapterIndex] &&
      errors.chapters[chapterIndex].topics &&
      errors.chapters[chapterIndex].topics[topicIndex] &&
      errors.chapters[chapterIndex].topics[topicIndex][field]
    ) {
      const newErrors = { ...errors };
      newErrors.chapters[chapterIndex].topics[topicIndex][field] = undefined;
      setErrors(newErrors);
    }
  };

  const handleChapterDelete = (chapterIndex) => {
    setCourseData((prev) => {
      const newChapters = prev.chapters.filter((_, idx) => idx !== chapterIndex);
      // Kalan bölümlerin sırasını güncelle
      return {
        ...prev,
        chapters: newChapters.map((chapter, idx) => ({
          ...chapter,
          order: idx + 1,
        })),
      };
    });
  };

  const handleTopicDelete = (chapterIndex, topicIndex) => {
    setCourseData((prev) => {
      const newChapters = prev.chapters.map((chapter, idx) => {
        if (idx === chapterIndex) {
          const newTopics = chapter.topics.filter((_, tIdx) => tIdx !== topicIndex);
          return {
            ...chapter,
            topics: newTopics.map((topic, idx) => ({
              ...topic,
              order: idx + 1,
            })),
          };
        }
        return chapter;
      });
      return {
        ...prev,
        chapters: newChapters,
      };
    });
  };

  const handleContentBlockAdd = (chapterIndex, topicIndex, blockType = 'text') => {
    setCourseData((prev) => {
      const updatedCourseData = { ...prev };
      const contentBlocks =
        updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks || [];

      // Yeni blok oluştur
      const newBlock = {
        type: blockType,
        title: '',
        description: '',
        order: contentBlocks.length + 1,
      };

      // Tipe göre içerik alanlarını ekle
      switch (blockType) {
        case 'text':
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = 0.7;
          newBlock.topP = 1;
          newBlock.frequencyPenalty = 0;
          newBlock.presencePenalty = 0;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'video':
          newBlock.textContent = null;
          newBlock.videoContent = {
            type: 'youtube', // Default to YouTube
            youtubeUrl: '',
            hlsData: null,
            thumbnail: '',
          };
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = 0.7;
          newBlock.topP = 1;
          newBlock.frequencyPenalty = 0;
          newBlock.presencePenalty = 0;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'quiz':
          newBlock.textContent = null;
          newBlock.videoContent = null;
          newBlock.quiz = {
            id: '',
            questions: [],
          };
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'pdf':
          newBlock.textContent = null;
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = {
            url: '',
            fileName: '',
            fileSize: 0,
          };
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'usecase':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'usecase'; // Görsel olarak usecase göster
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = '';
          newBlock.usecase_type = 'text'; // Geçerli bir usecase_type
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'playground-gpt':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'playground'; // Görsel olarak playground göster
          newBlock.playground_type = 'gpt';
          newBlock.initialPrompt = '';
          newBlock.temperature = 0.7;
          newBlock.topP = 1;
          newBlock.frequencyPenalty = 0;
          newBlock.presencePenalty = 0;
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'playground-dalle':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'playground'; // Görsel olarak playground göster
          newBlock.playground_type = 'dalle';
          newBlock.initialPrompt = '';
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'playground-heygen':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'playground'; // Görsel olarak playground göster
          newBlock.playground_type = 'heygen';
          newBlock.heygen_id = '';
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'form':
          newBlock.textContent = null;
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
      }

      // Yeni bloğu ekle
      contentBlocks.push(newBlock);

      return updatedCourseData;
    });
  };

  const handleContentBlockChange = (chapterIndex, topicIndex, blockIndex, field, value) => {
    setCourseData((prev) => {
      const updatedCourseData = { ...prev };
      const block =
        updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks[blockIndex];

      if (field === 'type') {
        let updatedBlock = {
          ...block,
          type: value,
          title: '',
          description: '',
        };

        // Playground tipleri için displayType alanını ayarla
        if (value === 'playground-gpt') {
          updatedBlock.displayType = 'playground';
          updatedBlock.playground_type = 'gpt';
        } else if (value === 'playground-dalle') {
          updatedBlock.displayType = 'playground';
          updatedBlock.playground_type = 'dalle';
        } else if (value === 'playground-heygen') {
          updatedBlock.displayType = 'playground';
          updatedBlock.playground_type = 'heygen';
        } else if (value === 'usecase') {
          updatedBlock.displayType = 'usecase';
        } else {
          updatedBlock.displayType = null;
        }

        switch (value) {
          case 'text':
            updatedBlock.textContent = {
              content: '',
              formatting: 'markdown',
            };
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'video':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = {
              type: 'youtube', // Default to YouTube
              youtubeUrl: '',
              hlsData: null,
              thumbnail: '',
            };
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'quiz':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = null;
            updatedBlock.quiz = {
              id: '',
              questions: [],
            };
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'pdf':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = {
              url: '',
              fileName: '',
              fileSize: 0,
            };
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'usecase':
            updatedBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
            updatedBlock.displayType = 'usecase'; // Görsel olarak usecase göster
            updatedBlock.textContent = {
              content: '',
              formatting: 'markdown',
            };
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = '';
            updatedBlock.usecase_type = 'text'; // Geçerli bir usecase_type
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'playground-gpt':
            updatedBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
            updatedBlock.displayType = 'playground'; // Görsel olarak playground göster
            updatedBlock.playground_type = 'gpt';
            updatedBlock.initialPrompt = '';
            updatedBlock.temperature = 0.7;
            updatedBlock.topP = 1;
            updatedBlock.frequencyPenalty = 0;
            updatedBlock.presencePenalty = 0;
            updatedBlock.textContent = {
              content: '',
              formatting: 'markdown',
            };
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'playground-dalle':
            updatedBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
            updatedBlock.displayType = 'playground'; // Görsel olarak playground göster
            updatedBlock.playground_type = 'dalle';
            updatedBlock.initialPrompt = '';
            updatedBlock.textContent = {
              content: '',
              formatting: 'markdown',
            };
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'playground-heygen':
            updatedBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
            updatedBlock.displayType = 'playground'; // Görsel olarak playground göster
            updatedBlock.playground_type = 'heygen';
            updatedBlock.heygen_id = '';
            updatedBlock.textContent = {
              content: '',
              formatting: 'markdown',
            };
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'form':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
        }
        updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks[blockIndex] =
          updatedBlock;
      } else if (field === 'textContent' && block.type === 'text') {
        block.textContent = value;
      } else if (field === 'pdfContent' && block.type === 'pdf') {
        block.pdfContent = value;
      } else if (field === 'initialPrompt' && block.playground_type) {
        block.initialPrompt = value;
      } else if (field === 'temperature' && block.playground_type === 'gpt') {
        block.temperature = value;
      } else if (field === 'topP' && block.playground_type === 'gpt') {
        block.topP = value;
      } else if (field === 'frequencyPenalty' && block.playground_type === 'gpt') {
        block.frequencyPenalty = value;
      } else if (field === 'presencePenalty' && block.playground_type === 'gpt') {
        block.presencePenalty = value;
      } else if (field === 'heygen_id' && block.playground_type === 'heygen') {
        block.heygen_id = value;
      } else if (field === 'usecase_slug') {
        try {
          // Find the selected usecase
          const selectedUsecase = usecases.find((u) => u.slug === value);

          if (selectedUsecase) {
            updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks[blockIndex] =
              {
                ...block,
                type: 'text',
                displayType: 'usecase',
                textContent: {
                  content: '',
                  formatting: 'markdown',
                },
                usecase_slug: selectedUsecase.slug,
                usecase_type: determineUsecaseType(selectedUsecase),
                title: selectedUsecase.title || 'Untitled Usecase',
              };
          }
        } catch (error) {
          console.error('Error updating usecase:', error);
        }
      } else if (field === 'quiz') {
        // Quiz için special case - MongoDB beklenen yapı

        if (!value.questions || !Array.isArray(value.questions)) {
          value.questions = [];
        }

        // MongoDB modelinde beklenen yapıya uygun olarak quiz_id değerini atayalım
        if (value._id || value.id) {
          value.quiz_id = value._id || value.id;
        }

        // Quiz verisini güncelle
        block.quiz = { ...value };
      } else {
        block[field] = value;
      }

      return updatedCourseData;
    });
  };

  const handleContentBlockDelete = (chapterIndex, topicIndex, blockIndex) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, chIdx) => {
        if (chIdx === chapterIndex) {
          return {
            ...chapter,
            topics: chapter.topics.map((topic, topIdx) => {
              if (topIdx === topicIndex) {
                return {
                  ...topic,
                  contentBlocks: topic.contentBlocks.filter((_, idx) => idx !== blockIndex),
                };
              }
              return topic;
            }),
          };
        }
        return chapter;
      }),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);

    try {
      // Clean empty arrays
      const cleanedData = {
        ...courseData,
        prerequisites: courseData.prerequisites.filter((item) => item.trim() !== ''),
        objectives: courseData.objectives.filter((item) => item.trim() !== ''),
        tags: courseData.tags.filter((item) => item.trim() !== ''),
      };

      // Create slug
      const slug = courseData.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/g, '-');

      // Quiz_id alanlarının doğru atandığından emin ol
      const updatedChapters = cleanedData.chapters.map((chapter) => {
        const updatedTopics = chapter.topics.map((topic) => {
          const updatedContentBlocks = topic.contentBlocks.map((block) => {
            // Quiz içeriğini kontrol et ve düzelt
            if (block.type === 'quiz' && block.quiz) {
              // Eğer quiz_id yoksa ama _id veya id varsa, quiz_id'yi ekle
              if (!block.quiz.quiz_id && (block.quiz._id || block.quiz.id)) {
                block.quiz.quiz_id = block.quiz._id || block.quiz.id;
              }
            }
            return block;
          });
          return { ...topic, contentBlocks: updatedContentBlocks };
        });
        return { ...chapter, topics: updatedTopics };
      });

      // Add additional required fields
      const courseToSubmit = {
        ...cleanedData,
        chapters: updatedChapters, // Güncellenmiş chapters dizisini kullan
        slug,
        isPublished: false,
        __v: 0,
      };

      const result = await addCourse(courseToSubmit);

      if (result) {
        toast.success('Course created successfully');
        navigate(paths.cds.lms.courses.all);
      } else {
        toast.error('Failed to create course');
      }
    } catch (error) {
      console.error('Error creating course:', error);
      toast.error(error.response?.data?.message || 'An error occurred while creating the course');
    } finally {
      setLoading(false);
    }
  };

  // Quiz listesini yükle
  const loadQuizzes = async () => {
    setLoadingQuizzes(true);
    try {
      const quizData = await getQuizzes();
      setQuizzes(quizData);
    } catch (error) {
      toast.error('Failed to load quizzes');
    } finally {
      setLoadingQuizzes(false);
    }
  };

  // Optimize search handling
  const handleUsecaseSearch = useCallback(
    async (searchValue) => {
      try {
        setLoadingUsecases(true);
        const response = await getUsecases(1, searchValue, '', '', -1);

        if (response?.data?.useCases) {
          setUsecases(response.data.useCases);
          setUsecaseOptions(response.data.useCases);
          setHasMoreUsecases(false);
        }
      } catch (error) {
        console.error('Error searching usecases:', error);
        toast.error('Error searching usecases');
      } finally {
        setLoadingUsecases(false);
      }
    },
    [getUsecases]
  );

  // Debounce search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      if (usecaseSearch !== undefined) {
        handleUsecaseSearch(usecaseSearch);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [usecaseSearch, handleUsecaseSearch]);

  // Component mount olduğunda quizleri ve usecase'leri yükle
  useEffect(() => {
    loadQuizzes();
    handleUsecaseSearch('');
  }, []);

  // Memoize usecase options
  const memoizedUsecaseOptions = useMemo(() => usecases || [], [usecases]);

  // Function to update a specific input value
  const updateUsecaseInputValue = (chapterIndex, topicIndex, blockIndex, value) => {
    setUsecaseInputValues((prev) => ({
      ...prev,
      [`${chapterIndex}-${topicIndex}-${blockIndex}`]: value,
    }));
  };

  // Function to get a specific input value
  const getUsecaseInputValue = (chapterIndex, topicIndex, blockIndex) => {
    return usecaseInputValues[`${chapterIndex}-${topicIndex}-${blockIndex}`] || '';
  };

  // Handle usecase select scroll for infinite loading
  const handleUsecaseSelectScroll = () => {
    if (!hasMoreUsecases || loadingUsecases) return;

    const nextPage = usecasePage + 1;
    setUsecasePage(nextPage);

    const fetchMoreUsecases = async () => {
      try {
        setLoadingUsecases(true);
        const response = await getUsecases(nextPage, usecaseSearchDebounced, '', '', -1);

        if (response?.data?.useCases?.length) {
          setUsecases((prev) => [...prev, ...response.data.useCases]);
          setUsecaseOptions((prev) => [...prev, ...response.data.useCases]);
          setHasMoreUsecases(response.data.useCases.length > 0);
        } else {
          setHasMoreUsecases(false);
        }
      } catch (error) {
        console.error('Error loading more usecases:', error);
        toast.error('Error loading more usecases');
      } finally {
        setLoadingUsecases(false);
      }
    };

    fetchMoreUsecases();
  };

  const handleSkillsChange = (event, newValue) => {
    setCourseData((prev) => ({
      ...prev,
      skills: newValue,
    }));
  };

  // Yeni bir outcome eklemek için fonksiyon
  const handleOutcomeAdd = () => {
    setCourseData((prev) => ({
      ...prev,
      outcomes: [
        ...(prev.outcomes || []),
        {
          text: '',
          imageUrl: '',
          imagePosition: 'right',
          formatting: 'html',
        },
      ],
    }));
  };

  // Bir outcome'u silmek için fonksiyon
  const handleOutcomeRemove = (index) => {
    setCourseData((prev) => {
      const newOutcomes = [...(prev.outcomes || [])];
      newOutcomes.splice(index, 1);
      return {
        ...prev,
        outcomes: newOutcomes,
      };
    });
  };

  // Bir outcome'un özelliklerini güncellemek için fonksiyon
  const handleOutcomeChange = (index, field, value) => {
    setCourseData((prev) => {
      const newOutcomes = [...(prev.outcomes || [])];

      // HTML içeriği için formatting alanı da ekleyelim
      if (field === 'text') {
        newOutcomes[index] = {
          ...newOutcomes[index],
          [field]: value,
          formatting: 'html',
        };
      } else {
        newOutcomes[index] = {
          ...newOutcomes[index],
          [field]: value,
        };
      }

      return {
        ...prev,
        outcomes: newOutcomes,
      };
    });
  };

  return (
    <Container maxWidth="xl">
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
        <Typography variant="h4">Add New Course</Typography>
        <Button variant="outlined" onClick={() => navigate(paths.cds.lms.courses.all)}>
          Cancel
        </Button>
      </Stack>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3 }}>
                  Basic Information
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Course Title"
                      name="title"
                      value={courseData.title}
                      onChange={handleBasicInfoChange}
                      error={!!errors.title}
                      helperText={errors.title}
                      required
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Course Description"
                      name="description"
                      value={courseData.description}
                      onChange={handleBasicInfoChange}
                      multiline
                      rows={4}
                      error={!!errors.description}
                      helperText={errors.description}
                      required
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth error={!!errors.category} required>
                      <InputLabel id="category-label">Category</InputLabel>
                      <Select
                        labelId="category-label"
                        name="category"
                        value={courseData.category}
                        onChange={handleBasicInfoChange}
                        label="Category"
                      >
                        {COURSE_CATEGORIES.map((category) => (
                          <MenuItem key={category} value={category}>
                            {category}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.category && <FormHelperText>{errors.category}</FormHelperText>}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth error={!!errors.level} required>
                      <InputLabel id="level-label">Level</InputLabel>
                      <Select
                        labelId="level-label"
                        name="level"
                        value={courseData.level}
                        onChange={handleBasicInfoChange}
                        label="Level"
                      >
                        {COURSE_LEVELS.map((level) => (
                          <MenuItem key={level} value={level}>
                            {level === 'beginner'
                              ? 'Beginner'
                              : level === 'intermediate'
                                ? 'Intermediate'
                                : 'Advanced'}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.level && <FormHelperText>{errors.level}</FormHelperText>}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Duration (e.g., 45m, 1h 30m)"
                      name="duration"
                      value={courseData.duration}
                      onChange={handleBasicInfoChange}
                      error={!!errors.duration}
                      helperText={errors.duration}
                      required
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle1" sx={{ mb: 1 }}>
                        Prerequisites
                      </Typography>
                      {courseData.prerequisites.map((prerequisite, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          spacing={1}
                          alignItems="center"
                          sx={{ mb: 1 }}
                        >
                          <TextField
                            fullWidth
                            value={prerequisite}
                            onChange={(e) =>
                              handleArrayFieldChange('prerequisites', index, e.target.value)
                            }
                            placeholder="Add prerequisite"
                            size="small"
                          />
                          <IconButton
                            onClick={() => handleArrayFieldRemove('prerequisites', index)}
                            disabled={courseData.prerequisites.length === 1 && index === 0}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Stack>
                      ))}
                      <Button
                        startIcon={<AddIcon />}
                        onClick={() => handleArrayFieldAdd('prerequisites')}
                        size="small"
                      >
                        Add Prerequisite
                      </Button>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle1" sx={{ mb: 1 }}>
                        Objectives
                      </Typography>
                      {courseData.objectives.map((objective, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          spacing={1}
                          alignItems="center"
                          sx={{ mb: 1 }}
                        >
                          <TextField
                            fullWidth
                            value={objective}
                            onChange={(e) =>
                              handleArrayFieldChange('objectives', index, e.target.value)
                            }
                            placeholder="Add objective"
                            size="small"
                          />
                          <IconButton
                            onClick={() => handleArrayFieldRemove('objectives', index)}
                            disabled={courseData.objectives.length === 1 && index === 0}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Stack>
                      ))}
                      <Button
                        startIcon={<AddIcon />}
                        onClick={() => handleArrayFieldAdd('objectives')}
                        size="small"
                      >
                        Add Objective
                      </Button>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle1" sx={{ mb: 1 }}>
                        Tags
                      </Typography>
                      {courseData.tags.map((tag, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          spacing={1}
                          alignItems="center"
                          sx={{ mb: 1 }}
                        >
                          <TextField
                            fullWidth
                            value={tag}
                            onChange={(e) => handleArrayFieldChange('tags', index, e.target.value)}
                            placeholder="Add tag"
                            size="small"
                          />
                          <IconButton
                            onClick={() => handleArrayFieldRemove('tags', index)}
                            disabled={courseData.tags.length === 1 && index === 0}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Stack>
                      ))}
                      <Button
                        startIcon={<AddIcon />}
                        onClick={() => handleArrayFieldAdd('tags')}
                        size="small"
                      >
                        Add Tag
                      </Button>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle1" sx={{ mb: 1 }}>
                        Skills Gained
                      </Typography>
                      <Autocomplete
                        multiple
                        id="skills-select"
                        options={[
                          'Generative AI Proficiency',
                          'Content Creation with AI',
                          'Data Extraction and Analysis',
                          'Creative Problem-Solving',
                          'Prompt Engineering',
                          'Deep Learning',
                          'Machine Learning',
                          'Natural Language Processing',
                          'Computer Vision',
                          'Python Programming',
                          'Data Visualization',
                          'Decision Making',
                          'Strategic Planning',
                          'Critical Thinking',
                        ]}
                        freeSolo
                        value={courseData.skills || []}
                        onChange={handleSkillsChange}
                        renderTags={(value, getTagProps) =>
                          value.map((option, index) => (
                            <Chip
                              variant="outlined"
                              label={option}
                              color="primary"
                              {...getTagProps({ index })}
                            />
                          ))
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            variant="outlined"
                            placeholder="Add skills (e.g., Content Creation with AI)"
                            helperText="Enter the skills that students will gain through this course"
                            fullWidth
                          />
                        )}
                      />
                    </Box>
                  </Grid>

                  {/* Learning Outcomes Bölümü */}
                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle1" sx={{ mb: 1 }}>
                        Learning Outcomes
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Add what students will achieve after completing this course
                      </Typography>

                      {(courseData.outcomes || []).map((outcome, index) => (
                        <Paper key={index} variant="outlined" sx={{ p: 2, mb: 2 }}>
                          <Stack
                            direction="row"
                            justifyContent="space-between"
                            alignItems="center"
                            sx={{ mb: 2 }}
                          >
                            <Typography variant="subtitle2">Outcome {index + 1}</Typography>
                            <IconButton
                              color="error"
                              onClick={() => handleOutcomeRemove(index)}
                              size="small"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Stack>

                          <Grid container spacing={2}>
                            <Grid item xs={12}>
                              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                Outcome Description
                              </Typography>
                              <Box
                                sx={{
                                  border: '1px solid #e0e0e0',
                                  borderRadius: 1,
                                  minHeight: '200px',
                                  mb: 1,
                                }}
                              >
                                <BundledEditor
                                  onInit={(_evt, editorInstance) => {
                                    editorRefs.current[`outcome-editor-${index}`] = editorInstance;
                                  }}
                                  value={outcome.text || ''}
                                  init={{
                                    height: 250,
                                    menubar: false,
                                    plugins: [
                                      'advlist',
                                      'anchor',
                                      'autolink',
                                      'image',
                                      'media',
                                      'link',
                                      'lists',
                                      'searchreplace',
                                      'codesample',
                                      'table',
                                      'wordcount',
                                    ],
                                    toolbar:
                                      'undo redo | blocks | ' +
                                      'bold italic forecolor | codesample | alignleft aligncenter ' +
                                      'alignright alignjustify | bullist numlist outdent indent | ' +
                                      'removeformat | image | media ',
                                    content_style:
                                      'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                                    base_url: '/tinymce',
                                    skin: 'oxide',
                                    skin_url: '/tinymce/skins/ui/oxide',
                                    content_css: '/tinymce/skins/content/default/content.css',
                                    image_advtab: true,
                                    image_dimensions: true,
                                    media_alt_source: true,
                                    media_poster: true,
                                    inline: false,
                                    dialog_type: 'window',
                                    media_filter_html: false,
                                    extended_valid_elements:
                                      'iframe[src|frameborder|style|scrolling|class|width|height|name|align]',
                                    convert_urls: false,
                                    relative_urls: false,
                                    remove_script_host: false,
                                  }}
                                  onEditorChange={(content) =>
                                    handleOutcomeChange(index, 'text', content)
                                  }
                                />
                              </Box>
                            </Grid>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                label="Image URL"
                                value={outcome.imageUrl || ''}
                                onChange={(e) =>
                                  handleOutcomeChange(index, 'imageUrl', e.target.value)
                                }
                                placeholder="Add an image URL to illustrate this outcome"
                              />
                            </Grid>
                            <Grid item xs={12}>
                              <FormControl component="fieldset">
                                <FormLabel component="legend">Image Position</FormLabel>
                                <RadioGroup
                                  row
                                  value={outcome.imagePosition || 'right'}
                                  onChange={(e) =>
                                    handleOutcomeChange(index, 'imagePosition', e.target.value)
                                  }
                                >
                                  <FormControlLabel value="left" control={<Radio />} label="Left" />
                                  <FormControlLabel
                                    value="right"
                                    control={<Radio />}
                                    label="Right"
                                  />
                                </RadioGroup>
                              </FormControl>
                            </Grid>
                          </Grid>
                        </Paper>
                      ))}

                      <Button
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={handleOutcomeAdd}
                        sx={{ mt: 1 }}
                      >
                        Add Outcome
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Course Media */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3 }}>
                  Course Media
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Cover Image URL"
                      name="coverImage"
                      value={courseData.coverImage}
                      onChange={handleBasicInfoChange}
                      helperText="Provide a URL for the course cover image"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Provider Image URL"
                      name="providerImage"
                      value={courseData.providerImage}
                      onChange={handleBasicInfoChange}
                      helperText="Provide a URL for the provider logo"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Course Settings */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3 }}>
                  Course Settings
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <FormControl>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={courseData.certificateAvailable}
                            onChange={(e) =>
                              setCourseData((prev) => ({
                                ...prev,
                                certificateAvailable: e.target.checked,
                              }))
                            }
                          />
                        }
                        label="Certificate Available"
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Instructors */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Typography variant="h6">Instructors</Typography>
                  <Button
                    startIcon={<AddIcon />}
                    onClick={() =>
                      setCourseData((prev) => ({
                        ...prev,
                        instructors: [
                          ...prev.instructors,
                          {
                            name: '',
                            role: '',
                            company: '',
                            bio: '',
                          },
                        ],
                      }))
                    }
                  >
                    Add Instructor
                  </Button>
                </Box>

                {courseData.instructors.map((instructor, index) => (
                  <Paper key={index} elevation={2} sx={{ p: 2, mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="subtitle1">Instructor {index + 1}</Typography>
                      <IconButton
                        onClick={() =>
                          setCourseData((prev) => ({
                            ...prev,
                            instructors: prev.instructors.filter((_, i) => i !== index),
                          }))
                        }
                        disabled={courseData.instructors.length === 1}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>

                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Name"
                          value={instructor.name}
                          onChange={(e) => {
                            const newInstructors = [...courseData.instructors];
                            newInstructors[index] = {
                              ...newInstructors[index],
                              name: e.target.value,
                            };
                            setCourseData((prev) => ({
                              ...prev,
                              instructors: newInstructors,
                            }));
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Role"
                          value={instructor.role}
                          onChange={(e) => {
                            const newInstructors = [...courseData.instructors];
                            newInstructors[index] = {
                              ...newInstructors[index],
                              role: e.target.value,
                            };
                            setCourseData((prev) => ({
                              ...prev,
                              instructors: newInstructors,
                            }));
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Company"
                          value={instructor.company}
                          onChange={(e) => {
                            const newInstructors = [...courseData.instructors];
                            newInstructors[index] = {
                              ...newInstructors[index],
                              company: e.target.value,
                            };
                            setCourseData((prev) => ({
                              ...prev,
                              instructors: newInstructors,
                            }));
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Bio"
                          value={instructor.bio}
                          multiline
                          rows={2}
                          onChange={(e) => {
                            const newInstructors = [...courseData.instructors];
                            newInstructors[index] = {
                              ...newInstructors[index],
                              bio: e.target.value,
                            };
                            setCourseData((prev) => ({
                              ...prev,
                              instructors: newInstructors,
                            }));
                          }}
                        />
                      </Grid>
                    </Grid>
                  </Paper>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Chapters and Topics */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Typography variant="h6">Chapters and Topics</Typography>
                  <Button startIcon={<AddIcon />} onClick={handleChapterAdd}>
                    Add Chapter
                  </Button>
                </Box>

                {courseData.chapters.map((chapter, chapterIndex) => (
                  <Paper
                    key={chapterIndex}
                    elevation={2}
                    sx={{ p: 2, mb: 3, position: 'relative' }}
                  >
                    <Box sx={{ position: 'absolute', top: 10, right: 10 }}>
                      <IconButton
                        onClick={() => handleChapterDelete(chapterIndex)}
                        disabled={courseData.chapters.length === 1}
                        color="error"
                        size="small"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>

                    <Typography variant="subtitle1" sx={{ mb: 2 }}>
                      Chapter {chapterIndex + 1}
                    </Typography>

                    <Grid container spacing={2} sx={{ mb: 3 }}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Chapter Title"
                          value={chapter.title}
                          onChange={(e) =>
                            handleChapterChange(chapterIndex, 'title', e.target.value)
                          }
                          error={
                            errors.chapters &&
                            errors.chapters[chapterIndex] &&
                            !!errors.chapters[chapterIndex].title
                          }
                          helperText={
                            errors.chapters &&
                            errors.chapters[chapterIndex] &&
                            errors.chapters[chapterIndex].title
                          }
                          required
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Chapter Description"
                          value={chapter.description}
                          onChange={(e) =>
                            handleChapterChange(chapterIndex, 'description', e.target.value)
                          }
                        />
                      </Grid>
                    </Grid>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="subtitle2">Topics</Typography>
                      <Button
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={() => handleTopicAdd(chapterIndex)}
                      >
                        Add Topic
                      </Button>
                    </Box>

                    {chapter.topics.map((topic, topicIndex) => (
                      <Paper key={topicIndex} variant="outlined" sx={{ p: 2, mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                          <Typography variant="body2">Topic {topicIndex + 1}</Typography>
                          <IconButton
                            onClick={() => handleTopicDelete(chapterIndex, topicIndex)}
                            disabled={chapter.topics.length === 1}
                            color="error"
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>

                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <TextField
                              fullWidth
                              label="Topic Title"
                              value={topic.title}
                              onChange={(e) =>
                                handleTopicChange(chapterIndex, topicIndex, 'title', e.target.value)
                              }
                              error={
                                errors.chapters &&
                                errors.chapters[chapterIndex] &&
                                errors.chapters[chapterIndex].topics &&
                                errors.chapters[chapterIndex].topics[topicIndex] &&
                                !!errors.chapters[chapterIndex].topics[topicIndex].title
                              }
                              helperText={
                                errors.chapters &&
                                errors.chapters[chapterIndex] &&
                                errors.chapters[chapterIndex].topics &&
                                errors.chapters[chapterIndex].topics[topicIndex] &&
                                errors.chapters[chapterIndex].topics[topicIndex].title
                              }
                              size="small"
                              required
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <TextField
                              fullWidth
                              label="Topic Description"
                              value={topic.description}
                              onChange={(e) =>
                                handleTopicChange(
                                  chapterIndex,
                                  topicIndex,
                                  'description',
                                  e.target.value
                                )
                              }
                              size="small"
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <TextField
                              fullWidth
                              label="Video Duration (e.g., 10:00)"
                              value={topic.video?.duration || '00:00'}
                              onChange={(e) =>
                                handleTopicChange(chapterIndex, topicIndex, 'video', {
                                  duration: e.target.value,
                                })
                              }
                              size="small"
                              placeholder="MM:SS"
                            />
                          </Grid>
                        </Grid>

                        <Box sx={{ mt: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="body2">Content Blocks</Typography>
                            <Button
                              size="small"
                              startIcon={<AddIcon />}
                              onClick={() => handleContentBlockAdd(chapterIndex, topicIndex)}
                            >
                              Add Content Block
                            </Button>
                          </Box>

                          {topic.contentBlocks.map((block, blockIndex) => (
                            <Paper key={blockIndex} variant="outlined" sx={{ p: 2, mb: 2 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                                <Typography variant="body2">
                                  Content Block {blockIndex + 1}
                                </Typography>
                                <IconButton
                                  onClick={() =>
                                    handleContentBlockDelete(chapterIndex, topicIndex, blockIndex)
                                  }
                                  color="error"
                                  size="small"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Box>

                              <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                  <FormControl fullWidth size="small">
                                    <InputLabel>Content Type</InputLabel>
                                    <Select
                                      value={block.displayType || block.type || 'text'}
                                      onChange={(e) => {
                                        handleContentBlockChange(
                                          chapterIndex,
                                          topicIndex,
                                          blockIndex,
                                          'type',
                                          e.target.value
                                        );
                                      }}
                                      label="Content Type"
                                    >
                                      {CONTENT_BLOCK_TYPES.map((type) => (
                                        <MenuItem key={type} value={type}>
                                          {type === 'text'
                                            ? 'Text'
                                            : type === 'video'
                                              ? 'Video'
                                              : type === 'quiz'
                                                ? 'Quiz'
                                                : type === 'usecase'
                                                  ? 'Use Case'
                                                  : type === 'pdf'
                                                    ? 'PDF'
                                                    : type === 'playground-gpt'
                                                      ? 'Playground GPT'
                                                      : type === 'playground-dalle'
                                                        ? 'Playground DALL-E'
                                                        : type === 'playground-heygen'
                                                          ? 'Playground Heygen'
                                                          : type === 'form'
                                                            ? 'Form'
                                                            : type.charAt(0).toUpperCase() +
                                                              type.slice(1)}
                                        </MenuItem>
                                      ))}
                                    </Select>
                                  </FormControl>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    label="Content Title"
                                    value={block.title}
                                    onChange={(e) =>
                                      handleContentBlockChange(
                                        chapterIndex,
                                        topicIndex,
                                        blockIndex,
                                        'title',
                                        e.target.value
                                      )
                                    }
                                  />
                                </Grid>

                                {block.type === 'text' &&
                                  block.displayType !== 'usecase' &&
                                  block.displayType !== 'playground' && (
                                    <Grid item xs={12}>
                                      <Box
                                        sx={{
                                          mt: 2,
                                          border: '1px solid #e0e0e0',
                                          minHeight: '300px',
                                        }}
                                      >
                                        <BundledEditor
                                          onInit={(_evt, editorInstance) => {
                                            editorRefs.current[
                                              `editor-${chapterIndex}-${topicIndex}-${blockIndex}`
                                            ] = editorInstance;
                                          }}
                                          value={block.textContent?.content || ''}
                                          init={{
                                            height: 500,
                                            menubar: false,
                                            plugins: [
                                              'advlist',
                                              'anchor',
                                              'autolink',
                                              'image',
                                              'media',
                                              'link',
                                              'lists',
                                              'searchreplace',
                                              'codesample',
                                              'table',
                                              'wordcount',
                                            ],
                                            toolbar:
                                              'undo redo | blocks | ' +
                                              'bold italic forecolor | codesample | alignleft aligncenter ' +
                                              'alignright alignjustify | bullist numlist outdent indent | ' +
                                              'removeformat | image | media ',
                                            content_style:
                                              'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                                            base_url: '/tinymce',
                                            skin: 'oxide',
                                            skin_url: '/tinymce/skins/ui/oxide',
                                            content_css:
                                              '/tinymce/skins/content/default/content.css',
                                            image_advtab: true,
                                            image_dimensions: true,
                                            media_alt_source: true,
                                            media_poster: true,
                                            inline: false,
                                            dialog_type: 'window',
                                            media_filter_html: false,
                                            extended_valid_elements:
                                              'iframe[src|frameborder|style|scrolling|class|width|height|name|align]',
                                            convert_urls: false,
                                            relative_urls: false,
                                            remove_script_host: false,
                                          }}
                                          onEditorChange={(content) => {
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'textContent',
                                              {
                                                content,
                                                formatting: 'html',
                                              }
                                            );
                                          }}
                                        />
                                      </Box>
                                    </Grid>
                                  )}

                                {block.displayType === 'playground' &&
                                  block.playground_type === 'gpt' && (
                                    <>
                                      <Grid item xs={12}>
                                        <TextField
                                          fullWidth
                                          multiline
                                          rows={4}
                                          size="small"
                                          label="Initial Prompt"
                                          value={block.initialPrompt || ''}
                                          onChange={(e) =>
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'initialPrompt',
                                              e.target.value
                                            )
                                          }
                                        />
                                      </Grid>
                                      <Grid item xs={12} md={6}>
                                        <TextField
                                          fullWidth
                                          type="number"
                                          size="small"
                                          label="Temperature"
                                          value={block.temperature || 0.7}
                                          onChange={(e) =>
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'temperature',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                          inputProps={{ min: 0, max: 1, step: 0.1 }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} md={6}>
                                        <TextField
                                          fullWidth
                                          type="number"
                                          size="small"
                                          label="Top P"
                                          value={block.topP || 1}
                                          onChange={(e) =>
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'topP',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                          inputProps={{ min: 0, max: 1, step: 0.1 }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} md={6}>
                                        <TextField
                                          fullWidth
                                          type="number"
                                          size="small"
                                          label="Frequency Penalty"
                                          value={block.frequencyPenalty || 0}
                                          onChange={(e) =>
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'frequencyPenalty',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                          inputProps={{ min: 0, max: 2, step: 0.1 }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} md={6}>
                                        <TextField
                                          fullWidth
                                          type="number"
                                          size="small"
                                          label="Presence Penalty"
                                          value={block.presencePenalty || 0}
                                          onChange={(e) =>
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'presencePenalty',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                          inputProps={{ min: 0, max: 2, step: 0.1 }}
                                        />
                                      </Grid>
                                    </>
                                  )}

                                {block.displayType === 'playground' &&
                                  block.playground_type === 'dalle' && (
                                    <Grid item xs={12}>
                                      <TextField
                                        fullWidth
                                        multiline
                                        rows={4}
                                        size="small"
                                        label="Initial Prompt"
                                        value={block.initialPrompt || ''}
                                        onChange={(e) =>
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'initialPrompt',
                                            e.target.value
                                          )
                                        }
                                      />
                                    </Grid>
                                  )}

                                {block.displayType === 'playground' &&
                                  block.playground_type === 'heygen' && (
                                    <Grid item xs={12}>
                                      <TextField
                                        fullWidth
                                        size="small"
                                        label="Heygen Video ID"
                                        value={block.heygen_id || ''}
                                        onChange={(e) =>
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'heygen_id',
                                            e.target.value
                                          )
                                        }
                                      />
                                    </Grid>
                                  )}

                                {block.type === 'video' && (
                                  <>
                                    <Grid item xs={12}>
                                      <FormControl fullWidth size="small">
                                        <InputLabel>Video Type</InputLabel>
                                        <Select
                                          value={block.videoContent?.type || 'youtube'}
                                          onChange={(e) => {
                                            const newType = e.target.value;
                                            let updatedVideoContent;

                                            if (newType === 'youtube') {
                                              updatedVideoContent = {
                                                type: 'youtube',
                                                youtubeUrl: '',
                                                hlsData: null,
                                                thumbnail: block.videoContent?.thumbnail || '',
                                              };
                                            } else if (newType === 'hls') {
                                              updatedVideoContent = {
                                                type: 'hls',
                                                youtubeUrl: '',
                                                hlsData: {
                                                  streamingUrls: [
                                                    {
                                                      paths: [''],
                                                      _id: generateId(),
                                                    },
                                                  ],
                                                },
                                                thumbnail: block.videoContent?.thumbnail || '',
                                              };
                                            }

                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'videoContent',
                                              updatedVideoContent
                                            );
                                          }}
                                          label="Video Type"
                                        >
                                          <MenuItem value="youtube">YouTube</MenuItem>
                                          <MenuItem value="hls">HLS</MenuItem>
                                        </Select>
                                      </FormControl>
                                    </Grid>
                                    <Grid item xs={12}>
                                      {block.videoContent?.type === 'hls' ? (
                                        <TextField
                                          fullWidth
                                          size="small"
                                          label="HLS Video URL"
                                          value={
                                            block.videoContent?.hlsData?.streamingUrls?.[0]
                                              ?.paths?.[0] || ''
                                          }
                                          onChange={(e) => {
                                            const newValue = e.target.value;
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'videoContent',
                                              {
                                                ...block.videoContent,
                                                type: 'hls',
                                                hlsData: {
                                                  streamingUrls: [
                                                    {
                                                      paths: [newValue],
                                                      _id:
                                                        block.videoContent?.hlsData
                                                          ?.streamingUrls?.[0]?._id || generateId(),
                                                    },
                                                  ],
                                                },
                                              }
                                            );
                                          }}
                                        />
                                      ) : (
                                        <TextField
                                          fullWidth
                                          size="small"
                                          label="YouTube URL"
                                          value={block.videoContent?.youtubeUrl || ''}
                                          onChange={(e) => {
                                            const newValue = e.target.value;
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'videoContent',
                                              {
                                                ...block.videoContent,
                                                type: 'youtube',
                                                youtubeUrl: newValue,
                                              }
                                            );
                                          }}
                                        />
                                      )}
                                    </Grid>
                                    <Grid item xs={12}>
                                      <TextField
                                        fullWidth
                                        size="small"
                                        label="Thumbnail URL"
                                        value={block.videoContent?.thumbnail || ''}
                                        onChange={(e) =>
                                          handleContentBlockChange(
                                            chapterIndex,
                                            topicIndex,
                                            blockIndex,
                                            'videoContent',
                                            {
                                              ...block.videoContent,
                                              thumbnail: e.target.value,
                                            }
                                          )
                                        }
                                      />
                                    </Grid>
                                  </>
                                )}

                                {block.displayType === 'usecase' && (
                                  <Grid item xs={12}>
                                    {loadingUsecases ? (
                                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                        <CircularProgress size={24} />
                                      </Box>
                                    ) : (
                                      <>
                                        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                                          <Autocomplete
                                            id={`usecase-autocomplete-${chapterIndex}-${topicIndex}-${blockIndex}`}
                                            options={memoizedUsecaseOptions}
                                            getOptionLabel={(option) =>
                                              typeof option === 'string'
                                                ? option
                                                : option.title || 'Untitled Usecase'
                                            }
                                            isOptionEqualToValue={(option, value) =>
                                              option?.slug === value?.slug ||
                                              option?._id === value?._id ||
                                              option?.uniqueid === value?.uniqueid
                                            }
                                            value={
                                              usecases.find(
                                                (u) =>
                                                  u.slug === block.usecase_slug ||
                                                  u._id === block.usecase_slug ||
                                                  u.uniqueid === block.usecase_slug
                                              ) || null
                                            }
                                            inputValue={getUsecaseInputValue(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex
                                            )}
                                            onInputChange={(event, newInputValue, reason) => {
                                              if (reason === 'reset' || reason === 'clear') return;

                                              updateUsecaseInputValue(
                                                chapterIndex,
                                                topicIndex,
                                                blockIndex,
                                                newInputValue
                                              );

                                              setUsecaseSearch(newInputValue);
                                            }}
                                            onChange={(event, newValue) => {
                                              if (!newValue) return;

                                              updateUsecaseInputValue(
                                                chapterIndex,
                                                topicIndex,
                                                blockIndex,
                                                newValue.title || ''
                                              );

                                              const selectedValue =
                                                newValue.slug || newValue._id || newValue.uniqueid;

                                              handleContentBlockChange(
                                                chapterIndex,
                                                topicIndex,
                                                blockIndex,
                                                'usecase_slug',
                                                selectedValue
                                              );
                                            }}
                                            renderInput={(params) => (
                                              <TextField
                                                {...params}
                                                label="Select Usecase"
                                                placeholder="Search usecases..."
                                                InputProps={{
                                                  ...params.InputProps,
                                                  endAdornment: (
                                                    <>
                                                      {loadingUsecases ? (
                                                        <CircularProgress size={20} />
                                                      ) : null}
                                                      {params.InputProps.endAdornment}
                                                    </>
                                                  ),
                                                }}
                                              />
                                            )}
                                          />
                                        </FormControl>
                                      </>
                                    )}
                                  </Grid>
                                )}

                                {block.type === 'quiz' && (
                                  <Grid item xs={12}>
                                    {loadingQuizzes ? (
                                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                        <CircularProgress size={24} />
                                      </Box>
                                    ) : (
                                      <FormControl fullWidth size="small">
                                        <InputLabel>Select Quiz</InputLabel>
                                        <Select
                                          value={block.quiz?._id || block.quiz?.id || ''}
                                          onChange={(e) => {
                                            const selectedQuiz = quizzes.find(
                                              (q) => q._id === e.target.value
                                            );
                                            handleContentBlockChange(
                                              chapterIndex,
                                              topicIndex,
                                              blockIndex,
                                              'quiz',
                                              {
                                                _id: e.target.value,
                                                id: e.target.value,
                                                quiz_id: e.target.value, // Backend için gerekli alan: quiz_id
                                                title: selectedQuiz?.title || '',
                                                questions:
                                                  selectedQuiz?.quiz?.questions ||
                                                  selectedQuiz?.questions ||
                                                  [],
                                              }
                                            );
                                          }}
                                          label="Select Quiz"
                                        >
                                          {quizzes.map((quiz) => (
                                            <MenuItem key={quiz._id} value={quiz._id}>
                                              {quiz.title}
                                            </MenuItem>
                                          ))}
                                        </Select>
                                        <FormHelperText>Select a quiz from the list</FormHelperText>
                                      </FormControl>
                                    )}
                                  </Grid>
                                )}

                                {block.type === 'pdf' && (
                                  <Grid item xs={12}>
                                    <Box
                                      sx={{
                                        mt: 2,
                                        p: 3,
                                        border: '2px dashed',
                                        borderColor: 'divider',
                                        borderRadius: 1,
                                        textAlign: 'center',
                                        cursor: 'pointer',
                                        '&:hover': {
                                          borderColor: 'primary.main',
                                          bgcolor: 'action.hover',
                                        },
                                      }}
                                      onClick={() => {
                                        const input = document.createElement('input');
                                        input.setAttribute('type', 'file');
                                        input.setAttribute('accept', '.pdf');

                                        input.onchange = async function () {
                                          const file = this.files[0];
                                          if (file) {
                                            try {
                                              const uploadedFile = await uploadMedia('pdf', file);
                                              if (uploadedFile) {
                                                handleContentBlockChange(
                                                  chapterIndex,
                                                  topicIndex,
                                                  blockIndex,
                                                  'pdfContent',
                                                  {
                                                    url: uploadedFile.url,
                                                    fileName: file.name,
                                                    fileSize: file.size,
                                                  }
                                                );
                                              }
                                            } catch (error) {
                                              console.error('Error uploading PDF:', error);
                                              toast.error('PDF yüklenirken bir hata oluştu');
                                            }
                                          }
                                        };

                                        input.click();
                                      }}
                                    >
                                      {block.pdfContent?.url ? (
                                        <Box>
                                          <Box
                                            sx={{
                                              display: 'flex',
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              mb: 1,
                                            }}
                                          >
                                            <PdfIcon color="primary" sx={{ fontSize: 40, mr: 1 }} />
                                            <Typography variant="body1" fontWeight="bold">
                                              {block.pdfContent.fileName || 'PDF Dosyası'}
                                            </Typography>
                                          </Box>
                                          <Typography variant="body2" color="text.secondary">
                                            {block.pdfContent.fileSize
                                              ? `${Math.round(block.pdfContent.fileSize / 1024)} KB`
                                              : ''}
                                          </Typography>
                                          <Box sx={{ mt: 1 }}>
                                            <Button
                                              size="small"
                                              variant="outlined"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                window.open(block.pdfContent.url, '_blank');
                                              }}
                                            >
                                              Önizle
                                            </Button>
                                            <Button
                                              size="small"
                                              color="error"
                                              sx={{ ml: 1 }}
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleContentBlockChange(
                                                  chapterIndex,
                                                  topicIndex,
                                                  blockIndex,
                                                  'pdfContent',
                                                  {
                                                    url: '',
                                                    fileName: '',
                                                    fileSize: 0,
                                                  }
                                                );
                                              }}
                                            >
                                              Kaldır
                                            </Button>
                                          </Box>
                                        </Box>
                                      ) : (
                                        <Box>
                                          <CloudUploadIcon
                                            color="primary"
                                            sx={{ fontSize: 40, mb: 1 }}
                                          />
                                          <Typography variant="body1">PDF Dosyası Yükle</Typography>
                                          <Typography variant="body2" color="text.secondary">
                                            Dosyayı seçmek için tıklayın veya sürükleyip bırakın
                                          </Typography>
                                        </Box>
                                      )}
                                    </Box>
                                  </Grid>
                                )}
                              </Grid>
                            </Paper>
                          ))}
                        </Box>
                      </Paper>
                    ))}
                  </Paper>
                ))}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Stack direction="row" justifyContent="flex-end" spacing={2}>
              <Button variant="outlined" onClick={() => navigate(paths.cds.lms.courses.all)}>
                Cancel
              </Button>
              <LoadingButton loading={loading} type="submit" variant="contained" color="primary">
                Create Course
              </LoadingButton>
            </Stack>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
}

// Helper function to determine usecase type
const determineUsecaseType = (usecase) => {
  if (!usecase) return 'text';

  let type = usecase.type || usecase.use_case_type;

  if (Array.isArray(type)) {
    type = type[0] || '';
  }

  type = String(type).toLowerCase();

  if (type.includes('text')) return 'text';
  if (type.includes('image')) return 'image';
  if (type.includes('video')) return 'video';
  if (type.includes('assistant')) return 'assistant';

  return 'text';
};
