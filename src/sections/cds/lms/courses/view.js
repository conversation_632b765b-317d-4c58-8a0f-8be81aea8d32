import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { Container, Typography, Button, Card, CardContent, Grid, Box, Stack } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { paths } from 'src/routes/paths';
import { useCourses } from 'src/apis/useCourses';
import Iconify from 'src/components/iconify';

function CourseView() {
  const { getCourses, courses, loading } = useCourses();
  const [featuredCourses, setFeaturedCourses] = useState([]);

  useEffect(() => {
    const fetchCourses = async () => {
      await getCourses();
    };
    fetchCourses();
  }, [getCourses]);

  useEffect(() => {
    if (courses && courses.length > 0) {
      // Show first 3 courses as featured
      setFeaturedCourses(courses.slice(0, 3));
    }
  }, [courses]);

  return (
    <Container maxWidth="xl">
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
        <Typography variant="h4">Courses</Typography>
        <Button
          component={RouterLink}
          to={paths.cds.lms.courses.add}
          variant="contained"
          startIcon={<AddIcon />}
        >
          Add New Course
        </Button>
      </Stack>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6">Featured Courses</Typography>
                <Button
                  component={RouterLink}
                  to={paths.cds.courses.all}
                  endIcon={<Iconify icon="eva:arrow-ios-forward-fill" />}
                >
                  View All
                </Button>
              </Box>

              <Grid container spacing={3}>
                {loading ? (
                  <Typography>Loading...</Typography>
                ) : featuredCourses && featuredCourses.length > 0 ? (
                  featuredCourses.map((course) => (
                    <Grid item xs={12} sm={6} md={4} key={course._id}>
                      <Card>
                        <Box
                          component="img"
                          src={course.coverImage || '/assets/images/covers/cover_1.jpg'}
                          alt={course.title}
                          sx={{ height: 200, width: '100%', objectFit: 'cover' }}
                        />
                        <CardContent>
                          <Typography variant="h6" noWrap>
                            {course.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }} noWrap>
                            {course.description}
                          </Typography>
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography variant="caption" sx={{ color: 'primary.main' }}>
                              {course.level}
                            </Typography>
                            <Typography variant="caption">{course.duration}</Typography>
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Typography>No courses available yet.</Typography>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}

export default CourseView;
