import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typo<PERSON>,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Stack,
  Box,
  MenuItem,
  IconButton,
  Divider,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  FormControlLabel,
  Switch,
  CircularProgress,
  Autocomplete,
  FormLabel,
  RadioGroup,
  Radio,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  DragHandle as DragHandleIcon,
  CloudUpload as CloudUploadIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { paths } from 'src/routes/paths';
import { useCourses } from 'src/apis/useCourses';
import { useQuizzes } from 'src/apis/useQuizzes';
import { toast } from 'react-toastify';
import { LoadingButton } from '@mui/lab';
import { useUsecases } from 'src/apis/useUsecases';
import BundledEditor from 'src/components/text-editor/editor';
import { useMedia } from 'src/apis/useMedia';

import { BasicInformation } from './components/BasicInformation';
import { CourseMedia } from './components/CourseMedia';
import { CourseSettings } from './components/CourseSettings';
import { Instructors } from './components/Instructors';
import { ChaptersAndTopics } from './components/ChaptersAndTopics';
import defaultCourseData from 'src/utils/defaultCourseData.json';

export default function AddCourseView() {
  const navigate = useNavigate();
  const { addCourse } = useCourses();
  const { getQuizzes } = useQuizzes();
  const { getUsecases } = useUsecases();
  const { uploadMedia } = useMedia();
  const [loading, setLoading] = useState(false);
  const [quizzes, setQuizzes] = useState([]);
  const [loadingQuizzes, setLoadingQuizzes] = useState(false);
  const [errors, setErrors] = useState({});
  const [courseData, setCourseData] = useState(defaultCourseData[0]);
  const [usecases, setUsecases] = useState([]);
  const [loadingUsecases, setLoadingUsecases] = useState(false);
  const [usecaseSearch, setUsecaseSearch] = useState('');
  const [usecaseSearchDebounced, setUsecaseSearchDebounced] = useState('');
  const [usecaseInputValues, setUsecaseInputValues] = useState({});
  const [usecasePage, setUsecasePage] = useState(1);
  const [usecaseOptions, setUsecaseOptions] = useState([]);
  const [hasMoreUsecases, setHasMoreUsecases] = useState(true);

  const editorRefs = useRef({});

  const renderEditor = (chapterIndex, topicIndex, blockIndex, block) => {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ml: 2,
          mt: 2,
          width: '100%',
        }}
      >
        <BundledEditor
          onInit={(_evt, editorInstance) => {
            editorRefs.current[`editor-${chapterIndex}-${topicIndex}-${blockIndex}`] =
              editorInstance;
          }}
          value={block.textContent?.content || ''}
          init={{
            height: 500,
            menubar: false,
            plugins: [
              'advlist',
              'anchor',
              'autolink',
              'image',
              'media',
              'link',
              'lists',
              'searchreplace',
              'codesample',
              'table',
              'wordcount',
            ],
            toolbar:
              'undo redo | blocks | ' +
              'bold italic forecolor | codesample | alignleft aligncenter ' +
              'alignright alignjustify | bullist numlist outdent indent | ' +
              'removeformat | image | media ',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            base_url: '/tinymce',
            skin: 'oxide',
            skin_url: '/tinymce/skins/ui/oxide',
            content_css: '/tinymce/skins/content/default/content.css',
            image_advtab: true,
            image_dimensions: true,
            media_alt_source: true,
            media_poster: true,
            inline: false,
            dialog_type: 'window',
            media_filter_html: false,
            extended_valid_elements:
              'iframe[src|frameborder|style|scrolling|class|width|height|name|align]',
            convert_urls: false,
            relative_urls: false,
            remove_script_host: false,
          }}
          onEditorChange={(content) => {
            handleContentBlockChange(chapterIndex, topicIndex, blockIndex, 'textContent', {
              content,
              formatting: 'html',
            });
          }}
        />
      </Box>
    );
  };

  const validateForm = () => {
    const newErrors = {};

    if (!courseData.title.trim()) {
      newErrors.title = 'Course title is required';
    }

    if (!courseData.description.trim()) {
      newErrors.description = 'Course description is required';
    }

    if (!courseData.category) {
      newErrors.category = 'Category selection is required';
    }

    if (!courseData.level) {
      newErrors.level = 'Level selection is required';
    }

    if (!courseData.duration.trim()) {
      newErrors.duration = 'Course duration is required';
    }

    // Chapter and topic validations
    const chapterErrors = [];
    courseData.chapters.forEach((chapter, chapterIndex) => {
      const chapterError = {};
      if (!chapter.title.trim()) {
        chapterError.title = 'Chapter title is required';
      }

      const topicErrors = [];
      chapter.topics.forEach((topic, topicIndex) => {
        const topicError = {};
        if (!topic.title.trim()) {
          topicError.title = 'Topic title is required';
        }
        if (Object.keys(topicError).length > 0) {
          topicErrors[topicIndex] = topicError;
        }
      });

      if (topicErrors.length > 0) {
        chapterError.topics = topicErrors;
      }

      if (Object.keys(chapterError).length > 0) {
        chapterErrors[chapterIndex] = chapterError;
      }
    });

    if (chapterErrors.length > 0) {
      newErrors.chapters = chapterErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBasicInfoChange = (e) => {
    const { name, value } = e.target;
    setCourseData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Hata varsa temizle
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleChapterAdd = () => {
    setCourseData((prev) => ({
      ...prev,
      chapters: [
        ...prev.chapters,
        {
          title: '',
          description: '',
          order: prev.chapters.length + 1,
          isLocked: true,
          topics: [
            {
              title: '',
              description: '',
              order: 1,
              contentBlocks: [],
            },
          ],
        },
      ],
    }));
  };

  const handleChapterChange = (index, field, value) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, i) =>
        i === index ? { ...chapter, [field]: value } : chapter
      ),
    }));

    // Hata varsa temizle
    if (errors.chapters && errors.chapters[index] && errors.chapters[index][field]) {
      const newErrors = { ...errors };
      newErrors.chapters[index][field] = undefined;
      setErrors(newErrors);
    }
  };

  const handleTopicAdd = (chapterIndex) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, idx) => {
        if (idx === chapterIndex) {
          return {
            ...chapter,
            topics: [
              ...chapter.topics,
              {
                title: '',
                description: '',
                order: chapter.topics.length + 1,
                contentBlocks: [],
              },
            ],
          };
        }
        return chapter;
      }),
    }));
  };

  const handleTopicChange = (chapterIndex, topicIndex, field, value) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, chIdx) => {
        if (chIdx === chapterIndex) {
          return {
            ...chapter,
            topics: chapter.topics.map((topic, topIdx) => {
              if (topIdx === topicIndex) {
                return { ...topic, [field]: value };
              }
              return topic;
            }),
          };
        }
        return chapter;
      }),
    }));

    // Hata varsa temizle
    if (
      errors.chapters &&
      errors.chapters[chapterIndex] &&
      errors.chapters[chapterIndex].topics &&
      errors.chapters[chapterIndex].topics[topicIndex] &&
      errors.chapters[chapterIndex].topics[topicIndex][field]
    ) {
      const newErrors = { ...errors };
      newErrors.chapters[chapterIndex].topics[topicIndex][field] = undefined;
      setErrors(newErrors);
    }
  };

  const handleChapterDelete = (chapterIndex) => {
    setCourseData((prev) => {
      const newChapters = prev.chapters.filter((_, idx) => idx !== chapterIndex);
      // Kalan bölümlerin sırasını güncelle
      return {
        ...prev,
        chapters: newChapters.map((chapter, idx) => ({
          ...chapter,
          order: idx + 1,
        })),
      };
    });
  };

  const handleTopicDelete = (chapterIndex, topicIndex) => {
    setCourseData((prev) => {
      const newChapters = prev.chapters.map((chapter, idx) => {
        if (idx === chapterIndex) {
          const newTopics = chapter.topics.filter((_, tIdx) => tIdx !== topicIndex);
          return {
            ...chapter,
            topics: newTopics.map((topic, idx) => ({
              ...topic,
              order: idx + 1,
            })),
          };
        }
        return chapter;
      });
      return {
        ...prev,
        chapters: newChapters,
      };
    });
  };

  const handleContentBlockAdd = (chapterIndex, topicIndex, blockType = 'text') => {
    setCourseData((prev) => {
      const updatedCourseData = { ...prev };
      const contentBlocks =
        updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks || [];

      // Yeni blok oluştur
      const newBlock = {
        type: blockType,
        title: '',
        description: '',
        order: contentBlocks.length + 1,
      };

      // Tipe göre içerik alanlarını ekle
      switch (blockType) {
        case 'text':
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = 0.7;
          newBlock.topP = 1;
          newBlock.frequencyPenalty = 0;
          newBlock.presencePenalty = 0;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'video':
          newBlock.textContent = null;
          newBlock.videoContent = {
            type: 'youtube', // Default to YouTube
            youtubeUrl: '',
            hlsData: null,
            thumbnail: '',
          };
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = 0.7;
          newBlock.topP = 1;
          newBlock.frequencyPenalty = 0;
          newBlock.presencePenalty = 0;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'quiz':
          newBlock.textContent = null;
          newBlock.videoContent = null;
          newBlock.quiz = {
            quiz_id: '',
          };
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'pdf':
          newBlock.textContent = null;
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = {
            url: '',
            fileName: '',
            fileSize: 0,
          };
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'usecase':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'usecase'; // Görsel olarak usecase göster
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = '';
          newBlock.usecase_type = 'text'; // Geçerli bir usecase_type
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'playground-gpt':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'playground'; // Görsel olarak playground göster
          newBlock.playground_type = 'gpt';
          newBlock.initialPrompt = '';
          newBlock.temperature = 0.7;
          newBlock.topP = 1;
          newBlock.frequencyPenalty = 0;
          newBlock.presencePenalty = 0;
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'playground-dalle':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'playground'; // Görsel olarak playground göster
          newBlock.playground_type = 'dalle';
          newBlock.initialPrompt = '';
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'playground-heygen':
          newBlock.type = 'text'; // MongoDB şeması için geçerli bir tip
          newBlock.displayType = 'playground'; // Görsel olarak playground göster
          newBlock.playground_type = 'heygen';
          newBlock.heygen_id = '';
          newBlock.textContent = {
            content: '',
            formatting: 'markdown',
          };
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
        case 'form':
          newBlock.textContent = null;
          newBlock.videoContent = null;
          newBlock.quiz = null;
          newBlock.usecase_slug = null;
          newBlock.usecase_type = null;
          newBlock.pdfContent = null;
          newBlock.playground_type = null;
          newBlock.initialPrompt = null;
          newBlock.temperature = null;
          newBlock.topP = null;
          newBlock.frequencyPenalty = null;
          newBlock.presencePenalty = null;
          newBlock.heygen_id = null;
          newBlock.form_id = null;
          newBlock.form_data = null;
          break;
      }

      // Yeni bloğu ekle
      contentBlocks.push(newBlock);

      return updatedCourseData;
    });
  };

  const handleContentBlockChange = (chapterIndex, topicIndex, blockIndex, field, value) => {
    setCourseData((prev) => {
      const updatedCourseData = { ...prev };
      const block =
        updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks[blockIndex];

      if (field === 'type') {
        let updatedBlock = {
          ...block,
          type: value,
          title: '',
          description: '',
        };

        // Playground tipleri için displayType alanını ayarla
        if (value === 'playground-gpt') {
          updatedBlock.displayType = 'playground';
          updatedBlock.playground_type = 'gpt';
        } else if (value === 'playground-dalle') {
          updatedBlock.displayType = 'playground';
          updatedBlock.playground_type = 'dalle';
        } else if (value === 'playground-heygen') {
          updatedBlock.displayType = 'playground';
          updatedBlock.playground_type = 'heygen';
        } else if (value === 'usecase') {
          updatedBlock.type = 'usecase';
          updatedBlock.displayType = 'usecase';
          updatedBlock.usecase_slug = '';
          updatedBlock.usecase_type = '';
          updatedBlock.textContent = null;
          updatedBlock.videoContent = null;
          updatedBlock.quiz = null;
          updatedBlock.pdfContent = null;
          updatedBlock.playground_type = null;
          updatedBlock.initialPrompt = null;
          updatedBlock.temperature = null;
          updatedBlock.topP = null;
          updatedBlock.frequencyPenalty = null;
          updatedBlock.presencePenalty = null;
          updatedBlock.heygen_id = null;
          updatedBlock.form_id = null;
          updatedBlock.form_data = null;
        } else {
          updatedBlock.displayType = null;
        }

        switch (value) {
          case 'text':
            updatedBlock.textContent = {
              content: '',
              formatting: 'markdown',
            };
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'video':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = {
              type: 'youtube',
              youtubeUrl: '',
              hlsData: null,
              thumbnail: '',
            };
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'quiz':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = null;
            updatedBlock.quiz = {
              quiz_id: '',
            };
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'pdf':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = {
              url: '',
              fileName: '',
              fileSize: 0,
            };
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'usecase':
            updatedBlock.type = 'usecase';
            updatedBlock.displayType = 'usecase';
            updatedBlock.textContent = null;
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = '';
            updatedBlock.usecase_type = '';
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
          case 'form':
            updatedBlock.textContent = null;
            updatedBlock.videoContent = null;
            updatedBlock.quiz = null;
            updatedBlock.usecase_slug = null;
            updatedBlock.usecase_type = null;
            updatedBlock.pdfContent = null;
            updatedBlock.playground_type = null;
            updatedBlock.initialPrompt = null;
            updatedBlock.temperature = null;
            updatedBlock.topP = null;
            updatedBlock.frequencyPenalty = null;
            updatedBlock.presencePenalty = null;
            updatedBlock.heygen_id = null;
            updatedBlock.form_id = null;
            updatedBlock.form_data = null;
            break;
        }
        updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks[blockIndex] =
          updatedBlock;
      } else if (field === 'textContent' && block.type === 'text') {
        block.textContent = value;
      } else if (field === 'pdfContent' && block.type === 'pdf') {
        block.pdfContent = value;
      } else if (field === 'initialPrompt' && block.playground_type) {
        block.initialPrompt = value;
      } else if (field === 'temperature' && block.playground_type === 'gpt') {
        block.temperature = value;
      } else if (field === 'topP' && block.playground_type === 'gpt') {
        block.topP = value;
      } else if (field === 'frequencyPenalty' && block.playground_type === 'gpt') {
        block.frequencyPenalty = value;
      } else if (field === 'presencePenalty' && block.playground_type === 'gpt') {
        block.presencePenalty = value;
      } else if (field === 'heygen_id' && block.playground_type === 'heygen') {
        block.heygen_id = value;
      } else if (field === 'usecase') {
        try {
          updatedCourseData.chapters[chapterIndex].topics[topicIndex].contentBlocks[blockIndex] = {
            ...block,
            ...value,
          };
        } catch (error) {
          console.error('Error updating usecase:', error);
        }
      } else if (field === 'quiz') {
        block.quiz = { ...value };
      } else {
        block[field] = value;
      }

      return updatedCourseData;
    });
  };

  const handleContentBlockDelete = (chapterIndex, topicIndex, blockIndex) => {
    setCourseData((prev) => ({
      ...prev,
      chapters: prev.chapters.map((chapter, chIdx) => {
        if (chIdx === chapterIndex) {
          return {
            ...chapter,
            topics: chapter.topics.map((topic, topIdx) => {
              if (topIdx === topicIndex) {
                return {
                  ...topic,
                  contentBlocks: topic.contentBlocks.filter((_, idx) => idx !== blockIndex),
                };
              }
              return topic;
            }),
          };
        }
        return chapter;
      }),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);

    try {
      // Clean empty arrays
      const cleanedData = {
        ...courseData,
        prerequisites: courseData.prerequisites.filter((item) => item.trim() !== ''),
        objectives: courseData.objectives.filter((item) => item.trim() !== ''),
        tags: courseData.tags.filter((item) => item.trim() !== ''),
      };

      // Create slug
      const slug = courseData.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/g, '-');

      // Quiz_id alanlarının doğru atandığından emin ol
      const updatedChapters = cleanedData.chapters.map((chapter) => {
        const updatedTopics = chapter.topics.map((topic) => {
          const updatedContentBlocks = topic.contentBlocks.map((block) => {
            // Quiz içeriğini kontrol et ve düzelt
            if (block.type === 'quiz' && block.quiz) {
              // Eğer quiz_id yoksa ama _id veya id varsa, quiz_id'yi ekle
              if (!block.quiz.quiz_id && (block.quiz._id || block.quiz.id)) {
                block.quiz.quiz_id = block.quiz._id || block.quiz.id;
              }
            }
            return block;
          });
          return { ...topic, contentBlocks: updatedContentBlocks };
        });
        return { ...chapter, topics: updatedTopics };
      });

      // Add additional required fields
      const courseToSubmit = {
        ...cleanedData,
        chapters: updatedChapters, // Güncellenmiş chapters dizisini kullan
        slug,
        isPublished: false,
        __v: 0,
      };

      const result = await addCourse(courseToSubmit);

      if (result) {
        toast.success('Course created successfully');
        navigate(paths.cds.lms.courses.all);
      } else {
        toast.error('Failed to create course');
      }
    } catch (error) {
      console.error('Error creating course:', error);
      toast.error(error.response?.data?.message || 'An error occurred while creating the course');
    } finally {
      setLoading(false);
    }
  };

  // Quiz listesini yükle
  const loadQuizzes = async () => {
    setLoadingQuizzes(true);
    try {
      const quizData = await getQuizzes();
      setQuizzes(quizData);
    } catch (error) {
      toast.error('Failed to load quizzes');
    } finally {
      setLoadingQuizzes(false);
    }
  };

  // Optimize search handling
  const handleUsecaseSearch = useCallback(
    async (searchValue) => {
      try {
        setLoadingUsecases(true);
        const response = await getUsecases(1, searchValue, '', '', -1);

        if (response?.data?.useCases) {
          setUsecases(response.data.useCases);
          setUsecaseOptions(response.data.useCases);
          setHasMoreUsecases(false);
        }
      } catch (error) {
        console.error('Error searching usecases:', error);
        toast.error('Error searching usecases');
      } finally {
        setLoadingUsecases(false);
      }
    },
    [getUsecases]
  );

  // Debounce search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      if (usecaseSearch !== undefined) {
        handleUsecaseSearch(usecaseSearch);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [usecaseSearch, handleUsecaseSearch]);

  // Component mount olduğunda quizleri ve usecase'leri yükle
  useEffect(() => {
    loadQuizzes();
    handleUsecaseSearch('');
  }, []);

  // Memoize usecase options
  const memoizedUsecaseOptions = useMemo(() => usecases || [], [usecases]);

  // Function to update a specific input value
  const updateUsecaseInputValue = (chapterIndex, topicIndex, blockIndex, value) => {
    setUsecaseInputValues((prev) => ({
      ...prev,
      [`${chapterIndex}-${topicIndex}-${blockIndex}`]: value,
    }));
  };

  // Function to get a specific input value
  const getUsecaseInputValue = (chapterIndex, topicIndex, blockIndex) => {
    return usecaseInputValues[`${chapterIndex}-${topicIndex}-${blockIndex}`] || '';
  };

  // Handle usecase select scroll for infinite loading
  const handleUsecaseSelectScroll = () => {
    if (!hasMoreUsecases || loadingUsecases) return;

    const nextPage = usecasePage + 1;
    setUsecasePage(nextPage);

    const fetchMoreUsecases = async () => {
      try {
        setLoadingUsecases(true);
        const response = await getUsecases(nextPage, usecaseSearchDebounced, '', '', -1);

        if (response?.data?.useCases?.length) {
          setUsecases((prev) => [...prev, ...response.data.useCases]);
          setUsecaseOptions((prev) => [...prev, ...response.data.useCases]);
          setHasMoreUsecases(response.data.useCases.length > 0);
        } else {
          setHasMoreUsecases(false);
        }
      } catch (error) {
        console.error('Error loading more usecases:', error);
        toast.error('Error loading more usecases');
      } finally {
        setLoadingUsecases(false);
      }
    };

    fetchMoreUsecases();
  };

  return (
    <Container maxWidth="xl">
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
        <Typography variant="h4">Add New Course</Typography>
        <Button variant="outlined" onClick={() => navigate(paths.cds.lms.courses.all)}>
          Cancel
        </Button>
      </Stack>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <BasicInformation
              courseData={courseData}
              handleBasicInfoChange={handleBasicInfoChange}
              errors={errors}
            />
          </Grid>

          {/* Course Media */}
          <Grid item xs={12}>
            <CourseMedia courseData={courseData} handleBasicInfoChange={handleBasicInfoChange} />
          </Grid>

          {/* Course Settings */}
          <Grid item xs={12}>
            <CourseSettings courseData={courseData} setCourseData={setCourseData} errors={errors} />
          </Grid>

          {/* Instructors */}
          <Grid item xs={12}>
            <Instructors courseData={courseData} setCourseData={setCourseData} errors={errors} />
          </Grid>

          {/* Chapters and Topics */}
          <Grid item xs={12}>
            <ChaptersAndTopics
              courseData={courseData}
              handleChapterAdd={handleChapterAdd}
              handleChapterChange={handleChapterChange}
              handleChapterDelete={handleChapterDelete}
              handleTopicAdd={handleTopicAdd}
              handleTopicChange={handleTopicChange}
              handleTopicDelete={handleTopicDelete}
              handleContentBlockAdd={handleContentBlockAdd}
              handleContentBlockChange={handleContentBlockChange}
              handleContentBlockDelete={handleContentBlockDelete}
              quizzes={quizzes}
              loadingQuizzes={loadingQuizzes}
              errors={errors}
              editorRefs={editorRefs}
              uploadMedia={uploadMedia}
              renderEditor={renderEditor}
              usecases={usecases}
              loadingUsecases={loadingUsecases}
              usecaseOptions={memoizedUsecaseOptions}
              setUsecaseOptions={setUsecaseOptions}
              updateUsecaseInputValue={updateUsecaseInputValue}
              getUsecaseInputValue={getUsecaseInputValue}
              handleUsecaseSelectScroll={handleUsecaseSelectScroll}
              usecaseSearch={usecaseSearch}
              setUsecaseSearch={setUsecaseSearch}
            />
          </Grid>

          <Grid item xs={12}>
            <Stack direction="row" justifyContent="flex-end" spacing={2}>
              <Button variant="outlined" onClick={() => navigate(paths.cds.lms.courses.all)}>
                Cancel
              </Button>
              <LoadingButton loading={loading} type="submit" variant="contained" color="primary">
                Create Course
              </LoadingButton>
            </Stack>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
}

// Helper function to determine usecase type
const determineUsecaseType = (usecase) => {
  if (!usecase) return 'text';

  let type = usecase.type || usecase.use_case_type;

  if (Array.isArray(type)) {
    type = type[0] || '';
  }

  type = String(type).toLowerCase();

  if (type.includes('text')) return 'text';
  if (type.includes('image')) return 'image';
  if (type.includes('video')) return 'video';
  if (type.includes('assistant')) return 'assistant';

  return 'text';
};
