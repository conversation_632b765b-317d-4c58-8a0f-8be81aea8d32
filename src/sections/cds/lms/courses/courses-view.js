import React from 'react';
import { Container, Typography, <PERSON><PERSON>, Card, CardContent, Grid, Box, Stack } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { paths } from 'src/routes/paths';

export function CoursesView() {
  return (
    <Container maxWidth="xl">
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
        <Typography variant="h4">Courses</Typography>
        <Button
          component={RouterLink}
          to={paths.cds.lms.courses.add}
          variant="contained"
          startIcon={<AddIcon />}
        >
          Add New Course
        </Button>
      </Stack>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6">Course Management</Typography>
                <Button component={RouterLink} to={paths.cds.lms.courses.all} variant="outlined">
                  View All Courses
                </Button>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Manage your courses, create new ones, and organize your content.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
