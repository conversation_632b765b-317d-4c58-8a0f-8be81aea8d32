import PropTypes from 'prop-types';
import { Link as RouterLink } from 'react-router-dom';

import {
  Checkbox,
  IconButton,
  Link,
  MenuItem,
  Stack,
  TableCell,
  TableRow,
  Typography,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { paths } from 'src/routes/paths';
import Label from 'src/components/label';
import Iconify from 'src/components/iconify';
import { fDate } from 'src/utils/format-time';
import MenuPopover from 'src/components/menu-popover';
import { usePopover } from 'src/hooks/use-popover';

// ----------------------------------------------------------------------

export default function QuizTableRow({ row, selected, onSelectRow, onDeleteRow }) {
  const { t } = useTranslate();

  const { _id, title, category, questions, timeLimit, passingScore, createdAt } = row;

  const popover = usePopover();

  return (
    <>
      <TableRow hover selected={selected}>
        <TableCell padding="checkbox">
          <Checkbox checked={selected} onClick={onSelectRow} />
        </TableCell>

        <TableCell>
          <Link
            component={RouterLink}
            to={paths.cds.lms.quizzes.edit(_id)}
            color="inherit"
            variant="subtitle2"
            noWrap
          >
            {title}
          </Link>
        </TableCell>

        <TableCell>
          <Label variant="soft" color="primary">
            {category}
          </Label>
        </TableCell>

        <TableCell align="center">{questions.length}</TableCell>

        <TableCell align="center">{timeLimit} min</TableCell>

        <TableCell align="center">{passingScore}%</TableCell>

        <TableCell align="center">
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {fDate(createdAt)}
          </Typography>
        </TableCell>

        <TableCell align="right">
          <IconButton color={popover.open ? 'primary' : 'default'} onClick={popover.onOpen}>
            <Iconify icon="eva:more-vertical-fill" />
          </IconButton>
        </TableCell>
      </TableRow>

      <MenuPopover open={popover.open} onClose={popover.onClose} arrow="right-top">
        <MenuItem
          component={RouterLink}
          to={paths.cds.lms.quizzes.edit(_id)}
          sx={{ color: 'info.main' }}
        >
          <Iconify icon="eva:edit-fill" />
          {t('common.edit')}
        </MenuItem>

        <MenuItem
          component={RouterLink}
          to={paths.cds.lms.quizzes.view(_id)}
          sx={{ color: 'info.main' }}
        >
          <Iconify icon="eva:eye-fill" />
          {t('common.view')}
        </MenuItem>

        <MenuItem onClick={onDeleteRow} sx={{ color: 'error.main' }}>
          <Iconify icon="eva:trash-2-outline" />
          {t('common.delete')}
        </MenuItem>
      </MenuPopover>
    </>
  );
}

QuizTableRow.propTypes = {
  onDeleteRow: PropTypes.func,
  onSelectRow: PropTypes.func,
  row: PropTypes.object,
  selected: PropTypes.bool,
};
