import PropTypes from 'prop-types';

import { InputAdornment, MenuItem, Stack, TextField } from '@mui/material';

import Iconify from 'src/components/iconify';
import { useTranslate } from 'src/locales';

// ----------------------------------------------------------------------

export default function QuizTableToolbar({
  filterName,
  filterCategory,
  onFilterName,
  onFilterCategory,
}) {
  const { t } = useTranslate();

  const CATEGORY_OPTIONS = [
    { value: 'programming', label: t('lms.categories.programming') },
    { value: 'dataScience', label: t('lms.categories.dataScience') },
    { value: 'ai', label: t('lms.categories.ai') },
    { value: 'businessSkills', label: t('lms.categories.businessSkills') },
    { value: 'language', label: t('lms.categories.language') },
  ];

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{
        xs: 'column',
        md: 'row',
      }}
      sx={{
        p: 2.5,
        pr: { xs: 2.5, md: 1 },
      }}
    >
      <Stack direction="row" alignItems="center" spacing={2} flexGrow={1} sx={{ width: 1 }}>
        <TextField
          fullWidth
          value={filterName}
          onChange={onFilterName}
          placeholder={t('lms.quizzes.searchPlaceholder')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
        />

        <TextField
          fullWidth
          select
          value={filterCategory}
          onChange={onFilterCategory}
          SelectProps={{
            MenuProps: {
              PaperProps: {
                sx: { maxHeight: 220 },
              },
            },
          }}
          sx={{
            maxWidth: { md: 160 },
            textTransform: 'capitalize',
          }}
        >
          {CATEGORY_OPTIONS.map((option) => (
            <MenuItem
              key={option.value}
              value={option.value}
              sx={{
                mx: 1,
                my: 0.5,
                borderRadius: 0.75,
                typography: 'body2',
                textTransform: 'capitalize',
                '&:first-of-type': {
                  mt: 1,
                },
                '&:last-of-type': {
                  mb: 1,
                },
              }}
            >
              {option.label}
            </MenuItem>
          ))}
        </TextField>
      </Stack>
    </Stack>
  );
}

QuizTableToolbar.propTypes = {
  filterName: PropTypes.string,
  filterCategory: PropTypes.string,
  onFilterName: PropTypes.func,
  onFilterCategory: PropTypes.func,
};
