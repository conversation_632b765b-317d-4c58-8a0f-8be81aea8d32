import PropTypes from 'prop-types';
import { Link as RouterLink } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useState, useEffect } from 'react';

import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  Divider,
  IconButton,
  Link,
  Stack,
  Typography,
  Tooltip,
  alpha,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  TextField,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Paper,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { paths } from 'src/routes/paths';
import Iconify from 'src/components/iconify';
import { fDate } from 'src/utils/format-time';
import { usePopover } from 'src/hooks/use-popover';
import MenuPopover from 'src/components/menu-popover';
import { Translate as TranslateIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';

// ----------------------------------------------------------------------

export default function QuizCard({
  quiz,
  onDelete,
  onTranslate,
  onUpdateTranslation,
  languageOptions,
}) {
  const { t } = useTranslate();
  const popover = usePopover();
  const [translationOpen, setTranslationOpen] = useState(false);
  const [translationEditOpen, setTranslationEditOpen] = useState(false);
  const [targetLanguage, setTargetLanguage] = useState('en');
  const [translating, setTranslating] = useState(false);
  const [currentTranslation, setCurrentTranslation] = useState(null);
  const [editableTranslation, setEditableTranslation] = useState(null);
  const [editLanguage, setEditLanguage] = useState('');

  const {
    _id,
    title,
    description,
    category,
    questions,
    timeLimit,
    passingScore,
    createdAt,
    translations,
  } = quiz;

  // Dil kodundan dil adını almak için yardımcı fonksiyon
  const getLanguageName = (code) => {
    if (!languageOptions || !Array.isArray(languageOptions)) return code;
    const language = languageOptions.find((lang) => lang.code === code);
    return language ? language.label : code;
  };

  const handleTranslateOpen = () => {
    setTranslationOpen(true);
  };

  const handleTranslateClose = () => {
    setTranslationOpen(false);
  };

  const handleTranslate = async () => {
    if (!targetLanguage) {
      toast.error(t('Please select a target language'));
      return;
    }

    setTranslating(true);
    try {
      await onTranslate(quiz, targetLanguage);
      toast.success(t(`${getLanguageName(targetLanguage)} language translation completed`));
    } catch (error) {
      console.error('Translate error:', error);
      toast.error(t('Translate error'));
    } finally {
      setTranslating(false);
      handleTranslateClose();
    }
  };

  // Mevcut bir çeviriyi düzenlemek için fonksiyon
  const handleEditTranslation = async (langCode) => {
    //setTranslating(true);
    /* try {
      const result = await onTranslate(quiz, langCode);
      if (result.success && result.data) {
        // Düzenleme modalını açmadan önce veriyi hazırla
        setEditLanguage(langCode);
        setCurrentTranslation(result.data);
        setEditableTranslation({
          title: result.data.title || '',
          description: result.data.description || '',
          questions: result.data.questions ? [...result.data.questions] : [],
        });
        setTranslationEditOpen(true);
      }
    } catch (error) {
      console.error('Translation edit error:', error);
      toast.error(t('Translation data retrieval error'));
    } finally {
      setTranslating(false);
    }
      */
  };

  const handleTranslationEditClose = () => {
    setTranslationEditOpen(false);
    setCurrentTranslation(null);
    setEditableTranslation(null);
  };

  // Çeviri değişikliklerini kaydetmek için fonksiyon
  const handleSaveTranslationChanges = async () => {
    if (!editableTranslation || !editLanguage) {
      toast.error(t('Translation to be edited not found'));
      return;
    }

    setTranslating(true);
    try {
      const result = await onUpdateTranslation(_id, editLanguage, editableTranslation);

      if (result.success) {
        toast.success(t(`${getLanguageName(editLanguage)} translation updated successfully`));
        handleTranslationEditClose();
      } else {
        toast.error(t('Translation update error'));
      }
    } catch (error) {
      console.error('Translation update error:', error);
      toast.error(t('Translation update error'));
    } finally {
      setTranslating(false);
    }
  };

  // Form alanlarının değişikliklerini izleme
  const handleEditableFieldChange = (field, value) => {
    setEditableTranslation((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Soru düzenleme işlemleri
  const handleQuestionChange = (index, field, value) => {
    const updatedQuestions = [...editableTranslation.questions];
    updatedQuestions[index] = {
      ...updatedQuestions[index],
      [field]: value,
    };

    setEditableTranslation((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }));
  };

  // Seçenek düzenleme işlemleri
  const handleOptionChange = (questionIndex, optionIndex, value) => {
    const updatedQuestions = [...editableTranslation.questions];
    const updatedOptions = [...updatedQuestions[questionIndex].options];

    updatedOptions[optionIndex] = {
      ...updatedOptions[optionIndex],
      text: value,
    };

    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      options: updatedOptions,
    };

    setEditableTranslation((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }));
  };

  return (
    <>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
            <Link
              component={RouterLink}
              to={paths.cds.lms.quizzes.edit(_id)}
              color="inherit"
              variant="subtitle1"
              noWrap
            >
              {translations?.en?.title || title}
            </Link>

            <IconButton color={popover.open ? 'primary' : 'default'} onClick={popover.onOpen}>
              <Iconify icon="eva:more-vertical-fill" />
            </IconButton>
          </Stack>

          <Chip
            label={category}
            variant="soft"
            color="primary"
            size="small"
            sx={{ mt: 1, mb: 2 }}
          />

          <Typography
            variant="body2"
            sx={{ color: 'text.secondary', mb: 2, height: 60, overflow: 'hidden' }}
          >
            {description}
          </Typography>

          <Stack spacing={1.5}>
            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                ID:
              </Typography>
              <Typography
                variant="subtitle2"
                sx={{
                  textOverflow: 'ellipsis',
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' },
                }}
                onClick={() => {
                  navigator.clipboard.writeText(_id);
                  toast.success('ID copied!');
                }}
                title={`${_id} (Click to copy)`}
              >
                {_id}
              </Typography>
            </Stack>

            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {t('lms.quizzes.questions')}:
              </Typography>
              <Typography variant="subtitle2">{translations?.en?.questions ? translations.en.questions.length : 0}</Typography>
            </Stack>

            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {t('lms.quizzes.timeLimit')}:
              </Typography>
              <Typography variant="subtitle2">{timeLimit} min</Typography>
            </Stack>

            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {t('lms.quizzes.passingScore')}:
              </Typography>
              <Typography variant="subtitle2">{passingScore}%</Typography>
            </Stack>
          </Stack>

          {/* Mevcut çevirileri göster */}
          {translations && Object.keys(translations).length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                {t('Existing Translations')}:
              </Typography>
              <Stack direction="row" spacing={0.5} flexWrap="wrap" gap={0.5}>
                {Object.keys(translations).map((langCode) => (
                  <Tooltip key={langCode} title={`${getLanguageName(langCode)} translation edit`}>
                    <Link
                      component={RouterLink}
                      to={paths.cds.lms.quizzes.edit(_id) + '?lang=' + langCode}
                      color="inherit"
                      variant="subtitle1"
                      noWrap
                    >
                      <Chip
                        label={langCode.toUpperCase()}
                        size="small"
                        color="primary"
                        variant="outlined"
                        sx={{
                          '&:hover': {
                            cursor: 'pointer',
                            backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                          },
                        }}
                      />
                    </Link>
                  </Tooltip>
                ))}
              </Stack>
            </Box>
          )}
        </CardContent>

        <Divider />

        <CardActions>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              {t('lms.quizzes.createdAt')}: {fDate(createdAt)}
            </Typography>
          </Box>

          <Button
            component={RouterLink}
            to={paths.cds.lms.quizzes.edit(_id)}
            size="small"
            color="primary"
          >
            {t('Edit')}
          </Button>

          <Button
            size="small"
            color="warning"
            variant="outlined"
            onClick={handleTranslateOpen}
            startIcon={<TranslateIcon />}
          >
            {t('Translate')}
          </Button>
        </CardActions>
      </Card>

      <MenuPopover
        open={popover.open}
        onClose={popover.onClose}
        arrow="right-top"
        anchorEl={popover.anchorEl}
      >
        <CustomMenuItem component={RouterLink} to={paths.cds.lms.quizzes.edit(_id)}>
          {t('Edit')}
        </CustomMenuItem>

        <CustomMenuItem component={RouterLink} to={paths.cds.lms.quizzes.view(_id)}>
          {t('View')}
        </CustomMenuItem>

        <CustomMenuItem onClick={handleTranslateOpen}>
          <Iconify icon="mdi:translate" sx={{ mr: 1 }} />
          {t('Translate')}
        </CustomMenuItem>

        <CustomMenuItem onClick={onDelete}>{t('Delete')}</CustomMenuItem>
      </MenuPopover>

      {/* Çeviri Modalı */}
      <Dialog
        open={translationOpen}
        onClose={handleTranslateClose}
        aria-labelledby="translation-dialog-title"
      >
        <DialogTitle id="translation-dialog-title">{t('Quiz Translate')}</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 3 }}>
            {t(
              'This action will translate all text fields, labels, and options to the selected language.'
            )}
          </Typography>
          <FormControl fullWidth>
            <InputLabel id="target-language-label">{t('Target Language')}</InputLabel>
            <Select
              labelId="target-language-label"
              id="target-language"
              value={targetLanguage}
              label={t('Target Language')}
              onChange={(e) => setTargetLanguage(e.target.value)}
            >
              {languageOptions &&
                languageOptions.map((option) => (
                  <MenuItem key={option.code} value={option.code}>
                    {option.label}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
          {translations && translations[targetLanguage] && (
            <Typography variant="body2" color="warning.main" sx={{ mt: 2 }}>
              {t(
                'This quiz already has a translation in the selected language. The existing translation will be updated.'
              )}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleTranslateClose} color="inherit">
            {t('Cancel')}
          </Button>
          <Button
            onClick={handleTranslate}
            variant="contained"
            color="primary"
            disabled={translating}
          >
            {translating ? t('Translating...') : t('Translate')}
            {translating && <CircularProgress size={24} sx={{ ml: 1 }} />}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Çeviri Düzenleme Modalı */}
      <Dialog
        open={translationEditOpen}
        onClose={handleTranslationEditClose}
        aria-labelledby="translation-edit-dialog-title"
        maxWidth="md"
        fullWidth
      >
        <DialogTitle id="translation-edit-dialog-title">
          {t('Edit Translation')} ({getLanguageName(editLanguage)})
        </DialogTitle>
        <DialogContent>
          {editableTranslation ? (
            <Box sx={{ my: 2 }}>
              <TextField
                label={t('Başlık')}
                fullWidth
                value={editableTranslation.title}
                onChange={(e) => handleEditableFieldChange('title', e.target.value)}
                sx={{ mb: 2 }}
              />

              <TextField
                label={t('Description')}
                fullWidth
                multiline
                rows={3}
                value={editableTranslation.description}
                onChange={(e) => handleEditableFieldChange('description', e.target.value)}
                sx={{ mb: 3 }}
              />

              <Typography variant="h6" sx={{ mb: 2 }}>
                {t('Questions')}
              </Typography>

              {editableTranslation.questions &&
                editableTranslation.questions.map((question, qIndex) => (
                  <Accordion key={qIndex} sx={{ mb: 1 }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>
                        {t('Question')} {qIndex + 1}: {question.question.substring(0, 50)}
                        {question.question.length > 50 ? '...' : ''}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <TextField
                        label={t('Question Text')}
                        fullWidth
                        multiline
                        rows={2}
                        value={question.question}
                        onChange={(e) => handleQuestionChange(qIndex, 'question', e.target.value)}
                        sx={{ mb: 2 }}
                      />

                      <Typography variant="subtitle2" sx={{ mb: 1 }}>
                        {t('Options')}
                      </Typography>

                      {question.options &&
                        question.options.map((option, optIndex) => (
                          <Box key={optIndex} sx={{ mb: 1 }}>
                            <Grid container spacing={2}>
                              <Grid item xs={12}>
                                <TextField
                                  label={`${t('Option')} ${optIndex + 1}`}
                                  fullWidth
                                  value={option.text}
                                  onChange={(e) =>
                                    handleOptionChange(qIndex, optIndex, e.target.value)
                                  }
                                  size="small"
                                />
                              </Grid>
                              {option.explanation && (
                                <Grid item xs={12}>
                                  <TextField
                                    label={t('Explanation')}
                                    fullWidth
                                    value={option.explanation}
                                    onChange={(e) => {
                                      const updatedQuestions = [...editableTranslation.questions];
                                      updatedQuestions[qIndex].options[optIndex].explanation =
                                        e.target.value;
                                      handleEditableFieldChange('questions', updatedQuestions);
                                    }}
                                    size="small"
                                  />
                                </Grid>
                              )}
                            </Grid>
                          </Box>
                        ))}
                    </AccordionDetails>
                  </Accordion>
                ))}
            </Box>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
              <CircularProgress />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleTranslationEditClose} color="inherit">
            {t('Cancel')}
          </Button>
          <Button
            onClick={handleSaveTranslationChanges}
            variant="contained"
            color="primary"
            disabled={translating || !editableTranslation}
          >
            {translating ? t('Saving...') : t('Save Changes')}
            {translating && <CircularProgress size={24} sx={{ ml: 1 }} />}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

QuizCard.propTypes = {
  quiz: PropTypes.object.isRequired,
  onDelete: PropTypes.func,
  onTranslate: PropTypes.func,
  onUpdateTranslation: PropTypes.func,
  languageOptions: PropTypes.array,
};

// ----------------------------------------------------------------------

function CustomMenuItem({ children, ...other }) {
  return (
    <Box
      component="div"
      role="menuitem"
      sx={{
        py: 1,
        px: 2.5,
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        '&:hover': {
          bgcolor: 'action.hover',
        },
      }}
      {...other}
    >
      {children}
    </Box>
  );
}
