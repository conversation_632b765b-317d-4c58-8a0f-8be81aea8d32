import PropTypes from 'prop-types';
import * as Yup from 'yup';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Card,
  Divider,
  Grid,
  IconButton,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { useResponsive } from 'src/hooks/use-responsive';
import FormProvider, { RHFSelect, RHFSwitch, RHFTextField } from 'src/components/hook-form';
import Iconify from 'src/components/iconify';

// ----------------------------------------------------------------------

export default function QuizNewEditForm({ isEdit, currentQuiz, loadingSave, onSave }) {
  const { t } = useTranslate();

  const NewQuizSchema = Yup.object().shape({
    title: Yup.string().required(t('validation.required')),
    description: Yup.string().notRequired(),
    category: Yup.string().required(t('validation.required')),
    timeLimit: Yup.number()
      .required(t('validation.required'))
      .min(0, t('validation.min', { min: 0 })),
    passingScore: Yup.number()
      .transform((value) => (isNaN(value) ? 0 : value))
      .required(t('validation.required'))
      .min(0, t('validation.min', { min: 0 }))
      .max(100, t('validation.max', { max: 100 })),
    maxAttempts: Yup.number()
      .required(t('validation.required'))
      .min(0, t('validation.min', { min: 0 })),
    shuffleQuestions: Yup.boolean(),
    showAnswers: Yup.boolean(),
    questions: Yup.array()
      .of(
        Yup.object().shape({
          question: Yup.string().required(t('validation.required')),
          type: Yup.string().required(t('validation.required')),
          points: Yup.number()
            .required(t('validation.required'))
            .min(0, t('validation.min', { min: 0 })),
          options: Yup.array()
            .of(
              Yup.object().shape({
                text: Yup.string().required(t('validation.required')),
                isCorrect: Yup.boolean().required(t('validation.required')),
                explanation: Yup.string(),
              })
            )
            .min(1, t('validation.min', { min: 1 })),
        })
      )
      .min(1, t('validation.min', { min: 1 })),
  });

  const defaultValues = useMemo(() => {
    // API'den gelen veriyi form formatına dönüştür
    let formattedQuiz = { ...currentQuiz };
    // Eğer quiz.questions varsa, bu eski API formatıdır
    if (currentQuiz?.quiz?.questions) {
      formattedQuiz.questions = currentQuiz.quiz.questions.map((question) => {
        // Temel soru yapısı
        const formattedQuestion = {
          question: question.question,
          type: question.type,
          points: question.points || 10,
        };

        // Soru tipine göre options yapısını oluştur
        if (question.type === 'matching' && question.pairs) {
          formattedQuestion.options = question.pairs.map((pair) => ({
            text: pair.left || '',
            match: pair.right || '',
            isCorrect: true,
            explanation: '',
          }));
        } else if (question.type === 'multiple-selection') {
          // Multiple-selection için özel işlem

          if (Array.isArray(question.options)) {
            formattedQuestion.options = question.options.map((opt, index) => {
              // isCorrect değerini doğru şekilde al
              const isCorrectValue =
                opt.isCorrect !== undefined
                  ? opt.isCorrect
                  : opt.correct !== undefined
                    ? opt.correct
                    : false;

              // Eğer sadece explanation alanı varsa ve text yoksa
              if (
                ((!opt.text || opt.text.trim() === '') && opt.explanation !== undefined) ||
                (Object.keys(opt).length === 1 && opt.explanation !== undefined)
              ) {
                return {
                  text: opt.explanation || `Option ${index + 1}`,
                  isCorrect: isCorrectValue, // Doğru değeri koru
                  explanation: opt.explanation || '',
                };
              }
              // Normal durumda
              return {
                text: opt.text || opt.option || `Option ${index + 1}`,
                isCorrect: isCorrectValue, // Doğru değeri koru
                explanation: opt.explanation || '',
              };
            });
          } else {
            // Seçenek dizisi yoksa varsayılan değerler oluştur
            formattedQuestion.options = [
              { text: 'Option 1', isCorrect: false, explanation: '' },
              { text: 'Option 2', isCorrect: false, explanation: '' },
              { text: 'Option 3', isCorrect: false, explanation: '' },
            ];
          }
        } else if (question.options) {
          formattedQuestion.options = question.options.map((opt, index) => ({
            text: opt.option || opt.text || `Option ${index + 1}`,
            isCorrect: opt.correct || opt.isCorrect || false,
            explanation: opt.explanation || '',
          }));
        }

        return formattedQuestion;
      });
    } else if (currentQuiz?.questions) {
      formattedQuiz.questions = currentQuiz.questions.map((question) => {
        // Temel soru yapısı
        const formattedQuestion = {
          question: question.question,
          type: question.type,
          points: question.points || 10,
        };

        // Soru tipine göre options yapısını oluştur
        if (question.type === 'matching') {
          // Farklı veri yapılarını kontrol et
          if (question.pairs && Array.isArray(question.pairs)) {
            formattedQuestion.options = question.pairs.map((pair) => ({
              text: pair.left || '',
              match: pair.right || '',
              isCorrect: true,
              explanation: '',
            }));
          } else if (question.options && Array.isArray(question.options)) {
            // options içinde match alanı var mı kontrol et
            const hasMatchField = question.options.some((opt) => opt.match !== undefined);

            if (hasMatchField) {
              formattedQuestion.options = question.options.map((opt) => ({
                text: opt.text || '',
                match: opt.match || '',
                isCorrect: true,
                explanation: opt.explanation || '',
              }));
            } else {
              // Eğer match alanı yoksa, originalMatches veya başka bir kaynaktan almaya çalış
              if (question.originalMatches && Array.isArray(question.originalMatches)) {
                formattedQuestion.options = question.originalMatches.map((match, index) => {
                  const option = question.options[index] || {};
                  return {
                    text: match.left || option.text || '',
                    match: match.right || '',
                    isCorrect: true,
                    explanation: option.explanation || '',
                  };
                });
              } else {
                // Hiçbir eşleştirme verisi bulunamadıysa, varsayılan değerler oluştur

                formattedQuestion.options = question.options.map((opt, index) => ({
                  text: opt.text || `Sol Öğe ${index + 1}`,
                  match: opt.match || '',
                  isCorrect: true,
                  explanation: opt.explanation || '',
                }));
              }
            }
          } else if (question.originalMatches && Array.isArray(question.originalMatches)) {
            formattedQuestion.options = question.originalMatches.map((match) => ({
              text: match.left || '',
              match: match.right || '',
              isCorrect: true,
              explanation: '',
            }));
          } else {
            formattedQuestion.options = [
              { text: 'Sol Öğe 1', match: 'Sağ Öğe 1', isCorrect: true, explanation: '' },
              { text: 'Sol Öğe 2', match: 'Sağ Öğe 2', isCorrect: true, explanation: '' },
            ];
          }
        } else if (question.type === 'multiple-selection') {
          if (Array.isArray(question.options)) {
            formattedQuestion.options = question.options.map((opt, index) => {
              // isCorrect değerini doğru şekilde al
              const isCorrectValue =
                opt.isCorrect !== undefined
                  ? opt.isCorrect
                  : opt.correct !== undefined
                    ? opt.correct
                    : false;

              // Eğer sadece explanation alanı varsa ve text yoksa
              if (
                ((!opt.text || opt.text.trim() === '') && opt.explanation !== undefined) ||
                (Object.keys(opt).length === 1 && opt.explanation !== undefined)
              ) {
                return {
                  text: opt.explanation || `Option ${index + 1}`,
                  isCorrect: isCorrectValue, // Doğru değeri koru
                  explanation: opt.explanation || '',
                };
              }
              // Normal durumda
              return {
                text: opt.text || opt.option || `Option ${index + 1}`,
                isCorrect: isCorrectValue, // Doğru değeri koru
                explanation: opt.explanation || '',
              };
            });
          } else {
            // Seçenek dizisi yoksa varsayılan değerler oluştur
            formattedQuestion.options = [
              { text: 'Option 1', isCorrect: false, explanation: '' },
              { text: 'Option 2', isCorrect: false, explanation: '' },
              { text: 'Option 3', isCorrect: false, explanation: '' },
            ];
          }
        } else if (
          (question.type === 'multiple-choice' || question.type === 'single-choice') &&
          question.options
        ) {
          // Multiple-choice ve single-choice için options dizisini dönüştür
          formattedQuestion.options = question.options.map((opt, index) => {
            const option = {
              text: opt.option || opt.text || `Option ${index + 1}`,
              isCorrect: opt.correct || opt.isCorrect || false,
              explanation: opt.explanation || '',
            };
            return option;
          });
        } else if (question.type === 'true-false') {
          // True-false için options dizisini oluştur
          // console.log('true-false question:', question);
          // Farklı veri yapılarını kontrol et
          if (question.correctAnswer !== undefined) {
            // correctAnswer alanı varsa, bunu kullan
            formattedQuestion.options = [
              { text: 'True', isCorrect: question.correctAnswer === true, explanation: '' },
              { text: 'False', isCorrect: question.correctAnswer === false, explanation: '' },
            ];
          } else if (Array.isArray(question.options) && question.options.length === 2) {
            // options dizisi varsa ve 2 elemanlıysa, bunları kullan
            console.log(question.options);
            const trueOption = question.options.find(
              (opt) => opt.text === 'True' || opt.text === 'STIMMT'
            );
            const falseOption = question.options.find(
              (opt) => opt.text === 'False' || opt.text === 'FALSCH'
            );

            formattedQuestion.options = [
              {
                text: trueOption ? trueOption.text : 'True',
                isCorrect: trueOption ? trueOption.isCorrect || trueOption.correct || false : false,
                explanation: trueOption ? trueOption.explanation || '' : '',
              },
              {
                text: falseOption ? falseOption.text : 'False',
                isCorrect: falseOption
                  ? falseOption.isCorrect || falseOption.correct || false
                  : false,
                explanation: falseOption ? falseOption.explanation || '' : '',
              },
            ];
          } else {
            // Hiçbir veri bulunamazsa varsayılan değerler
            formattedQuestion.options = [
              { text: 'True', isCorrect: formattedQuestion.isCorrect, explanation: '' },
              { text: 'False', isCorrect: formattedQuestion.isCorrect, explanation: '' },
            ];
          }
        } else if (question.type === 'fill-in-the-blank' && question.correctAnswer) {
          // Fill-in-the-blank için options dizisini oluştur
          formattedQuestion.options = [
            { text: question.correctAnswer, isCorrect: true, explanation: '' },
          ];
        } else if (question.options) {
          // Eski format için geriye dönük uyumluluk
          formattedQuestion.options = question.options.map((opt, index) => {
            // Eğer sadece explanation alanı varsa
            if (
              ((!opt.text || opt.text.trim() === '') && opt.explanation !== undefined) ||
              (Object.keys(opt).length === 1 && opt.explanation !== undefined)
            ) {
              return {
                text: opt.explanation || `Option ${index + 1}`,
                isCorrect: opt.isCorrect || opt.correct || false,
                explanation: opt.explanation || '',
              };
            }
            return {
              text: opt.text || opt.option || `Option ${index + 1}`,
              isCorrect: opt.isCorrect || opt.correct || false,
              explanation: opt.explanation || '',
            };
          });
        } else {
          // Varsayılan options dizisi
          formattedQuestion.options = [
            { text: 'Option 1', isCorrect: false, explanation: '' },
            { text: 'Option 2', isCorrect: false, explanation: '' },
          ];
        }

        return formattedQuestion;
      });
    }

    return {
      title: formattedQuiz?.title || '',
      description: formattedQuiz?.description || '',
      category: formattedQuiz?.category || '',
      timeLimit: formattedQuiz?.timeLimit || 30,
      passingScore: formattedQuiz?.passingScore ?? 0,
      maxAttempts: formattedQuiz?.maxAttempts || 0,
      shuffleQuestions: formattedQuiz?.shuffleQuestions || false,
      showAnswers: formattedQuiz?.showAnswers || true,
      questions: formattedQuiz?.questions || [
        {
          question: '',
          type: 'multiple-choice',
          points: 10,
          options: [
            { text: '', isCorrect: false, explanation: '' },
            { text: '', isCorrect: false, explanation: '' },
          ],
        },
      ],
    };
  }, [currentQuiz]);

  const methods = useForm({
    resolver: yupResolver(NewQuizSchema),
    defaultValues,
  });

  const {
    reset,
    control,
    watch,
    setValue,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const {
    fields: questionFields,
    append: appendQuestion,
    remove: removeQuestion,
  } = useFieldArray({
    control,
    name: 'questions',
  });

  useEffect(() => {
    if (isEdit && currentQuiz) {
      // URL'den dil parametresini kontrol et
      const urlParams = new URLSearchParams(window.location.search);
      const lang = urlParams.get('lang');

      // Eğer belirli bir dil çevirisi düzenleniyorsa
      if (lang && currentQuiz.translations && currentQuiz.translations[lang]) {
        // Belirli dildeki çeviriyi formda göster
        const translatedQuiz = currentQuiz.translations[lang];
        reset(formatQuizForForm(translatedQuiz));
      } else {
        // Normal düzenleme (dil parametresi yoksa)
        reset(defaultValues);
      }
    }
  }, [isEdit, currentQuiz, reset, defaultValues]);

  const handleAddQuestion = () => {
    appendQuestion({
      question: '',
      type: 'multiple-choice',
      points: 10,
      options: [
        { text: '', isCorrect: false, explanation: '' },
        { text: '', isCorrect: false, explanation: '' },
      ],
    });
  };

  const handleQuestionTypeChange = (questionIndex, newType) => {
    const questions = watch('questions');
    const currentQuestion = questions[questionIndex];

    let defaultOptions = [];

    switch (newType) {
      case 'multiple-choice':
      case 'single-choice':
        defaultOptions =
          currentQuestion.options.length >= 2
            ? currentQuestion.options
            : [
                { text: '', isCorrect: false, explanation: '' },
                { text: '', isCorrect: false, explanation: '' },
              ];
        break;
      case 'multiple-selection':
        defaultOptions =
          currentQuestion.options.length >= 2
            ? currentQuestion.options
            : [
                { text: '', isCorrect: false, explanation: '' },
                { text: '', isCorrect: false, explanation: '' },
                { text: '', isCorrect: false, explanation: '' },
              ];
        break;
      case 'true-false':
        defaultOptions = [
          { text: 'True', isCorrect: false, explanation: '' },
          { text: 'False', isCorrect: false, explanation: '' },
        ];
        console.log(currentQuestion);
        break;
      case 'text':
      case 'fill-in-the-blank':
        defaultOptions = [{ text: '', isCorrect: true, explanation: '' }];
        break;
      case 'matching':
        defaultOptions = [
          { text: 'Sol Öğe 1', match: 'Sağ Öğe 1', isCorrect: true, explanation: '' },
          { text: 'Sol Öğe 2', match: 'Sağ Öğe 2', isCorrect: true, explanation: '' },
        ];

        // Eğer mevcut seçenekler varsa, onları eşleştirme formatına dönüştür
        if (currentQuestion.options && currentQuestion.options.length > 0) {
          defaultOptions = currentQuestion.options.map((opt, index) => ({
            text: opt.text || `Sol Öğe ${index + 1}`,
            match: opt.match || `Sağ Öğe ${index + 1}`,
            isCorrect: true,
            explanation: opt.explanation || '',
          }));
        }
        break;
      default:
        defaultOptions = currentQuestion.options;
    }

    // Soru tipini ve seçenekleri güncelle
    setValue(`questions.${questionIndex}.type`, newType);
    setValue(`questions.${questionIndex}.options`, defaultOptions);
  };

  const handleAddOption = (questionIndex) => {
    const questions = watch('questions');
    const questionType = questions[questionIndex].type;
    let newOption = { text: '', isCorrect: false, explanation: '' };

    // Soru tipine göre yeni seçenek yapısını ayarla
    if (questionType === 'matching') {
      const optionCount = questions[questionIndex].options.length + 1;
      newOption = {
        text: `Sol Öğe ${optionCount}`,
        match: `Sağ Öğe ${optionCount}`,
        isCorrect: true,
        explanation: '',
      };
    } else if (questionType === 'true-false') {
      newOption = { text: '', isCorrect: false, explanation: '' };
    }

    const options = [...questions[questionIndex].options, newOption];
    setValue(`questions.${questionIndex}.options`, options);
  };

  const handleRemoveOption = (questionIndex, optionIndex) => {
    const questions = watch('questions');
    const options = [...questions[questionIndex].options];
    options.splice(optionIndex, 1);
    setValue(`questions.${questionIndex}.options`, options);
  };

  const onSubmit = useCallback(
    async (data) => {
      try {
        const formattedData = {
          ...data,
          questions: data.questions.map((question) => {
            // Multiple-selection tipi için seçenekleri kontrol et ve düzelt
            if (
              question.type === 'multiple-selection' ||
              question.type === 'multiple-choice' ||
              question.type === 'single-choice'
            ) {
              if (Array.isArray(question.options)) {
                // Her bir seçenek için text alanını kontrol et
                const fixedOptions = question.options.map((opt, index) => {
                  const result = { ...opt };

                  // isCorrect değerini koru
                  const isCorrectValue = opt.isCorrect !== undefined ? opt.isCorrect : false;

                  // Eğer text alanı boşsa ve explanation varsa
                  if ((!opt.text || opt.text.trim() === '') && opt.explanation !== undefined) {
                    result.text = opt.explanation || `Option ${index + 1}`; // Açıklama yoksa varsayılan değer
                  }
                  // Eğer text alanı boşsa ve option alanı varsa
                  else if ((!opt.text || opt.text.trim() === '') && opt.option) {
                    result.text = opt.option;
                  }
                  // Eğer text alanı boşsa, varsayılan değer belirle
                  else if (!opt.text || opt.text.trim() === '') {
                    result.text = `Option ${index + 1}`;
                  }

                  // isCorrect değerini daima koru - kullanıcının seçimi önceliktir
                  result.isCorrect = isCorrectValue;
                  return result;
                });

                // Düzeltilmiş seçenekleri ata
                question.options = fixedOptions;
              }
            }

            if (question.type === 'matching') {
              let options = [...question.options];
              if (options.length < 2) {
                options = [
                  { text: 'Sol Öğe 1', match: 'Sağ Öğe 1', isCorrect: true, explanation: '' },
                  { text: 'Sol Öğe 2', match: 'Sağ Öğe 2', isCorrect: true, explanation: '' },
                ];
              }

              // Eşleştirme öğelerinin orijinal halini kaydet
              const originalMatches = options.map((option) => ({
                left: option.text || '',
                right: option.match || '',
              }));
              // Sağ öğeleri karıştır
              const rightItems = options.map((option) => option.match || '');
              const shuffledRightItems = [...rightItems].sort(() => Math.random() - 0.5);

              const shuffledOptions = options.map((option, index) => ({
                ...option,
                isCorrect: true,
                originalMatch: option.match || '',
                match: shuffledRightItems[index] || '',
              }));

              // API'ye gönderilecek veri yapısını oluştur
              const formattedQuestion = {
                ...question,
                options: shuffledOptions,
                originalMatches: originalMatches,
                pairs: originalMatches, // API uyumluluğu için pairs ekle
                isMatchesShuffled: true,
              };

              return formattedQuestion;
            }

            // True-false tipindeki sorular için özel işlem
            if (question.type === 'true-false') {
              // options dizisini kontrol et
              if (Array.isArray(question.options) && question.options.length === 2) {
                // True seçeneğinin isCorrect değerini al
                const trueOption = question.options.find((opt) => opt.text === 'True');
                const falseOption = question.options.find((opt) => opt.text === 'False');

                // Eğer True seçeneği işaretlenmişse correctAnswer = true, değilse false
                question.correctAnswer = trueOption && trueOption.isCorrect === true;

                // options dizisini düzenle
                question.options = [
                  {
                    text: 'True',
                    isCorrect: trueOption ? trueOption.isCorrect : false,
                    explanation: trueOption ? trueOption.explanation || '' : '',
                  },
                  {
                    text: 'False',
                    isCorrect: falseOption ? falseOption.isCorrect : false,
                    explanation: falseOption ? falseOption.explanation || '' : '',
                  },
                ];
              }
            }

            if (question.type === 'fill-in-the-blank') {
              question.correctAnswer = question.options[0].text;
            }

            return question;
          }),
        };
        await onSave(formattedData);
      } catch (error) {
        console.error('Form gönderme hatası:', error);
      }
    },
    [onSave]
  );

  const CATEGORY_OPTIONS = [
    { value: 'programming', label: t('lms.categories.programming') },
    { value: 'dataScience', label: t('lms.categories.dataScience') },
    { value: 'ai', label: t('lms.categories.ai') },
    { value: 'businessSkills', label: t('lms.categories.businessSkills') },
    { value: 'language', label: t('lms.categories.language') },
  ];

  const QUESTION_TYPE_OPTIONS = [
    { value: 'multiple-choice', label: t('lms.questionTypes.multipleChoice') },
    { value: 'single-choice', label: t('lms.questionTypes.singleChoice') },
    { value: 'multiple-selection', label: t('lms.questionTypes.multipleSelection') },
    { value: 'true-false', label: t('lms.questionTypes.trueFalse') },
    { value: 'text', label: t('lms.questionTypes.text') },
    { value: 'fill-in-the-blank', label: t('lms.questionTypes.fillInTheBlank') },
    { value: 'matching', label: t('lms.questionTypes.matching') },
  ];

  const renderQuizDetails = (
    <Card sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3 }}>
        {t('lms.quizzes.details')}
      </Typography>

      <Box
        rowGap={3}
        columnGap={2}
        display="grid"
        gridTemplateColumns={{
          xs: 'repeat(1, 1fr)',
          sm: 'repeat(2, 1fr)',
        }}
      >
        <RHFTextField name="title" label={t('lms.quizzes.title')} />
        {
          // ekrana currentQuiz.category yazdır
          //  <pre>{JSON.stringify(currentQuiz, null, 2)}</pre>
        }
        <RHFSelect name="category" label={t('lms.quizzes.category')}>
          {CATEGORY_OPTIONS.map((option) => (
            <MenuItem
              key={option.value}
              value={option.value}
              selected={option.value === currentQuiz?.category}
            >
              {option.label}
            </MenuItem>
          ))}
        </RHFSelect>
        <RHFTextField
          name="description"
          label={t('lms.quizzes.description')}
          multiline
          rows={3}
          sx={{ gridColumn: { xs: 'span 1', sm: 'span 2' } }}
        />
      </Box>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h6" sx={{ mb: 3 }}>
        {t('lms.quizzes.settings')}
      </Typography>

      <Box
        rowGap={3}
        columnGap={2}
        display="grid"
        gridTemplateColumns={{
          xs: 'repeat(1, 1fr)',
          sm: 'repeat(3, 1fr)',
        }}
      >
        <RHFTextField
          name="timeLimit"
          label={t('lms.quizzes.timeLimit')}
          type="number"
          InputProps={{ inputProps: { min: 0 } }}
        />
        <RHFTextField
          name="passingScore"
          label={t('lms.quizzes.passingScore')}
          type="number"
          value={watch('passingScore')}
          onChange={(e) => {
            const value = e.target.value === '' ? 0 : Number(e.target.value);
            setValue('passingScore', value);
          }}
          InputProps={{
            inputProps: {
              min: 0,
              max: 100,
              step: 1,
            },
          }}
        />
        <RHFTextField
          name="maxAttempts"
          label={t('lms.quizzes.maxAttempts')}
          type="number"
          InputProps={{ inputProps: { min: 0 } }}
          helperText={t('lms.quizzes.maxAttemptsHelp') || '0 means unlimited attempts'}
        />
      </Box>

      <Stack direction="row" spacing={3} sx={{ mt: 3 }}>
        <RHFSwitch name="shuffleQuestions" label={t('lms.quizzes.shuffleQuestions')} />
        <RHFSwitch name="showAnswers" label={t('lms.quizzes.showAnswers')} />
      </Stack>
    </Card>
  );

  const renderQuestions = (
    <Card sx={{ p: 3 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h6">{t('lms.quizzes.questions')}</Typography>
        <Button
          size="small"
          startIcon={<Iconify icon="eva:plus-fill" />}
          onClick={handleAddQuestion}
        >
          {t('lms.quizzes.addQuestion')}
        </Button>
      </Stack>

      {questionFields.map((item, questionIndex) => {
        const questionType = watch(`questions.${questionIndex}.type`);
        const question = watch(`questions.${questionIndex}`);
        const renderOptionFields = (questionIndex, optionIndex, questionType) => {
          switch (questionType) {
            case 'multiple-choice':
            case 'single-choice':
            case 'multiple-selection':
              return (
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={8}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.text`}
                      label={t('lms.quizzes.optionText')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <RHFSwitch
                      name={`questions.${questionIndex}.options.${optionIndex}.isCorrect`}
                      label={t('lms.quizzes.isCorrect')}
                      sx={{ mt: 2 }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.explanation`}
                      label={t('lms.quizzes.explanation')}
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              );

            case 'true-false':
              return (
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={8}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.text`}
                      label={t('lms.quizzes.optionText')}
                      disabled
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <RHFSwitch
                      name={`questions.${questionIndex}.options.${optionIndex}.isCorrect`}
                      label={t('lms.quizzes.isCorrect')}
                      sx={{ mt: 2 }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.explanation`}
                      label={t('lms.quizzes.explanation')}
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              );

            case 'text':
            case 'fill-in-the-blank':
              return (
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.text`}
                      label={t('lms.quizzes.correctAnswer')}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.explanation`}
                      label={t('lms.quizzes.explanation')}
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              );

            case 'matching':
              return (
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={6}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.text`}
                      label={t('lms.quizzes.leftItem')}
                      InputProps={{
                        sx: {
                          bgcolor: 'action.hover',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.match`}
                      label={t('lms.quizzes.rightItem')}
                      InputProps={{
                        sx: {
                          bgcolor: 'action.hover',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <RHFTextField
                      name={`questions.${questionIndex}.options.${optionIndex}.explanation`}
                      label={t('lms.quizzes.explanation')}
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              );

            default:
              return null;
          }
        };

        return (
          <Card key={item.id} sx={{ p: 3, mb: 3, bgcolor: 'background.neutral' }}>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{ mb: 2 }}
            >
              <Typography variant="subtitle1">
                {t('lms.quizzes.questionNumber', { number: questionIndex + 1 })}
              </Typography>
              {questionFields.length > 1 && (
                <IconButton onClick={() => removeQuestion(questionIndex)}>
                  <Iconify icon="eva:trash-2-outline" />
                </IconButton>
              )}
            </Stack>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <RHFTextField
                  name={`questions.${questionIndex}.question`}
                  label={t('lms.quizzes.questionText')}
                  multiline
                  rows={2}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <RHFSelect
                  name={`questions.${questionIndex}.type`}
                  label={t('lms.quizzes.questionType')}
                  onChange={(e) => handleQuestionTypeChange(questionIndex, e.target.value)}
                >
                  {QUESTION_TYPE_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </RHFSelect>
              </Grid>

              <Grid item xs={12} sm={6}>
                <RHFTextField
                  name={`questions.${questionIndex}.points`}
                  label={t('lms.quizzes.points')}
                  type="number"
                  InputProps={{ inputProps: { min: 0 } }}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{ mb: 2 }}
            >
              <Typography variant="subtitle2">{t('lms.quizzes.options')}</Typography>
              <Button
                size="small"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={() => handleAddOption(questionIndex)}
              >
                {t('lms.quizzes.addOption')}
              </Button>
            </Stack>

            {watch(`questions.${questionIndex}.options`) &&
              Array.isArray(watch(`questions.${questionIndex}.options`)) &&
              watch(`questions.${questionIndex}.options`).map((option, optionIndex) => (
                <Card key={optionIndex} sx={{ p: 2, mb: 2, bgcolor: 'background.paper' }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="body2">
                      {t('lms.quizzes.optionNumber', { number: optionIndex + 1 })}
                    </Typography>
                    {watch(`questions.${questionIndex}.options`) &&
                      Array.isArray(watch(`questions.${questionIndex}.options`)) &&
                      watch(`questions.${questionIndex}.options`).length > 1 && (
                        <IconButton
                          size="small"
                          onClick={() => handleRemoveOption(questionIndex, optionIndex)}
                        >
                          <Iconify icon="eva:trash-2-outline" />
                        </IconButton>
                      )}
                  </Stack>

                  {renderOptionFields(questionIndex, optionIndex, questionType)}
                </Card>
              ))}
          </Card>
        );
      })}
    </Card>
  );

  return (
    <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          {renderQuizDetails}
        </Grid>

        <Grid item xs={12} md={8}>
          {renderQuestions}
        </Grid>
      </Grid>

      <Stack direction="row" justifyContent="flex-end" spacing={2} sx={{ mt: 3 }}>
        <LoadingButton
          type="submit"
          variant="contained"
          size="large"
          loading={loadingSave && isSubmitting}
        >
          {t('common.save')}
        </LoadingButton>
      </Stack>
    </FormProvider>
  );
}

QuizNewEditForm.propTypes = {
  currentQuiz: PropTypes.object,
  isEdit: PropTypes.bool,
  loadingSave: PropTypes.bool,
  onSave: PropTypes.func,
};
