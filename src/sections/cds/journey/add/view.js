/* eslint-disable no-nested-ternary */
/* eslint-disable react-hooks/exhaustive-deps */

// @mui
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Typography from '@mui/material/Typography';
import { useEffect, useState } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAITranslator } from 'src/apis/useAITranslator';

// components
import { useSettingsContext } from 'src/components/settings';

import { LoadingButton } from '@mui/lab';

import { useCourses, useJourneys, useUsecases } from 'src/apis';
import { useIdeations } from 'src/apis/useIdeations';
import TranslateConfirmationModal from 'src/components/TranslateConfirmationModal';
import { appCreator, buttonTypes, genAI, journeyTypes, workflowCreator } from 'src/utils/Constants';
import JourneyCard from './components/JourneyCard';
import { useOnboarding } from 'src/apis/useOnboarding';

// ----------------------------------------------------------------------

export default function AddJourneyView() {
  const settings = useSettingsContext();
  const { addJourney } = useJourneys();

  const {
    getIndustry,
    getFunctions,
    getLevels,
    getManagementRoles,
    industry,
    functions,
    levels,
    managementRoles,
  } = useOnboarding();

  const { getUsecases, usecases = [] } = useUsecases();
  const { getCourses, courses = [] } = useCourses();
  const { getIdeations, ideations = [] } = useIdeations();

  const { getPlatformLanguages, translateJourney, platformLanguages = [] } = useAITranslator();
  const [titleEnglish, setTitleEnglish] = useState('');

  const [description, setDescription] = useState('');

  const [buttonText, setButtonText] = useState('');
  const [buttonURL, setButtonURL] = useState('');

  // Provider state tanımlaması
  const [selectedProvider, setSelectedProvider] = useState('ai-business-school');
  const [providerOptions] = useState([
    { label: 'AI Business School', value: 'ai-business-school' },
    { label: 'Microsoft', value: 'microsoft' },
  ]);
  const [providerInputValue, setProviderInputValue] = useState('');

  const [selectedJourneyType, setSelectedJourneyType] = useState(journeyTypes[0]);
  const [journeyTypeInputValue, setJourneyTypeInputValue] = useState('');

  const [selectedButtonType, setSelectedButtonType] = useState(buttonTypes[0]);
  const [buttonTypeInputValue, setButtonTypeInputValue] = useState('');

  const [checkedNewTab, setCheckedNewTab] = useState(false);
  const [checkedClosedCard, setCheckedClosedCard] = useState(false);
  const [checkedLeadership, setCheckedLeadership] = useState(false);
  const [percent, setPercent] = useState(0);

  const [editors, setEditors] = useState([{ id: 0 }]);
  const [editorsContent, setEditorsContent] = useState(['']);

  const [selectedIdeation, setSelectedIdeation] = useState();
  const [ideationInputValue, setIdeationInputValue] = useState('');

  const [selectedAppCreator, setSelectedAppCreator] = useState();
  const [appCreatorInputValue, setAppCreatorInputValue] = useState('');

  const [selectedWorkflowCreator, setSelectedWorkflowCreator] = useState();
  const [workflowCreatorInputValue, setWorkflowCreatorInputValue] = useState('');

  const [selectedGenAICreator, setSelectedGenAICreator] = useState();
  const [genAICreatorInputValue, setGenAICreatorInputValue] = useState('');

  const [selectedCourse, setSelectedCourse] = useState();
  const [courseInputValue, setCourseInputValue] = useState('');

  const [selectedFSU, setSelectedFSU] = useState();
  const [fsuInputValue, setFsuInputValue] = useState('');

  const [selectedAOF, setSelectedAOF] = useState([]);
  const [selectedFunctions, setSelectedFunctions] = useState([]);
  const [selectedLevels, setSelectedLevels] = useState([]);
  const [selectedManagementRoles, setSelectedManagementRoles] = useState([]);

  const [fileUrl, setFileUrl] = useState('');

  const [isTranslating, setIsTranslating] = useState(false);

  const [currentTab, setCurrentTab] = useState('english');

  const [translations, setTranslations] = useState({
    english: { title: '', description: '', buttonText: '', content: '' },
  });

  const [openTranslateModal, setOpenTranslateModal] = useState(false);

  const sortLanguages = (languages) => {
    if (!languages) return [];

    return [...languages].sort((a, b) => {
      if (a.name.toLowerCase() === 'english') return -1;
      if (b.name.toLowerCase() === 'english') return 1;
      return 0;
    });
  };

  useEffect(() => {
    if (platformLanguages?.length > 0) {
      const sortedLanguages = sortLanguages(platformLanguages);
      setCurrentTab(sortedLanguages[0].name.toLowerCase());
    }
  }, [platformLanguages]);

  const handleChangetitleEnglish = (e, lang = 'english') => {
    if (lang === 'english') {
      setTitleEnglish(e.target.value);
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        title: e.target.value,
      },
    }));
  };

  const handleChangeDescription = (e, lang = 'english') => {
    if (lang === 'english') {
      setDescription(e.target.value);
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        description: e.target.value,
      },
    }));
  };

  const handleChangeLeadership = (e) => {
    setCheckedLeadership(e.target.checked);
  };

  const handleChangeButtonText = (e, lang = 'english') => {
    if (lang === 'english') {
      setButtonText(e.target.value);
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        buttonText: e.target.value,
      },
    }));
  };

  const handleChangeButtonUrl = (e) => {
    setButtonURL(e.target.value);
  };
  const handleChangeNewTab = (event) => {
    setCheckedNewTab(event.target.checked);
  };
  const handleFileDrop = (url) => {
    setFileUrl(url);
  };
  const handleChangeClosedCard = (event) => {
    setCheckedClosedCard(event.target.checked);
  };
  const handleChangePercent = (event, newValue) => {
    if (typeof newValue === 'number') {
      setPercent(newValue);
    } else {
      const { value } = event.target;
      if (value === '' || (Number(value) >= 0 && Number(value) <= 100)) {
        setPercent(Number(value));
      }
    }
  };

  const handleEditorChange = (id, content, lang = 'english') => {
    if (lang === 'english') {
      setEditorsContent((prev) => {
        const updatedContent = [...prev];
        updatedContent[id] = content;
        return updatedContent;
      });
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        content,
      },
    }));
  };

  const handleAddEditor = () => {
    setEditors((prevEditors) => [...prevEditors, { id: prevEditors.length }]);
    setEditorsContent((prevContent) => [...prevContent, '']);
  };

  const handleRemoveEditor = (id) => {
    setEditors((prevEditors) => prevEditors.filter((editor) => editor.id !== id));
    setEditorsContent((prevContent) => {
      const updatedContent = [...prevContent];
      updatedContent.splice(id, 1);
      return updatedContent;
    });
  };

  const handleAOFChange = (event) => {
    const value = event.target.name;
    setSelectedAOF((prev) =>
      prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]
    );
  };

  const handleFunctionChange = (event) => {
    const value = event.target.name;
    setSelectedFunctions((prev) =>
      prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]
    );
  };

  const handleLevelChange = (event) => {
    const value = event.target.name;
    setSelectedLevels((prev) =>
      prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]
    );
  };

  const handleManagementRoleChange = (event) => {
    const value = event.target.name;
    setSelectedManagementRoles((prev) =>
      prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]
    );
  };

  useEffect(() => {
    if (selectedJourneyType !== 'Content') {
      setEditorsContent(['']);
    }
    if (selectedJourneyType !== 'Ideation Project') {
      setSelectedIdeation(null);
    }
    if (selectedJourneyType !== 'App Creator') {
      setSelectedAppCreator(null);
    }
    if (selectedJourneyType !== 'Workflow Creator') {
      setSelectedWorkflowCreator(null);
    }
    if (selectedJourneyType !== 'GenAI Playground') {
      setSelectedGenAICreator(null);
    }
  }, [selectedJourneyType]);

  useEffect(() => {
    getIndustry();
    getFunctions();
    getLevels();
    getManagementRoles();
    getPlatformLanguages();
    getUsecases(1, '', '', '', 1000);
    getCourses();
    getIdeations();
  }, []);

  const resetForm = () => {
    setTitleEnglish('');
    setDescription('');
    setButtonText('');
    setEditorsContent(['']);
    setTranslations({
      english: { title: '', description: '', buttonText: '', content: '' },
    });
    setButtonURL(null);
    setSelectedJourneyType(journeyTypes[0]);
    setJourneyTypeInputValue('');
    setCheckedLeadership(false);
    setSelectedButtonType(buttonTypes[0]);
    setButtonTypeInputValue('');
    setCheckedNewTab(false);
    setCheckedClosedCard(false);
    setPercent(0);
    setEditors([{ id: 0 }]);
    setSelectedIdeation(null);
    setIdeationInputValue('');
    setSelectedAppCreator(null);
    setAppCreatorInputValue('');
    setSelectedWorkflowCreator(null);
    setWorkflowCreatorInputValue('');
    setSelectedGenAICreator(null);
    setGenAICreatorInputValue('');
    setSelectedProvider('ai-business-school');
    setProviderInputValue('');
    setFsuInputValue('');
    setSelectedAOF([]);
    setSelectedFunctions([]);
    setSelectedLevels([]);
    setSelectedManagementRoles([]);
    setFileUrl('');
  };

  // eslint-disable-next-line arrow-body-style
  const isFormValid = () => {
    return titleEnglish;
  };

  const createNewJourney = () => {
    // Convert English content to array format
    const formattedTranslations = {
      ...translations,
      english: {
        ...translations.english,
        content: Array.isArray(translations.english.content)
          ? translations.english.content
          : [translations.english.content || ''],
      },
    };

    if (!isFormValid()) {
      toast.error('Please fill in all fields before adding the journey');
      return;
    }

    // Determine the correct ID for FSU

    let fsuId = null;
    let courseId = null;
    let ideationId = null;

    if (selectedJourneyType === 'Usecases' && selectedFSU) {
      fsuId = selectedFSU._id; // Get ID for FSU
    } else if (selectedJourneyType === 'Course' && selectedCourse) {
      courseId = selectedCourse._id; // Get ID for Course
    } else if (selectedJourneyType === 'Ideation Project' && selectedIdeation) {
      ideationId = selectedIdeation._id; // Get ID for Ideation
    }

    addJourney(
      titleEnglish,
      description,
      selectedProvider,
      checkedLeadership,
      journeyTypeInputValue,
      checkedNewTab,
      checkedClosedCard,
      fsuId,
      ideationId,
      courseId,
      editorsContent,
      buttonTypeInputValue,
      buttonText,
      buttonURL,
      fileUrl,
      selectedAOF,
      selectedFunctions,
      selectedManagementRoles,
      selectedLevels,
      percent,
      formattedTranslations
    ).then((res) => {
      if (res.status === 'success') {
        toast.success('Journey added successfully');
        resetForm();
      } else {
        toast.error('Journey add error');
      }
    });
  };
  useEffect(() => {
    setIsTranslating(false);
  }, [translations]);

  const handleTranslate = () => {
    if (!titleEnglish.trim()) {
      toast.error('Please fill in the English title first');
      return;
    }
    setOpenTranslateModal(true);
  };

  const handleConfirmTranslate = () => {
    const journey = {
      title_english: titleEnglish,
      description,
      buttonText,
      content: editorsContent,
    };

    platformLanguages.forEach((language) => {
      if (language.name.toLowerCase() !== 'english') {
        translateJourney(journey, 'en', language.lang)
          .then((res) => {
            if (res) {
              setTranslations((prev) => ({
                ...prev,
                [language.name.toLowerCase()]: {
                  title: res.message.title || res.message.title_english || '',
                  description: res.message.description || '',
                  buttonText: res.message.buttonText || '',
                  content: Array.isArray(res.message.content)
                    ? res.message.content
                    : [res.message.content || ''],
                },
              }));
              setIsTranslating(false);
              toast.success(`${language.name} translation success`);
            }
          })
          .catch((error) => {
            console.error(`Error during ${language.name} translation:`, error);
            toast.error(`${language.name} translation failed`);
          });
      }
    });

    setOpenTranslateModal(false);
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4"> Add Journey </Typography>

      <Box
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          mt: 3,
          display: 'flex',
          gap: 2,
          justifyContent: 'space-between',
        }}
      >
        <Tabs value={currentTab || 'english'} onChange={(e, newValue) => setCurrentTab(newValue)}>
          {platformLanguages &&
            sortLanguages(platformLanguages).map((lang) => (
              <Tab key={lang.name} value={lang.name.toLowerCase()} label={lang.name} />
            ))}
        </Tabs>
        <LoadingButton
          variant="contained"
          onClick={handleTranslate}
          loading={isTranslating}
          loadingIndicator="Translating..."
          disabled={!translations.english?.title && !translations.english?.description}
        >
          Translate
        </LoadingButton>
      </Box>

      {platformLanguages &&
        sortLanguages(platformLanguages).map(
          (lang) =>
            currentTab === lang.name.toLowerCase() && (
              <JourneyCard
                key={lang.name}
                titleEnglish={translations[lang.name.toLowerCase()]?.title || ''}
                description={translations[lang.name.toLowerCase()]?.description || ''}
                buttonText={translations[lang.name.toLowerCase()]?.buttonText || ''}
                editorsContent={
                  lang.name.toLowerCase() === 'english'
                    ? editorsContent
                    : Array.isArray(translations[lang.name.toLowerCase()]?.content)
                      ? translations[lang.name.toLowerCase()]?.content
                      : [translations[lang.name.toLowerCase()]?.content || '']
                }
                checkedLeadership={checkedLeadership}
                handleChangeLeadership={handleChangeLeadership}
                handleChangeDescription={(e) => handleChangeDescription(e, lang.name.toLowerCase())}
                handleChangetitleEnglish={(e) =>
                  handleChangetitleEnglish(e, lang.name.toLowerCase())
                }
                handleChangeButtonText={(e) => handleChangeButtonText(e, lang.name.toLowerCase())}
                handleEditorChange={(id, content) =>
                  handleEditorChange(id, content, lang.name.toLowerCase())
                }
                checkedClosedCard={checkedClosedCard}
                checkedNewTab={checkedNewTab}
                handleChangeButtonUrl={handleChangeButtonUrl}
                buttonTypes={buttonTypes}
                buttonTypeInputValue={buttonTypeInputValue}
                buttonURL={buttonURL}
                handleChangeNewTab={handleChangeNewTab}
                handleChangeClosedCard={handleChangeClosedCard}
                handleChangePercent={handleChangePercent}
                journeyTypeInputValue={journeyTypeInputValue}
                journeyTypes={journeyTypes}
                percent={percent}
                onDropFile={handleFileDrop}
                fileUrl={fileUrl}
                selectedButtonType={selectedButtonType}
                selectedProvider={selectedProvider}
                providerOptions={providerOptions}
                providerInputValue={providerInputValue}
                setSelectedProvider={setSelectedProvider}
                setProviderInputValue={setProviderInputValue}
                selectedJourneyType={selectedJourneyType}
                setSelectedJourneyType={setSelectedJourneyType}
                setJourneyTypeInputValue={setJourneyTypeInputValue}
                setSelectedButtonType={setSelectedButtonType}
                setButtonTypeInputValue={setButtonTypeInputValue}
                editors={editors}
                handleAddEditor={handleAddEditor}
                handleRemoveEditor={handleRemoveEditor}
                selectedAOF={selectedAOF}
                handleAOFChange={handleAOFChange}
                selectedFunctions={selectedFunctions}
                handleFunctionChange={handleFunctionChange}
                selectedLevels={selectedLevels}
                handleLevelChange={handleLevelChange}
                selectedManagementRoles={selectedManagementRoles}
                handleManagementRoleChange={handleManagementRoleChange}
                selectedCourse={selectedCourse}
                courseInputValue={courseInputValue}
                handleCourseInputChange={setCourseInputValue}
                handleSelectedCourseChange={setSelectedCourse}
                selectedFSU={selectedFSU}
                fsuInputValue={fsuInputValue}
                handleFsuInputChange={setFsuInputValue}
                handleSelectedFsuChange={setSelectedFSU}
                selectedIdeation={selectedIdeation}
                ideationInputValue={ideationInputValue}
                handleIdeationInputChange={setIdeationInputValue}
                handleSelectedIdeationChange={setSelectedIdeation}
                courses={courses}
                industry={industry}
                functions={functions}
                usecases={usecases}
                ideations={ideations}
                levels={levels}
                managementRoles={managementRoles}
                createNewJourney={createNewJourney}
              />
            )
        )}

      <TranslateConfirmationModal
        open={openTranslateModal}
        onClose={() => setOpenTranslateModal(false)}
        onConfirm={handleConfirmTranslate}
      />
    </Container>
  );
}
