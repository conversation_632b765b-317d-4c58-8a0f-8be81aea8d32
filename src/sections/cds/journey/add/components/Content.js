import {
  Autocomplete,
  Box,
  FormControlLabel,
  Grid,
  Slider,
  Stack,
  Switch,
  Text<PERSON>ield,
  Typography,
  Divider,
} from '@mui/material';
import PropTypes from 'prop-types';
import { UploadBox } from 'src/components/upload';

export default function Content({
  handleChangetitleEnglish,
  handleChangeDescription,
  handleChangeLeadership,
  checkedLeadership,
  description,
  selectedJourneyType,
  setSelectedJourneyType,
  journeyTypeInputValue,
  setJourneyTypeInputValue,
  selectedButtonType,
  setSelectedButtonType,
  buttonTypeInputValue,
  journeyTypes = [],
  buttonTypes = [],
  setButtonTypeInputValue,
  checkedNewTab,
  handleChangeNewTab,
  checkedClosedCard,
  handleChangeClosedCard,
  handleChangeButtonText,
  buttonText,
  titleEnglish,
  handleChangeButtonUrl,
  buttonURL,
  percent,
  handleChangePercent,
  fileUrl,
  onDropFile,
  currentTab,
  selectedProvider,
  providerOptions = [
    { label: 'AI Business School', value: 'ai-business-school' },
    { label: 'Microsoft', value: 'microsoft' },
  ],
  providerInputValue,
  setSelectedProvider,
  setProviderInputValue,
}) {
  const getSelectedProviderObject = () => {
    if (!selectedProvider || !providerOptions || !providerOptions.length) {
      return null;
    }

    return providerOptions.find((option) => option.value === selectedProvider) || null;
  };

  return (
    <Box sx={{ height: '100%' }}>
      <Typography variant="h5">Journey Content</Typography>
      <Divider sx={{ my: 2, borderStyle: 'dashed' }} />
      <Stack direction="column" spacing={2}>
        <TextField
          fullWidth
          label="Title"
          variant="outlined"
          placeholder="Add Title"
          value={titleEnglish || ''}
          onChange={handleChangetitleEnglish}
        />
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Autocomplete
            fullWidth
            value={getSelectedProviderObject()}
            onChange={(event, newValue) => {
              setSelectedProvider(newValue ? newValue.value : '');
            }}
            inputValue={providerInputValue || ''}
            onInputChange={(event, newInputValue) => {
              if (typeof setProviderInputValue === 'function') {
                setProviderInputValue(newInputValue);
              }
            }}
            options={providerOptions || []}
            getOptionLabel={(option) => option.label || ''}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Provider"
                placeholder="Provider"
                InputProps={{
                  ...params.InputProps,
                }}
              />
            )}
          />
        </Box>

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Description"
          placeholder="Add Description"
          value={description || ''}
          onChange={handleChangeDescription}
        />

        <Grid container spacing={{ xs: 2, md: 2, lg: 2 }}>
          <Grid item xs={12} sm={12} md={6} lg={6} sx={{ mt: 3 }}>
            {journeyTypes && (
              <Autocomplete
                value={selectedJourneyType || null}
                onChange={(event, newValue) => {
                  setSelectedJourneyType(newValue);
                }}
                inputValue={journeyTypeInputValue || ''}
                onInputChange={(event, newValue) => {
                  setJourneyTypeInputValue(newValue);
                }}
                options={journeyTypes || []}
                isOptionEqualToValue={(option, value) => {
                  if (value === null || value === '') return true;
                  return option === value;
                }}
                renderInput={(params) => (
                  <TextField {...params} label="Journey Type" variant="outlined" />
                )}
              />
            )}
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={6} sx={{ mt: 3 }}>
            {buttonTypes && (
              <Autocomplete
                value={selectedButtonType || null}
                onChange={(event, newValue) => {
                  setSelectedButtonType(newValue);
                }}
                inputValue={buttonTypeInputValue || ''}
                onInputChange={(event, newValue) => {
                  setButtonTypeInputValue(newValue);
                }}
                options={buttonTypes || []}
                isOptionEqualToValue={(option, value) => {
                  if (value === null || value === '') return true;
                  return option === value;
                }}
                renderInput={(params) => <TextField {...params} label="Button Type" />}
              />
            )}
          </Grid>
        </Grid>

        <Stack sx={{ mt: 2 }}>
          <Grid container spacing={{ xs: 2, md: 2, lg: 2 }}>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              <FormControlLabel
                sx={{ ml: 1 }}
                control={
                  <Switch
                    checked={checkedNewTab}
                    sx={{ transform: 'scale(1.2)' }}
                    onChange={handleChangeNewTab}
                  />
                }
                label="Open in New Tab"
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              <FormControlLabel
                sx={{ ml: 1 }}
                control={
                  <Switch
                    checked={checkedClosedCard}
                    sx={{ transform: 'scale(1.2)' }}
                    onChange={handleChangeClosedCard}
                  />
                }
                label="Closed Card"
              />
            </Grid>
          </Grid>
        </Stack>

        <TextField
          id="button-text"
          label="Button Text"
          variant="outlined"
          value={buttonText}
          onChange={handleChangeButtonText}
          sx={{ mt: 2 }}
        />
        {buttonTypeInputValue === 'URL' && buttonTypes?.includes('Selected Content') && (
          <TextField
            id="button-url"
            label="Button URL"
            variant="outlined"
            value={buttonURL}
            onChange={handleChangeButtonUrl}
            sx={{ mt: 2 }}
          />
        )}
        {buttonTypeInputValue === 'File' && <UploadBox sx={{ mt: 2 }} onDropFile={onDropFile} />}
      </Stack>

      {fileUrl && (
        <Stack sx={{ mt: 3 }}>
          <b>File:</b>
          <a href={fileUrl} target="_blank" rel="noopener noreferrer">
            {fileUrl}
          </a>
        </Stack>
      )}

      <Stack sx={{ mt: 3, border: '1px solid #F1F2F5' }}>
        <Grid container>
          <Grid item xs={12} sm={12} md={8} lg={8}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: 53,
                ml: 5,
              }}
            >
              <Slider
                sx={{ width: '90%' }}
                size="medium"
                value={percent}
                onChange={handleChangePercent}
                aria-label="Percentage"
                valueLabelDisplay="auto"
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={4}>
            <Box>
              <TextField
                id="percent"
                label="Percent"
                value={percent}
                sx={{ ml: 4 }}
                variant="standard"
                onChange={handleChangePercent}
              />
            </Box>
          </Grid>
        </Grid>
      </Stack>
    </Box>
  );
}

Content.propTypes = {
  buttonText: PropTypes.string,
  titleEnglish: PropTypes.string,
  handleChangeDescription: PropTypes.func,
  description: PropTypes.string,
  handleChangetitleEnglish: PropTypes.func,
  selectedJourneyType: PropTypes.string,
  setSelectedJourneyType: PropTypes.func,
  journeyTypeInputValue: PropTypes.string,
  setJourneyTypeInputValue: PropTypes.func,
  selectedButtonType: PropTypes.string,
  setSelectedButtonType: PropTypes.func,
  buttonTypeInputValue: PropTypes.string,
  setButtonTypeInputValue: PropTypes.func,
  checkedNewTab: PropTypes.bool,
  journeyTypes: PropTypes.array,
  buttonTypes: PropTypes.array,
  handleChangeNewTab: PropTypes.func,
  checkedClosedCard: PropTypes.bool,
  handleChangeClosedCard: PropTypes.func,
  handleChangeButtonText: PropTypes.func,
  handleChangeButtonUrl: PropTypes.func,
  percent: PropTypes.number,
  handleChangePercent: PropTypes.func,
  fileUrl: PropTypes.string,
  onDropFile: PropTypes.func,
  currentTab: PropTypes.string,
  selectedProvider: PropTypes.string,
  providerOptions: PropTypes.array,
  providerInputValue: PropTypes.string,
  setSelectedProvider: PropTypes.func,
  setProviderInputValue: PropTypes.func,
};
