import { Box, Checkbox, FormControlLabel, Grid, Stack, Typography } from '@mui/material';
import PropTypes from 'prop-types';

export default function CheckboxArea({
  aof,
  functions,
  levels,
  managementRoles,
  handleAOFChange,
  handleFunctionChange,
  handleLevelChange,
  handleManagementRoleChange,
  selectedAOF,
  selectedFunctions,
  selectedLevels,
  selectedManagementRoles,
}) {
  return (
    <Stack sx={{ mt: 5 }}>
      <Grid container spacing={{ xs: 2, md: 3, lg: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
        <Grid item xs={12} sm={12} md={3} lg={3} sx={{ border: '1px solid #F1F2F5' }}>
          <Typography variant="h6" component="h6">
            Industry
          </Typography>
          {aof?.map((item) => (
            <Box key={item._id}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedAOF.includes(item.slug)}
                    onChange={handleAOFChange}
                    name={item.slug}
                  />
                }
                label={item.translations?.en}
              />
            </Box>
          ))}
        </Grid>
        <Grid item xs={12} sm={12} md={3} lg={3} sx={{ border: '1px solid #F1F2F5' }}>
          <Typography variant="h6" component="h6">
            Functions
          </Typography>
          {functions?.map((item) => (
            <Box key={item._id}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedFunctions?.includes(item.slug)}
                    onChange={handleFunctionChange}
                    name={item.slug}
                  />
                }
                label={item.translations?.en}
              />
            </Box>
          ))}
        </Grid>
        <Grid item xs={12} sm={12} md={3} lg={3} sx={{ border: '1px solid #F1F2F5' }}>
          <Typography variant="h6" component="h6">
            Levels
          </Typography>
          {levels?.map((item) => (
            <Box key={item._id}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedLevels.includes(item.slug)}
                    onChange={handleLevelChange}
                    name={item.slug}
                  />
                }
                label={item.translations?.en}
              />
            </Box>
          ))}
        </Grid>
        <Grid item xs={12} sm={12} md={3} lg={3} sx={{ border: '1px solid #F1F2F5' }}>
          <Typography variant="h6" component="h6">
            Management Roles
          </Typography>
          {managementRoles?.map((item) => (
            <Box key={item._id}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedManagementRoles?.includes(item.slug)}
                    onChange={handleManagementRoleChange}
                    name={item.slug}
                  />
                }
                label={item.translations?.en}
              />
            </Box>
          ))}
        </Grid>
      </Grid>
    </Stack>
  );
}
CheckboxArea.propTypes = {
  aof: PropTypes.array,
  functions: PropTypes.array,
  levels: PropTypes.array,
  managementRoles: PropTypes.array,
  handleAOFChange: PropTypes.func,
  handleFunctionChange: PropTypes.func,
  handleLevelChange: PropTypes.func,
  handleManagementRoleChange: PropTypes.func,
  selectedAOF: PropTypes.array,
  selectedFunctions: PropTypes.array,
  selectedLevels: PropTypes.array,
  selectedManagementRoles: PropTypes.array,
};
