/* eslint-disable no-return-assign */
import { CheckCircle } from '@mui/icons-material';
import {
  Autocomplete,
  Box,
  Button,
  Grid,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import PropTypes from 'prop-types';
import { useRef } from 'react';
import Iconify from 'src/components/iconify';
import BundledEditor from 'src/components/text-editor/editor';
import { journeyTypeMap } from 'src/utils/Constants';
import FunctionSpecific from './FunctionSpecific';

export default function JourneyType({
  selectedJourneyType,
  editors,
  editorsContent,
  handleAddEditor,
  handleEditorChange,
  handleRemoveEditor,
  fsm,
  fsu,
  courses,
  ideations,
  selectedFSM,
  setSelectedFSM,
  fsmInputValue,
  setFsmInputValue,
  selectedFSU,
  setSelectedFSU,
  fsuInputValue,
  setFsuInputValue,
  selectedCourse,
  setSelectedCourse,
  courseInputValue,
  setCourseInputValue,
  selectedIdeation,
  setSelectedIdeation,
  ideationInputValue,
  setIdeationInputValue,
  tinyMceConfig = {},
  ...props
}) {
  const editorRefs = useRef({});

  const handleContentChange = (id, content) => {
    handleEditorChange(id, content);
  };

  const renderFunctionSpecific = (type) => {
    const selectedKey = journeyTypeMap[type];
    if (!selectedKey) return null;

    if (selectedKey === 'fsu') {
      return (
        <Box sx={{ width: '100%', mt: 2 }}>
          <Autocomplete
            fullWidth
            options={fsu?.data?.useCases || []}
            value={selectedFSU}
            onChange={(event, newValue) => {
              setSelectedFSU(newValue);
            }}
            getOptionLabel={(option) => option.title || ''}
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%',
                  }}
                >
                  <Typography>{option.title}</Typography>
                  {selected && (
                    <CheckCircle
                      sx={{
                        color: 'primary.main',
                        ml: 1,
                      }}
                    />
                  )}
                </Box>
              </li>
            )}
            renderInput={(params) => (
              <TextField {...params} label="Select Use Case" placeholder="Search for Use Case..." />
            )}
            isOptionEqualToValue={(option, value) => option._id === value?._id}
          />
        </Box>
      );
    }

    const dataMapping = {
      fsm: {
        data: fsm,
        emptyMessage: 'No Function Specific Modules exist yet.',
        searchPlaceholder: 'Search for Function Specific Modules...',
      },
      course: {
        data: Array.isArray(courses) ? courses : courses?.data?.courses ? courses.data.courses : [],
        emptyMessage: 'No courses exist yet.',
        searchPlaceholder: 'Search for courses...',
      },
      ideations: {
        data: ideations,
        emptyMessage: 'No ideations exist yet.',
        searchPlaceholder: 'Search for ideations...',
      },
    };

    const stateMapping = {
      fsm: {
        selected: selectedFSM,
        setSelected: setSelectedFSM,
        inputValue: fsmInputValue,
        setInputValue: setFsmInputValue,
      },
      course: {
        selected: selectedCourse,
        setSelected: setSelectedCourse,
        inputValue: courseInputValue,
        setInputValue: setCourseInputValue,
      },
      ideations: {
        selected: selectedIdeation,
        setSelected: setSelectedIdeation,
        inputValue: ideationInputValue,
        setInputValue: setIdeationInputValue,
      },
    };

    if (!dataMapping[selectedKey]) return null;

    return (
      <FunctionSpecific
        title={type}
        data={dataMapping[selectedKey].data}
        journeyType={selectedJourneyType}
        selected={stateMapping[selectedKey].selected}
        setSelected={stateMapping[selectedKey].setSelected}
        inputValue={stateMapping[selectedKey].inputValue}
        setInputValue={stateMapping[selectedKey].setInputValue}
        emptyMessage={dataMapping[selectedKey].emptyMessage}
        searchPlaceholder={dataMapping[selectedKey].searchPlaceholder}
      />
    );
  };

  return (
    <Grid item xs={12} sm={12} md={6} lg={6}>
      {['Workflow Creator', 'App Creator', 'GenAI Playground'].includes(
        selectedJourneyType
      ) ? null : (
        <>
          {selectedJourneyType === 'Content' && (
            <Stack key={selectedJourneyType}>
              <Typography variant="h6">Content Blocks</Typography>
              {editors?.map((editor, index) => (
                <Box key={editor.id} sx={{ position: 'relative', mb: 2 }}>
                  <BundledEditor
                    onInit={(_evt, editorInstance) =>
                      (editorRefs.current[editor.id] = editorInstance)
                    }
                    onEditorChange={(content) => handleContentChange(editor.id, content)}
                    value={editorsContent[index]}
                    init={{
                      height: 500,
                      menubar: false,
                      plugins: [
                        'advlist',
                        'anchor',
                        'autolink',
                        'image',
                        'media',
                        'link',
                        'lists',
                        'searchreplace',
                        'codesample',
                        'table',
                        'wordcount',
                      ],
                      toolbar:
                        'undo redo | blocks | ' +
                        'bold italic forecolor | codesample | alignleft aligncenter ' +
                        'alignright alignjustify | bullist numlist outdent indent | ' +
                        'removeformat | image | media ',
                      content_style:
                        'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                      base_url: '/tinymce',
                      skin: 'oxide',
                      skin_url: '/tinymce/skins/ui/oxide',
                      content_css: '/tinymce/skins/content/default/content.css',

                      // Resim ve Medya İletişim Kutuları için Özel Ayarlar
                      image_advtab: true,
                      image_dimensions: true,
                      image_title: true,
                      image_caption: true,

                      // Dialog pencereleri için kritik ayarlar
                      dialog_type: 'modal',
                      file_picker_types: 'image media',

                      // Upload özelliklerini kapat - manuel URL giriş seçenekleri çalışsın
                      automatic_uploads: false,
                      image_uploadtab: false,

                      // Medya ayarları
                      media_live_embeds: true,
                      media_alt_source: true,
                      media_poster: true,
                      media_dimensions: true,

                      // URL işleme ayarları - URL dönüşümünü yapma
                      convert_urls: false,
                      relative_urls: false,
                      remove_script_host: false,
                      document_base_url: window.location.origin,

                      // iFrame desteği (medya embedlemek için)
                      extended_valid_elements:
                        'iframe[src|frameborder|style|scrolling|class|width|height|name|align]',

                      // Dialog formları için IMPORTANT ayarı:
                      // TinyMCE DOM event binding sorununu düzelt
                      browser_spellcheck: true,
                      hidden_input: false,

                      // Parent bileşenden gelen konfigürasyonu ekle
                      ...tinyMceConfig,
                    }}
                  />
                  <IconButton
                    color="error"
                    onClick={() => {
                      handleRemoveEditor(editor.id);
                      delete editorRefs.current[editor.id];
                    }}
                    sx={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      mt: 1,
                      mr: 1,
                      zIndex: 99,
                    }}
                  >
                    <Iconify icon="mdi:trash" />
                  </IconButton>
                </Box>
              ))}
              <Button variant="contained" color="primary" sx={{ mt: 2 }} onClick={handleAddEditor}>
                Add Content Block
              </Button>
            </Stack>
          )}
          <Typography variant="h6">{selectedJourneyType}</Typography>

          {journeyTypeMap[selectedJourneyType] && renderFunctionSpecific(selectedJourneyType)}
        </>
      )}
    </Grid>
  );
}

JourneyType.propTypes = {
  selectedJourneyType: PropTypes.string,
  editors: PropTypes.array,
  editorsContent: PropTypes.array,
  handleAddEditor: PropTypes.func,
  handleEditorChange: PropTypes.func,
  handleRemoveEditor: PropTypes.func,
  selectedFSM: PropTypes.shape({
    _id: PropTypes.string,
    title: PropTypes.string,
    slug: PropTypes.string,
  }),
  fsmInputValue: PropTypes.string,
  setFsmInputValue: PropTypes.func,
  selectedFSU: PropTypes.any,
  setSelectedFSU: PropTypes.func,
  fsuInputValue: PropTypes.string,
  setFsuInputValue: PropTypes.func,
  selectedIdeation: PropTypes.object,
  setSelectedIdeation: PropTypes.func,
  ideationInputValue: PropTypes.string,
  setIdeationInputValue: PropTypes.func,
  tinyMceConfig: PropTypes.object,
};
