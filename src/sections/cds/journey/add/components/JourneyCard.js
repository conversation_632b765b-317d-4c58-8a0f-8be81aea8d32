import { alpha, Button, Card, Grid } from '@mui/material';
import PropTypes from 'prop-types';
import CheckboxArea from './CheckboxArea';
import Content from './Content';
import JourneyType from './JourneyType';

export default function JourneyCard({
  titleEnglish = '',
  handleChangeDescription,
  handleChangeLeadership,
  checkedLeadership,
  description = '',
  buttonText = '',
  buttonTypeInputValue = '',
  buttonTypes = [],
  checkedClosedCard = false,
  checkedNewTab = false,
  handleChangeButtonText,
  handleChangeButtonUrl,
  handleChangeClosedCard,
  handleChangeNewTab,
  handleChangePercent,
  handleChangetitleEnglish,
  journeyTypeInputValue = '',
  journeyTypes = [],
  percent = 0,
  onDropFile,
  fileUrl = '',
  selectedButtonType = '',
  selectedJourneyType = '',
  setButtonTypeInputValue,
  setJourneyTypeInputValue,
  setSelectedButtonType,
  setSelectedJourneyType,
  selectedProvider = 'ai-business-school',
  providerOptions = [],
  providerInputValue = '',
  setSelectedProvider,
  setProviderInputValue,
  editors = [],
  editorsContent = [],
  handleAddEditor,
  handleEditorChange,
  handleRemoveEditor,
  selectedFSU = null,
  handleSelectedFsuChange,
  fsuInputValue = '',
  handleFsuInputChange,
  selectedCourse = null,
  handleSelectedCourseChange,
  courseInputValue = '',
  handleCourseInputChange,
  usecases = [],
  ideations = [],
  courses = [],
  industry = [],
  functions = [],
  levels = [],
  managementRoles = [],
  handleAOFChange,
  handleFunctionChange,
  handleLevelChange,
  handleManagementRoleChange,
  selectedAOF = [],
  selectedFunctions = [],
  selectedLevels = [],
  selectedManagementRoles = [],
  createNewJourney,
  selectedIdeation = null,
  handleSelectedIdeationChange,
  ideationInputValue = '',
  handleIdeationInputChange,
}) {
  return (
    <Card
      sx={{
        minHeight: 570,
        mt: 5,
        borderRadius: 2,
        flexGrow: 1,
        bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04),
        p: 2,
      }}
    >
      <Grid container spacing={{ xs: 2, md: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
        <Grid item xs={12} sm={12} md={6} lg={6}>
          <Content
            titleEnglish={titleEnglish}
            handleChangeDescription={handleChangeDescription}
            handleChangeLeadership={handleChangeLeadership}
            checkedLeadership={checkedLeadership}
            description={description}
            buttonText={buttonText}
            buttonTypeInputValue={buttonTypeInputValue}
            buttonTypes={buttonTypes}
            checkedClosedCard={checkedClosedCard}
            checkedNewTab={checkedNewTab}
            handleChangeButtonText={handleChangeButtonText}
            handleChangeButtonUrl={handleChangeButtonUrl}
            handleChangeClosedCard={handleChangeClosedCard}
            handleChangeNewTab={handleChangeNewTab}
            handleChangePercent={handleChangePercent}
            handleChangetitleEnglish={handleChangetitleEnglish}
            journeyTypeInputValue={journeyTypeInputValue}
            journeyTypes={journeyTypes}
            percent={percent}
            onDropFile={onDropFile}
            fileUrl={fileUrl}
            selectedButtonType={selectedButtonType}
            selectedJourneyType={selectedJourneyType}
            setButtonTypeInputValue={setButtonTypeInputValue}
            setJourneyTypeInputValue={setJourneyTypeInputValue}
            setSelectedButtonType={setSelectedButtonType}
            setSelectedJourneyType={setSelectedJourneyType}
            selectedProvider={selectedProvider}
            providerOptions={providerOptions}
            providerInputValue={providerInputValue}
            setSelectedProvider={setSelectedProvider}
            setProviderInputValue={setProviderInputValue}
          />
        </Grid>
        <JourneyType
          selectedJourneyType={selectedJourneyType}
          editors={editors}
          editorsContent={editorsContent}
          selectedFSU={selectedFSU}
          setSelectedFSU={handleSelectedFsuChange}
          setFsuInputValue={handleFsuInputChange}
          selectedIdeation={selectedIdeation}
          setSelectedIdeation={handleSelectedIdeationChange}
          ideationInputValue={ideationInputValue}
          selectedCourse={selectedCourse}
          setSelectedCourse={handleSelectedCourseChange}
          courseInputValue={courseInputValue}
          setCourseInputValue={handleCourseInputChange}
          fsu={fsuInputValue}
          handleAddEditor={handleAddEditor}
          handleEditorChange={handleEditorChange}
          handleRemoveEditor={handleRemoveEditor}
          selectedFSM={selectedFSU}
          setSelectedFSM={selectedFSU}
          fsmInputValue={fsuInputValue}
          setFsmInputValue={handleFsuInputChange}
          fsuInputValue={fsuInputValue}
          ideations={ideations}
          courses={courses}
          appCreator={industry}
          workflowCreator={industry}
          genAI={industry}
          setIdeationInputValue={handleIdeationInputChange}
        />
      </Grid>
      <CheckboxArea
        aof={industry}
        functions={functions}
        levels={levels}
        managementRoles={managementRoles}
        handleAOFChange={handleAOFChange}
        handleFunctionChange={handleFunctionChange}
        handleLevelChange={handleLevelChange}
        handleManagementRoleChange={handleManagementRoleChange}
        selectedAOF={selectedAOF}
        selectedFunctions={selectedFunctions}
        selectedLevels={selectedLevels}
        selectedManagementRoles={selectedManagementRoles}
      />
      <Button variant="contained" fullWidth sx={{ mt: 3 }} onClick={createNewJourney}>
        Add Journey
      </Button>
    </Card>
  );
}

JourneyCard.propTypes = {
  titleEnglish: PropTypes.string,
  handleChangeDescription: PropTypes.func,
  description: PropTypes.string,
  buttonText: PropTypes.string,
  buttonTypeInputValue: PropTypes.string,
  buttonTypes: PropTypes.array,
  checkedClosedCard: PropTypes.bool,
  checkedNewTab: PropTypes.bool,
  handleChangeButtonText: PropTypes.func,
  handleChangeButtonUrl: PropTypes.func,
  handleChangeClosedCard: PropTypes.func,
  handleChangeNewTab: PropTypes.func,
  handleChangePercent: PropTypes.func,
  handleChangetitleEnglish: PropTypes.func,
  journeyTypeInputValue: PropTypes.string,
  journeyTypes: PropTypes.array,
  percent: PropTypes.number,
  onDropFile: PropTypes.func,
  fileUrl: PropTypes.string,
  selectedButtonType: PropTypes.string,
  selectedJourneyType: PropTypes.string,
  setButtonTypeInputValue: PropTypes.func,
  setJourneyTypeInputValue: PropTypes.func,
  setSelectedButtonType: PropTypes.func,
  setSelectedJourneyType: PropTypes.func,
  selectedProvider: PropTypes.string,
  providerOptions: PropTypes.array,
  providerInputValue: PropTypes.string,
  setSelectedProvider: PropTypes.func,
  setProviderInputValue: PropTypes.func,
  editors: PropTypes.array,
  editorsContent: PropTypes.array,
  handleAddEditor: PropTypes.func,
  handleEditorChange: PropTypes.func,
  handleRemoveEditor: PropTypes.func,
  selectedFSU: PropTypes.shape({
    _id: PropTypes.string,
    title: PropTypes.string,
    slug: PropTypes.string,
    // ... diğer FSU özellikleri
  }),
  handleSelectedFsuChange: PropTypes.func,
  fsuInputValue: PropTypes.string,
  handleFsuInputChange: PropTypes.func,
  selectedCourse: PropTypes.string,
  handleSelectedCourseChange: PropTypes.func,
  courseInputValue: PropTypes.string,
  handleCourseInputChange: PropTypes.func,
  usecases: PropTypes.array,
  ideations: PropTypes.array,
  courses: PropTypes.array,
  industry: PropTypes.array,
  functions: PropTypes.array,
  levels: PropTypes.array,
  managementRoles: PropTypes.array,
  handleAOFChange: PropTypes.func,
  handleFunctionChange: PropTypes.func,
  handleLevelChange: PropTypes.func,
  handleManagementRoleChange: PropTypes.func,
  selectedAOF: PropTypes.array,
  selectedFunctions: PropTypes.array,
  selectedLevels: PropTypes.array,
  selectedManagementRoles: PropTypes.array,
  createNewJourney: PropTypes.func,
  selectedIdeation: PropTypes.object,
  handleSelectedIdeationChange: PropTypes.func,
  ideationInputValue: PropTypes.string,
  handleIdeationInputChange: PropTypes.func,
};
