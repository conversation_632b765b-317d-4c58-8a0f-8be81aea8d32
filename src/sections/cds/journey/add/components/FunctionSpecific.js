/* eslint-disable jsx-a11y/img-redundant-alt */
import { CheckCircle } from '@mui/icons-material';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Pagination,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import PropTypes from 'prop-types';
import { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { useUsecases } from 'src/apis/useUsecases';

export default function FunctionSpecific({
  data,
  selected,
  setSelected,
  inputValue,
  setInputValue,
  searchPlaceholder,
  journeyType,
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [useCaseData, setUseCaseData] = useState(data);
  const [localInputValue, setLocalInputValue] = useState(inputValue || '');
  const isInitialized = useRef(false);
  const timeoutRef = useRef(null);
  const ignoreParentUpdate = useRef(false);
  const lastLocalValue = useRef('');
  const preventResetRef = useRef(false);

  const { getUsecases } = useUsecases();

  // Kurslar için sayfa başına daha fazla öğe göster
  const ITEMS_PER_PAGE = journeyType === 'Course' ? 10 : 5;

  // Bileşen ilk yüklendiğinde
  useEffect(() => {
    if (!isInitialized.current) {
      isInitialized.current = true;

      setLocalInputValue(inputValue || '');
      lastLocalValue.current = inputValue || '';
    }

    setUseCaseData(data);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [inputValue, data, journeyType]);

  useEffect(() => {
    if (ignoreParentUpdate.current || preventResetRef.current) {
      return;
    }

    // Parent değeri anlamlı bir şekilde değiştiyse güncelle
    if (inputValue !== undefined && inputValue !== null && inputValue !== lastLocalValue.current) {
      // Yerel değer güncellemesi (sadece gerekli olduğunda)
      if (!localInputValue || inputValue.length >= localInputValue.length) {
        setLocalInputValue(inputValue);
        lastLocalValue.current = inputValue;
      }
    }
  }, [inputValue, journeyType, localInputValue]);

  // Journey tipi veya seçili öğe değiştiğinde
  useEffect(() => {
    if (typeof setSelected === 'function' && !selected && !preventResetRef.current) {
      setSelected(null);
      setLocalInputValue('');
      lastLocalValue.current = '';
      setCurrentPage(1);
    }
  }, [journeyType, setSelected, selected]);

  // Veri yapılarını hazırla
  let items = [];
  let showPagination = false;
  let paginationData = null;

  // Kurslara özel veri işleme
  if (journeyType === 'Course' && Array.isArray(data)) {
    items = data;
    // Kurslar için de pagination göster eğer yeterli sayıda öğe varsa
    showPagination = items.length > ITEMS_PER_PAGE;
  }
  // UseCase verilerini işle
  else if (useCaseData?.data?.useCases) {
    items = useCaseData.data.useCases;
    showPagination = true;
    paginationData = useCaseData.data.pagination;
  } else if (useCaseData?.data && Array.isArray(useCaseData.data)) {
    items = useCaseData.data;
  } else if (Array.isArray(useCaseData)) {
    items = useCaseData;
  }

  // Sayfalama değişikliği
  const handlePageChange = async (event, newPage) => {
    setCurrentPage(newPage);

    // Eğer UseCase tipi ise API'den yeni sayfayı getir
    if (journeyType !== 'Course' && showPagination) {
      try {
        const response = await getUsecases(newPage, localInputValue);
        if (response && response.data) {
          const useCaseData = response.data.useCases.map((usecase) => ({
            _id: usecase._id,
            title: usecase.title,
            order: usecase.usecase_order,
            slug: usecase.slug,
            function: usecase.function,
            api_type: usecase.api_type,
            function_order: usecase.function_order || {},
            icon: usecase.usecase_icon_url,
          }));

          setUseCaseData({
            data: {
              useCases: useCaseData,
              pagination: response.data.pagination,
            },
          });
        }
      } catch (err) {
        console.error('Sayfalama hatası:', err);
      }
    }
    // Course tipi için sayfa değişimi yeterli, veriler zaten filteredItems'dan geliyor
  };

  // Öğeleri filtrele
  const filteredItems = useMemo(() => {
    // Boş veri kontrolü
    if (!items || items.length === 0) {
      return [];
    }

    // Arama metni kontrolü
    const searchText = localInputValue?.toLowerCase().trim() || '';
    if (!searchText) {
      return items;
    }

    // Filtreleme işlemi
    const filtered = items.filter((item) => {
      if (!item) return false;

      // Farklı başlık formatlarını kontrol et - kurs objelerinin yapısına özel kontroller ekle
      let title = '';

      // Çeviri objelerini kontrol et
      if (item.translations) {
        if (item.translations.en && item.translations.en.title) {
          title = item.translations.en.title.toLowerCase();
        } else if (item.translations.tr && item.translations.tr.title) {
          title = item.translations.tr.title.toLowerCase();
        }
      }
      // Doğrudan başlık özelliğini kontrol et
      else if (item.title && typeof item.title === 'string') {
        title = item.title.toLowerCase();
      }
      // İsim özelliğini kontrol et
      else if (item.name && typeof item.name === 'string') {
        title = item.name.toLowerCase();
      }

      // Description/açıklama özelliğini de kontrol et
      let description = '';
      if (item.description && typeof item.description === 'string') {
        description = item.description.toLowerCase();
      } else if (item.translations?.en?.description) {
        description = item.translations.en.description.toLowerCase();
      }

      // Hem başlıkta hem de açıklamada ara
      return title.includes(searchText) || description.includes(searchText);
    });

    return filtered;
  }, [items, localInputValue]);

  // Seçili olan öğeyi (kurs veya diğer) listenin en başına getir
  const sortedItems = useMemo(() => {
    if (!filteredItems.length) return [];

    if (!selected) return filteredItems;

    return [...filteredItems].sort((a, b) => {
      if (a._id === selected._id) return -1; // Seçili öğe önce gelsin
      if (b._id === selected._id) return 1; // Diğer öğe seçili ise sonra gelsin
      return 0; // Diğer durumlarda sıralama değişmesin
    });
  }, [filteredItems, selected]);

  // Sayfalandırılmış öğeleri göster
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return sortedItems.slice(startIndex, endIndex);
  }, [sortedItems, currentPage, ITEMS_PER_PAGE]);

  // Toplam sayfa sayısı hesapla
  const totalFilteredPages = Math.ceil(sortedItems.length / ITEMS_PER_PAGE);

  // Parent'a input değerini gönder (debounce ile)
  const updateParentInputValue = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      // Parent güncellemesinde yerel değişiklikleri engelle
      ignoreParentUpdate.current = true;

      // Parent input değerini güncelle
      setInputValue(localInputValue);

      // Engellemeyi belirli bir süre sonra kaldır
      setTimeout(() => {
        ignoreParentUpdate.current = false;
      }, 100);
    }, 300);
  }, [localInputValue, setInputValue]);

  // Yerel input değeri değiştiğinde parent'a bildir
  useEffect(() => {
    if (isInitialized.current && localInputValue !== lastLocalValue.current) {
      lastLocalValue.current = localInputValue;
      updateParentInputValue();
    }
  }, []);

  // UseCase verilerini yükle (paginated API için)
  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await getUsecases(1, localInputValue);
        if (response && response.data) {
          const useCaseData = response.data.useCases.map((usecase) => ({
            _id: usecase._id,
            title: usecase.title,
            order: usecase.usecase_order,
            slug: usecase.slug,
            function: usecase.function,
            api_type: usecase.api_type,
            function_order: usecase.function_order || {},
            icon: usecase.usecase_icon_url,
          }));

          setUseCaseData({
            data: {
              useCases: useCaseData,
              pagination: response.data.pagination,
            },
          });
          setCurrentPage(1); // Sayfa numarasını sıfırla
        }
      } catch (err) {
        console.error('Veri yükleme hatası:', err);
      }
    };

    // Sadece pagination yapısı gerektiren durumlarda veri yükle
    if (showPagination && journeyType !== 'Course') {
      loadData();
    }
  }, [getUsecases, journeyType]);

  // Input değeri değişikliğini yönet - BU FONKSİYON ARAMA SORUNUNU ÇÖZÜYOR
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    // Direkt olarak yerel state'i güncelle
    setLocalInputValue(newValue);
    // Sayfa 1'e dön arama yaparken
    setCurrentPage(1);
    // Değişikliği daha sonra parent'a ilet, silme sorununu engellemek için
    // updateParentInputValue fonksiyonu debounce ile çalışacak
  };

  // Öğe seçimini yönet
  const handleItemSelect = (item) => {
    if (typeof setSelected === 'function') {
      preventResetRef.current = true;
      setSelected(item);

      // Seçim sonrası korumayı kaldır
      setTimeout(() => {
        preventResetRef.current = false;
      }, 200);
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <TextField
        fullWidth
        value={localInputValue}
        onChange={handleInputChange}
        placeholder={searchPlaceholder || 'Ara...'}
        sx={{ mb: 3 }}
        autoComplete="off"
      />

      <Grid container spacing={2}>
        {paginatedItems.length > 0 ? (
          paginatedItems.map((item) => (
            <Grid item xs={12} key={item._id}>
              <Card
                onClick={() => handleItemSelect(item)}
                sx={{
                  cursor: 'pointer',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  bgcolor: selected?._id === item._id ? 'primary.light' : 'background.paper',
                  '&:hover': {
                    bgcolor: 'primary.light',
                    color: 'white',
                  },
                }}
              >
                <CardContent
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    py: 2,
                    '&:last-child': { pb: 1 },
                  }}
                >
                  <Typography
                    variant="button"
                    component="div"
                    gutterBottom
                    sx={{ color: selected?._id === item._id ? 'white' : 'inherit' }}
                  >
                    {/* Değişik formatlardaki başlıkları destekle */}
                    {item.translations?.en?.title || item.title || item.name || 'Başlıksız'}
                  </Typography>
                  {selected?._id === item._id && (
                    <CheckCircle
                      sx={{ position: 'absolute', top: 15, right: 10, color: 'white' }}
                    />
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))
        ) : (
          <Grid item xs={12}>
            <Typography variant="body1" color="text.secondary" textAlign="center" py={3}>
              {localInputValue
                ? `"${localInputValue}" için sonuç bulunamadı`
                : 'Lütfen arama yapmak için kelime girin'}
            </Typography>
          </Grid>
        )}
      </Grid>

      {/* Pagination alanı - her iki journey tipi için de göster */}
      {sortedItems.length > ITEMS_PER_PAGE && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Stack spacing={1}>
            <Pagination
              count={totalFilteredPages}
              page={currentPage}
              onChange={handlePageChange}
              color="primary"
              showFirstButton
              showLastButton
            />
          </Stack>
        </Box>
      )}
    </Box>
  );
}

FunctionSpecific.propTypes = {
  data: PropTypes.oneOfType([
    PropTypes.array,
    PropTypes.shape({
      data: PropTypes.oneOfType([
        PropTypes.array,
        PropTypes.shape({
          useCases: PropTypes.array,
          courses: PropTypes.array,
        }),
      ]),
    }),
  ]),
  selected: PropTypes.any,
  setSelected: PropTypes.func,
  inputValue: PropTypes.string,
  setInputValue: PropTypes.func,
  emptyMessage: PropTypes.string,
  searchPlaceholder: PropTypes.string,
  journeyType: PropTypes.string,
};
