import PropTypes from 'prop-types';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
// @mui
import {
  Box,
  Card,
  Divider,
  Stack,
  Typography,
  IconButton,
  Tooltip,
  Button,
  Chip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
// components
import Iconify from 'src/components/iconify';
import Label from 'src/components/label';
import TextMaxLine from 'src/components/text-max-line';

// ----------------------------------------------------------------------

JourneyCard.propTypes = {
  journey: PropTypes.object.isRequired,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onPreview: PropTypes.func,
};

export default function JourneyCard({ journey, onEdit, onDelete, onPreview }) {
  const theme = useTheme();
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(false);

  const {
    _id,
    title_english,
    description,
    journeyType,
    levels = [],
    functions = [],
    content = [],
    buttonType,
    buttonText,
    buttonURL,
    file,
    order,
  } = journey;

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEditClick = () => {
    if (onEdit) {
      onEdit(journey);
    }
    handleMenuClose();
  };

  const handleDeleteClick = () => {
    setConfirmDelete(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = () => {
    if (onDelete) {
      onDelete(journey);
    }
    setConfirmDelete(false);
  };

  const handleDeleteCancel = () => {
    setConfirmDelete(false);
  };

  const handlePreview = () => {
    if (onPreview) {
      onPreview(journey);
    }
    handleMenuClose();
  };

  // Level'e göre renk belirleme
  const getLevelColor = (level) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'success';
      case 'expert':
        return 'warning';
      case 'master':
        return 'error';
      default:
        return 'default';
    }
  };

  // Journey tipine göre ikon belirleme
  const getJourneyTypeIcon = (type) => {
    switch (type) {
      case 'Content':
        return 'mdi:file-document-outline';
      case 'Function Specific Module':
        return 'mdi:function-variant';
      case 'Learning Path':
        return 'mdi:school-outline';
      default:
        return 'mdi:shape-outline';
    }
  };

  return (
    <Card
      sx={{
        '&:hover': {
          boxShadow: theme.customShadows.z16,
          transition: theme.transitions.create('box-shadow', {
            duration: theme.transitions.duration.shorter,
          }),
        },
      }}
    >
      <Stack spacing={2} sx={{ p: 3 }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box
            sx={{
              width: 40,
              height: 40,
              flexShrink: 0,
              display: 'flex',
              borderRadius: 1,
              alignItems: 'center',
              justifyContent: 'center',
              color: 'primary.main',
              bgcolor: (theme) => alpha(theme.palette.primary.main, 0.08),
            }}
          >
            <Iconify icon={getJourneyTypeIcon(journeyType)} width={24} />
          </Box>

          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <TextMaxLine variant="subtitle1" line={1}>
                {title_english}
              </TextMaxLine>

              <Label color="primary" sx={{ ml: 1 }}>
                {`Order: ${order}`}
              </Label>
            </Stack>

            <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 0.5 }}>
              {levels.map((level) => (
                <Chip
                  key={level}
                  label={level.charAt(0).toUpperCase() + level.slice(1)}
                  size="small"
                  color={getLevelColor(level)}
                />
              ))}
            </Stack>
          </Box>

          <Box>
            <Tooltip title="Actions">
              <IconButton
                size="small"
                color="primary"
                onClick={handleMenuOpen}
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: (theme) => alpha(theme.palette.primary.main, 0.08),
                  '&:hover': {
                    bgcolor: (theme) => alpha(theme.palette.primary.main, 0.16),
                  },
                }}
              >
                <Iconify icon="eva:more-vertical-fill" />
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              PaperProps={{
                sx: { width: 200, maxWidth: '100%' },
              }}
            >
              <MenuItem onClick={handleEditClick}>
                <Iconify icon="eva:edit-fill" sx={{ mr: 2 }} />
                Edit
              </MenuItem>

              <Divider sx={{ borderStyle: 'dashed' }} />
              {/* <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>
                <Iconify icon="eva:trash-2-outline" sx={{ mr: 2 }} />
                Delete
              </MenuItem> */}
            </Menu>
          </Box>
        </Stack>

        <Stack spacing={1}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {description || 'No description'}
          </Typography>
        </Stack>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <Stack direction="row" flexWrap="wrap" alignItems="center">
          <Stack direction="row" spacing={1} flexGrow={1} alignItems="center">
            <Iconify icon="clarity:blocks-group-line" width={16} />
            <Typography variant="caption" sx={{ color: 'text.disabled', flexGrow: 1 }}>
              {journeyType}
            </Typography>
          </Stack>

          <Stack direction="row" spacing={1} alignItems="center">
            <Iconify icon="fluent:document-bullet-list-16-regular" width={16} />
            <Typography variant="caption" sx={{ color: 'text.disabled' }}>
              {`${content?.length || 0} content`}
            </Typography>
          </Stack>
        </Stack>
      </Stack>

      {/* Onay dialogu */}
      <Dialog open={confirmDelete} onClose={handleDeleteCancel}>
        <DialogTitle>Delete Journey</DialogTitle>
        <DialogContent>
          <DialogContentText>
            "{title_english}" Are you sure you want to delete this journey? This action cannot be
            undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="inherit">
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
}
