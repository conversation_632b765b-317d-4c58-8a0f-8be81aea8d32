/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Autocomplete,
  Box,
  Button,
  Card,
  Checkbox,
  Chip,
  CircularProgress,
  Container,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  Popover,
  Stack,
  Switch,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { ReactSortable } from 'react-sortablejs';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// İkonlar
import Iconify from 'src/components/iconify';
import EmptyContent from 'src/components/empty-content';
import { useSettingsContext } from 'src/components/settings';

// API'ler
import { useJourneys } from 'src/apis/useJourneys';
import { useOnboarding } from 'src/apis/useOnboarding';

// Bileşenler
import JourneyCard from './JourneyCard';
import ConfirmDialog from 'src/components/confirm-dialog';

// ----------------------------------------------------------------------

export default function AllJourneysView() {
  const theme = useTheme();
  const settings = useSettingsContext();
  const navigate = useNavigate(); // React Router hook

  const { getJourneys, updateJourneyOrder, deleteJourney } = useJourneys();

  const { getIndustry, getFunctions, getLevels, getManagementRoles, functions } = useOnboarding();

  // State tanımlamaları
  const [beginnerJourneys, setBeginnerJourneys] = useState([]);
  const [expertJourneys, setExpertJourneys] = useState([]);
  const [masterJourneys, setMasterJourneys] = useState([]);

  const [selectedFunc, setSelectedFunc] = useState('');
  const [funcInputValue, setFuncInputValue] = useState('');

  // Provider için state tanımlamaları
  const [selectedProvider, setSelectedProvider] = useState('ai-business-school');
  const [providerOptions] = useState([
    { label: 'AI Business School', value: 'ai-business-school' },
    { label: 'Microsoft', value: 'microsoft' },
  ]);

  const [leadership, setLeadership] = useState(false);

  const [loading, setLoading] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);

  const [searchValue, setSearchValue] = useState('');
  const [filterBy, setFilterBy] = useState('ai-business-school');

  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    content: '',
    action: null,
  });

  const [viewMode, setViewMode] = useState('list'); // 'card' veya 'list'
  const [currentTab, setCurrentTab] = useState('beginner'); // 'beginner', 'expert', 'master'

  // Journey verileri yükleme
  useEffect(() => {
    getIndustry();
    getFunctions();
    getLevels();
    getManagementRoles();
  }, []);

  const handleChangeLeadership = (e) => {
    setLeadership(e.target.checked);
  };

  // API'den seçili fonksiyon için journeyleri getir
  const fetchJourneys = useCallback(
    (selectedFunction) => {
      if (!selectedFunction) return;

      setLoading(true);

      // Aktif tab için level değerini belirle
      const level = currentTab;

      getJourneys(
        { slug: selectedFunction?.slug, function: selectedFunction?.slug },
        selectedProvider,
        leadership ? 'yes' : undefined,
        level
      )
        .then((res) => {
          if (!res) return;

          // API'den gelen veriyi arama terimlerine göre filtrele
          const filteredJourneys = res.filter((journey) => {
            const searchLower = searchValue.toLowerCase();
            return journey.title_english.toLowerCase().includes(searchLower);
          });

          // Level'a göre set et
          switch (level) {
            case 'beginner':
              setBeginnerJourneys(filteredJourneys.sort((a, b) => a.order - b.order));
              break;
            case 'expert':
              setExpertJourneys(filteredJourneys.sort((a, b) => a.order - b.order));
              break;
            case 'master':
              setMasterJourneys(filteredJourneys.sort((a, b) => a.order - b.order));
              break;
            default:
              break;
          }
        })
        .catch((error) => {
          console.error('An error occurred while loading the journeys:', error);
          toast.error('An error occurred while loading the journeys');
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [getJourneys, selectedProvider, leadership, currentTab, searchValue]
  );

  // Aramalarda journeyleri filtrele
  useEffect(() => {
    if (selectedFunc) {
      fetchJourneys(selectedFunc);
    }
  }, [searchValue, filterBy, selectedFunc, selectedProvider, leadership, currentTab]);

  // Sıralama işlemini yönet
  const handleSortEnd = useCallback((updatedList, setJourneys) => {
    const reorderedList = updatedList.map((item, index) => ({
      ...item,
      order: index + 1,
    }));
    setJourneys(reorderedList);
  }, []);

  // Journey sırasını güncelle
  const handleUpdateJourneys = useCallback(
    (journeys, level) => {
      if (!journeys || journeys.length === 0) {
        toast.warning(`${level} level journeys to be updated not found`);
        return;
      }

      setUpdateLoading(true);

      updateJourneyOrder(journeys)
        .then((response) => {
          if (response.status === 'success') {
            toast.success(`${level} journeys successfully updated`);
            fetchJourneys(selectedFunc);
          } else {
            toast.error(`${level} journeys update error occurred`);
          }
        })
        .catch((error) => {
          console.error(`${level} journeys update error occurred:`, error);
          toast.error('An error occurred while updating the journeys');
        })
        .finally(() => {
          setUpdateLoading(false);
        });
    },
    [updateJourneyOrder, fetchJourneys, selectedFunc]
  );

  // Journey düzenleme
  const handleEditJourney = useCallback(
    (journey) => {
      console.log('Starting to edit journey:', journey._id);
      // Doğrudan düzenleme sayfasına yönlendir
      navigate(`/cds/journey/edit/${journey._id}`);
    },
    [navigate]
  );

  // Journey önizleme
  const handlePreviewJourney = (journey) => {
    toast.info(`Previewing "${journey.title_english}"...`);
  };

  // Journey silme onayı
  const handleDeleteConfirm = (journey) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Journey',
      content: `"${journey.title_english}" Are you sure you want to delete this journey? This action cannot be undone.`,
      action: () => handleDeleteJourney(journey._id),
    });
  };

  // Journey silme işlemi
  const handleDeleteJourney = (journeyId) => {
    deleteJourney(journeyId)
      .then((response) => {
        if (response.status === 'success') {
          toast.success('Journey successfully deleted');
          fetchJourneys(selectedFunc);
        } else {
          toast.error('An error occurred while deleting the journey');
        }
      })
      .catch((error) => {
        console.error('An error occurred while deleting the journey:', error);
        toast.error('An error occurred while deleting the journey');
      });
  };

  // Tab değişikliği
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    // Tab değiştiğinde journeyleri yeniden yükle
    if (selectedFunc) {
      fetchJourneys(selectedFunc);
    }
  };

  // Görünüm modu değişikliği
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  // Kart görünümü
  const renderCardView = () => {
    let journeys = [];
    let setJourneys = null;
    let level = '';

    switch (currentTab) {
      case 'beginner':
        journeys = beginnerJourneys;
        setJourneys = setBeginnerJourneys;
        level = 'Beginner';
        break;
      case 'expert':
        journeys = expertJourneys;
        setJourneys = setExpertJourneys;
        level = 'Expert';
        break;
      case 'master':
        journeys = masterJourneys;
        setJourneys = setMasterJourneys;
        level = 'Master';
        break;
      default:
        journeys = [];
    }

    return (
      <>
        {loading ? (
          <Box
            sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}
          >
            <CircularProgress />
          </Box>
        ) : journeys.length > 0 ? (
          <>
            <Box sx={{ mb: 3 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => handleUpdateJourneys(journeys, level)}
                disabled={updateLoading}
                startIcon={
                  updateLoading ? (
                    <CircularProgress size={20} />
                  ) : (
                    <Iconify icon="eva:save-outline" />
                  )
                }
              >
                {updateLoading ? 'Updating...' : 'Update Order'}
              </Button>
            </Box>

            <Grid container spacing={3}>
              <ReactSortable
                list={journeys}
                setList={(list) => handleSortEnd(list, setJourneys)}
                className="journey-grid-container"
                style={{ display: 'flex', flexWrap: 'wrap', width: '100%' }}
              >
                {journeys.map((journey) => (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    key={journey._id}
                    style={{ width: '33.33%', padding: '12px' }}
                  >
                    <JourneyCard
                      journey={journey}
                      onEdit={handleEditJourney}
                      onDelete={handleDeleteConfirm}
                      onPreview={handlePreviewJourney}
                    />
                  </Grid>
                ))}
              </ReactSortable>
            </Grid>
          </>
        ) : (
          <EmptyContent
            title="No Journey Found"
            description={
              selectedFunc
                ? 'No journeys have been added for this level or no journeys were found in the search results'
                : 'Please select a function'
            }
            sx={{ py: 10 }}
          />
        )}
      </>
    );
  };

  // Liste görünümü
  const renderListView = () => {
    let journeys = [];
    let setJourneys = null;
    let level = '';

    switch (currentTab) {
      case 'beginner':
        journeys = beginnerJourneys;
        setJourneys = setBeginnerJourneys;
        level = 'Beginner';
        break;
      case 'expert':
        journeys = expertJourneys;
        setJourneys = setExpertJourneys;
        level = 'Expert';
        break;
      case 'master':
        journeys = masterJourneys;
        setJourneys = setMasterJourneys;
        level = 'Master';
        break;
      default:
        journeys = [];
    }

    return (
      <>
        {loading ? (
          <Box
            sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}
          >
            <CircularProgress />
          </Box>
        ) : journeys.length > 0 ? (
          <>
            <Box sx={{ mb: 3 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => handleUpdateJourneys(journeys, level)}
                disabled={updateLoading}
                startIcon={
                  updateLoading ? (
                    <CircularProgress size={20} />
                  ) : (
                    <Iconify icon="eva:save-outline" />
                  )
                }
              >
                {updateLoading ? 'Updating...' : 'Update Order'}
              </Button>
            </Box>

            <Card>
              <ReactSortable list={journeys} setList={(list) => handleSortEnd(list, setJourneys)}>
                {journeys.map((journey, index) => (
                  <Stack
                    key={journey._id}
                    direction="row"
                    alignItems="center"
                    sx={{
                      p: 2,
                      borderBottom:
                        index !== journeys.length - 1
                          ? `1px dashed ${theme.palette.divider}`
                          : 'none',
                      cursor: 'grab',
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.lighter, 0.08),
                      },
                    }}
                  >
                    <Box
                      sx={{
                        width: 32,
                        height: 32,
                        mr: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '50%',
                        bgcolor: alpha(theme.palette.primary.main, 0.08),
                      }}
                    >
                      <Typography variant="subtitle2" sx={{ color: 'primary.main' }}>
                        {journey.order}
                      </Typography>
                    </Box>

                    <Box sx={{ minWidth: 0, flexGrow: 1 }}>
                      <Typography variant="subtitle2" noWrap>
                        {journey.title_english}
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'text.secondary' }} noWrap>
                        {journey.journeyType}
                      </Typography>
                    </Box>

                    <Stack direction="row" spacing={1} alignItems="center">
                      {journey.levels.map((level) => (
                        <Chip
                          key={level}
                          label={level.charAt(0).toUpperCase() + level.slice(1)}
                          size="small"
                          color={
                            level.toLowerCase() === 'beginner'
                              ? 'success'
                              : level.toLowerCase() === 'expert'
                                ? 'warning'
                                : 'error'
                          }
                        />
                      ))}

                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditJourney(journey);
                          }}
                        >
                          <Iconify icon="eva:edit-outline" />
                        </IconButton>
                      </Tooltip>

                      {/* <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteConfirm(journey);
                          }}
                        >
                          <Iconify icon="eva:trash-2-outline" />
                        </IconButton>
                      </Tooltip> */}
                    </Stack>
                  </Stack>
                ))}
              </ReactSortable>
            </Card>
          </>
        ) : (
          <EmptyContent
            title="No Journey Found"
            description={
              selectedFunc
                ? 'No journeys have been added for this level or no journeys were found in the search results'
                : 'Please select a function'
            }
            sx={{ py: 10 }}
          />
        )}
      </>
    );
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <ToastContainer position="top-right" autoClose={3000} />

      <Box sx={{ mb: 5 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={2}>
          <Typography variant="h4">Journey Management</Typography>

          <Button
            variant="contained"
            color="primary"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={() => navigate('/cds/journey/add')}
          >
            Add New Journey
          </Button>
        </Stack>
      </Box>

      <Card sx={{ p: 3, mb: 3 }}>
        <Stack spacing={2}>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            alignItems={{ sm: 'center' }}
            justifyContent="space-between"
            spacing={2}
          >
            <Autocomplete
              fullWidth
              value={selectedFunc}
              onChange={(event, newValue) => {
                setSelectedFunc(newValue);
                fetchJourneys(newValue);
              }}
              inputValue={funcInputValue}
              onInputChange={(event, newInputValue) => setFuncInputValue(newInputValue)}
              id="functions"
              options={functions || []}
              getOptionLabel={(option) => option.translations?.en || ''}
              isOptionEqualToValue={(option, value) => option._id === value?._id}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Function"
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <>
                        <InputAdornment position="start">
                          <Iconify icon="eva:funnel-fill" width={24} />
                        </InputAdornment>
                        {params.InputProps.startAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />

            <TextField
              select
              fullWidth
              label="Select Provider"
              value={selectedProvider}
              onChange={(event) => {
                setSelectedProvider(event.target.value);
                if (selectedFunc) fetchJourneys(selectedFunc);
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:briefcase-outline" width={24} />
                  </InputAdornment>
                ),
              }}
            >
              {providerOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
            <FormControlLabel
              sx={{
                border: '1px solid #e0e0e0',
                height: 55,
                width: '50%',
                marginLeft: 2,
                borderRadius: 1,
              }}
              control={<Switch checked={leadership} onChange={handleChangeLeadership} />}
              label="Leadership"
            />
            <TextField
              fullWidth
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              placeholder="Search Journey..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:search-fill" width={24} />
                  </InputAdornment>
                ),
                endAdornment: searchValue && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setSearchValue('')} edge="end">
                      <Iconify icon="eva:close-fill" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Stack>
        </Stack>
      </Card>

      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ px: 2 }}>
            <Tabs value={currentTab} onChange={handleTabChange} textColor="primary">
              <Tab
                value="beginner"
                label={
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Iconify icon="mdi:chart-timeline-variant" width={20} />
                    <Box>Beginner ({beginnerJourneys.length})</Box>
                  </Stack>
                }
              />
              <Tab
                value="expert"
                label={
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Iconify icon="mdi:chart-bell-curve-cumulative" width={20} />
                    <Box>Expert ({expertJourneys.length})</Box>
                  </Stack>
                }
              />
              <Tab
                value="master"
                label={
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Iconify icon="mdi:chart-pyramid" width={20} />
                    <Box>Master ({masterJourneys.length})</Box>
                  </Stack>
                }
              />
            </Tabs>

            <Stack direction="row" spacing={1}>
              <Tooltip title="Card View">
                <IconButton
                  color={viewMode === 'card' ? 'primary' : 'default'}
                  onClick={() => handleViewModeChange('card')}
                >
                  <Iconify icon="mdi:view-grid-outline" width={20} />
                </IconButton>
              </Tooltip>
              <Tooltip title="List View">
                <IconButton
                  color={viewMode === 'list' ? 'primary' : 'default'}
                  onClick={() => handleViewModeChange('list')}
                >
                  <Iconify icon="mdi:view-list-outline" width={20} />
                </IconButton>
              </Tooltip>
            </Stack>
          </Stack>
        </Box>

        <Box sx={{ p: 3 }}>{viewMode === 'card' ? renderCardView() : renderListView()}</Box>
      </Card>

      <ConfirmDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        content={confirmDialog.content}
        action={confirmDialog.action}
        onClose={() =>
          setConfirmDialog({
            open: false,
            title: '',
            content: '',
            action: null,
          })
        }
      />
    </Container>
  );
}
