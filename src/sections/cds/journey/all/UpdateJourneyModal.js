/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useNavigate, useParams } from 'react-router-dom';
// @mui
import {
  Box,
  Button,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
  Tab,
  Tabs,
  Stepper,
  Step,
  StepLabel,
  Paper,
  CircularProgress,
  Divider,
  Card,
  Container,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  ContentCopy,
  Save,
  Translate,
  ChevronRight,
  ChevronLeft,
  ArrowBack,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

// hooks
import { useCourses } from 'src/apis';
import { useAITranslator } from 'src/apis/useAITranslator';
import { useIdeations } from 'src/apis/useIdeations';
import { useJourneys } from 'src/apis/useJourneys';
import { useUsecases } from 'src/apis/useUsecases';

// components
import TranslateConfirmationModal from 'src/components/TranslateConfirmationModal';
import { buttonTypes, journeyTypes } from 'src/utils/Constants';
import { CheckboxArea, Content, JourneyType } from '../add/components';

import { toast } from 'react-toastify';
import { useSettingsContext } from 'src/components/settings';
import { LoadingScreen } from 'src/components/loading-screen';
import { useOnboarding } from 'src/apis/useOnboarding';

// ----------------------------------------------------------------------

// Step definitions
const STEPS = [
  {
    label: 'Basic Information',
    description: 'Enter basic information for the journey',
  },
  {
    label: 'Content',
    description: 'Edit the content of the journey',
  },
  {
    label: 'Targeting',
    description: 'Select the target audience for the journey',
  },
];

// ----------------------------------------------------------------------

export default function UpdateJourneyView() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { journeyId } = useParams(); // Get journey ID from URL
  const settings = useSettingsContext();

  const { editJourney, getJourneyDetails, journeyDetails } = useJourneys();
  const { getUsecases, usecases } = useUsecases();
  const { getIdeations, ideations } = useIdeations();
  const { getCourses, courses = [] } = useCourses();
  const { getPlatformLanguages, translateJourney, platformLanguages } = useAITranslator();

  const {
    getIndustry,
    getFunctions,
    getLevels,
    getManagementRoles,
    industry,
    functions,
    levels,
    managementRoles,
  } = useOnboarding();

  // Active step
  const [activeStep, setActiveStep] = useState(0);

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [initialLoadDone, setInitialLoadDone] = useState(false);

  // Form values
  const [formFields, setFormFields] = useState({
    // Basic information
    id: '',
    title: '',
    description: '',
    journeyType: '',
    buttonType: 'URL',
    buttonText: '',
    buttonURL: '',
    isNewTab: false,
    isClosedCard: false,
    progressPercent: 0,
    thumbnailUrl: '',

    // Content
    content: [''],
    editors: [{ id: 'editor-0' }],

    // Related content
    relatedUsecase: null,
    relatedIdeation: null,
    relatedCourse: null,

    // Targeting
    targetAreas: [],
    targetFunctions: [],
    targetLevels: [],
    targetRoles: [],
  });

  // AutoComplete değerleri
  const [inputValues, setInputValues] = useState({
    usecase: '',
    ideation: '',
    course: '',
  });

  // Provider için state tanımlamaları
  const [selectedProvider, setSelectedProvider] = useState('ai-business-school');
  const [providerInputValue, setProviderInputValue] = useState('');
  const [providerOptions] = useState([
    { label: 'AI Business School', value: 'ai-business-school' },
    { label: 'Microsoft', value: 'microsoft' },
  ]);

  // İlişkili içerik seçimleri
  const [selectedItems, setSelectedItems] = useState({
    usecase: null,
    ideation: null,
    course: null,
    journeyType: null,
    buttonType: 'URL',
  });

  // Çeviriler
  const [currentLanguage, setCurrentLanguage] = useState('english');
  const [translations, setTranslations] = useState({
    english: {
      title: '',
      description: '',
      buttonText: '',
      content: [''],
    },
  });
  const [translateModalOpen, setTranslateModalOpen] = useState(false);

  // -------------------------------------------------------
  // 1. Data Loading Operations
  // -------------------------------------------------------

  // Load basic data
  useEffect(() => {
    if (!journeyId) return;

    setIsFetching(true);

    // Load basic data
    Promise.all([
      getPlatformLanguages(),
      getUsecases(1, '', '', '', 1000),
      getIdeations(),
      getCourses(),
      getIndustry(),
      getFunctions(),
      getLevels(),
      getManagementRoles(),
      getJourneyDetails(journeyId),
    ])
      .then(() => {
        setInitialLoadDone(true);
      })
      .catch(() => {
        toast.error('An error occurred while loading the required data');
      })
      .finally(() => {
        setIsFetching(false);
      });
  }, [journeyId]);

  // Journey detayları geldiğinde form alanlarını doldur
  useEffect(() => {
    if (journeyDetails?.data && initialLoadDone) {
      const journeyData = journeyDetails.data;

      // Process selected usecase data - handle both object and string ID formats
      let selectedUsecaseData = null;
      if (journeyData.selectedFunctionSpecUseCases) {
        // If it's an object with _id, use it directly
        if (
          typeof journeyData.selectedFunctionSpecUseCases === 'object' &&
          journeyData.selectedFunctionSpecUseCases._id
        ) {
          selectedUsecaseData = journeyData.selectedFunctionSpecUseCases;
        }
        // If it's a string ID, find the matching usecase from usecases list
        else if (usecases?.data?.useCases) {
          const usecaseId =
            typeof journeyData.selectedFunctionSpecUseCases === 'string'
              ? journeyData.selectedFunctionSpecUseCases
              : journeyData.selectedFunctionSpecUseCases?._id;

          if (usecaseId) {
            selectedUsecaseData = usecases.data.useCases.find((u) => u._id === usecaseId) || {
              _id: usecaseId,
            }; // Fallback to at least have the ID
          }
        }
      }

      // Process course data - handle both object and string ID formats
      let selectedCourseData = null;
      if (journeyData.course) {
        // If it's an object with _id, use it directly
        if (typeof journeyData.course === 'object' && journeyData.course._id) {
          selectedCourseData = journeyData.course;
        }
        // If it's a string ID, first try to find the matching course from courses list
        // If not found, at least create an object with the ID so it can be selected
        else {
          const courseId =
            typeof journeyData.course === 'string' ? journeyData.course : journeyData.course?._id;

          if (courseId) {
            // If courses list is loaded, try to find the matching course
            if (courses && Array.isArray(courses)) {
              const foundCourse = courses.find((c) => c._id === courseId);
              selectedCourseData = foundCourse || { _id: courseId }; // Use found course or just the ID
            } else {
              // If courses list is not loaded yet, create a minimal object with just the ID
              selectedCourseData = { _id: courseId };
            }
          }
        }
      }

      // Process ideation data - handle both object and string ID formats
      let selectedIdeationData = null;
      if (journeyData.ideation) {
        // If it's an object with _id, use it directly
        if (typeof journeyData.ideation === 'object' && journeyData.ideation._id) {
          selectedIdeationData = journeyData.ideation;
        }
        // If it's a string ID, find the matching ideation from ideations list
        else if (ideations) {
          const ideationId =
            typeof journeyData.ideation === 'string'
              ? journeyData.ideation
              : journeyData.ideation?._id;

          if (ideationId) {
            selectedIdeationData = ideations.find((i) => i._id === ideationId) || {
              _id: ideationId,
            }; // Fallback to at least have the ID
          }
        }
      }

      // Form alanlarını doldur
      setFormFields({
        id: journeyData._id || '',
        title: journeyData.title_english || '',
        description: journeyData.description || '',
        journeyType: journeyData.journeyType || '',
        buttonType: journeyData.buttonType || 'URL',
        buttonText: journeyData.buttonText || '',
        buttonURL: journeyData.buttonURL || '',
        isNewTab: journeyData.newTab || false,
        isClosedCard: journeyData.closedCard || false,
        progressPercent: journeyData.percent || 0,
        thumbnailUrl: journeyData.file || '',
        content: journeyData.content || [''],
        editors: journeyData.content?.map((_, index) => ({ id: `editor-${index}` })) || [
          { id: 'editor-0' },
        ],
        relatedUsecase: selectedUsecaseData,
        relatedIdeation: selectedIdeationData,
        relatedCourse: selectedCourseData,
        targetAreas: journeyData.areaOfInterest || [],
        targetFunctions: journeyData.functions || [],
        targetLevels: journeyData.levels || [],
        targetRoles: journeyData.managementRole || [],
      });

      // Seçili öğeleri ayarla
      setSelectedItems({
        usecase: selectedUsecaseData,
        ideation: selectedIdeationData,
        course: selectedCourseData,
        journeyType: journeyData.journeyType,
        buttonType: journeyData.buttonType || 'URL',
      });

      // Provider'ı ayarla
      if (journeyData.provider) {
        setSelectedProvider(journeyData.provider);
      }

      // Çevirileri hazırla
      const initialTranslations = {
        english: {
          title: journeyData.title_english || '',
          description: journeyData.description || '',
          buttonText: journeyData.buttonText || '',
          content: journeyData.content || [''],
        },
      };

      if (journeyData.translations) {
        Object.keys(journeyData.translations).forEach((lang) => {
          initialTranslations[lang] = {
            title: journeyData.translations[lang].title || '',
            description: journeyData.translations[lang].description || '',
            buttonText: journeyData.translations[lang].buttonText || '',
            content: journeyData.translations[lang].content || journeyData.content || [''],
          };
        });
      }

      setTranslations(initialTranslations);

      // İlişkili öğeler için input değerlerini ayarla
      if (selectedUsecaseData && usecases?.data?.useCases) {
        const usecase = usecases.data.useCases.find(
          (u) => u._id === (selectedUsecaseData._id || selectedUsecaseData)
        );
        if (usecase) {
          setInputValues((prev) => ({ ...prev, usecase: usecase.name || '' }));
        }
      }

      if (selectedIdeationData && ideations) {
        const ideation = ideations.find(
          (i) => i._id === (selectedIdeationData._id || selectedIdeationData)
        );
        if (ideation) {
          setInputValues((prev) => ({ ...prev, ideation: ideation.name || '' }));
        }
      }

      if (selectedCourseData && courses) {
        const course = courses.find(
          (c) => c._id === (selectedCourseData._id || selectedCourseData)
        );
        if (course) {
          setInputValues((prev) => ({ ...prev, course: course.name || '' }));
        }
      }
    }
  }, [journeyDetails, initialLoadDone, usecases, ideations, courses]);

  // Ideations, Usecases veya Courses sonradan yüklendiğinde, seçili öğeleri güncelle
  useEffect(() => {
    if (
      selectedItems.journeyType === 'Course' &&
      selectedItems.course &&
      selectedItems.course._id &&
      courses &&
      Array.isArray(courses)
    ) {
      // Seçili kursun sadece ID'si varsa, courses listesinden tam veriyi bul
      if (Object.keys(selectedItems.course).length === 1 && selectedItems.course._id) {
        const fullCourseData = courses.find((c) => c._id === selectedItems.course._id);

        if (fullCourseData) {
          // Tam kurs verisi bulunduğunda, seçili öğeleri ve form alanlarını güncelle
          setSelectedItems((prev) => ({ ...prev, course: fullCourseData }));
          setFormFields((prev) => ({ ...prev, relatedCourse: fullCourseData }));

          // İnput değerini de güncelle (arama alanı için)
          const courseName = fullCourseData.translations?.en?.title || fullCourseData.title;
          if (courseName) {
            setInputValues((prev) => ({ ...prev, course: courseName }));
          }
        }
      }
    }
  }, [courses, selectedItems.journeyType, selectedItems.course]);

  // -------------------------------------------------------
  // 2. Event Handlers
  // -------------------------------------------------------

  // Navigate to next step
  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  // Go back to previous step
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Form alanı değişikliğini yönet
  const handleFormChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;

    // Form değerlerini güncelle
    setFormFields((prev) => ({
      ...prev,
      [field]: value,
    }));

    // İngilizce çeviriyi de güncelle
    if (['title', 'description', 'buttonText'].includes(field)) {
      setTranslations((prev) => ({
        ...prev,
        english: {
          ...prev.english,
          [field === 'title' ? 'title' : field]: value,
        },
      }));
    }
  };

  // Çeviri değişikliğini yönet
  const handleTranslationChange = (field) => (event) => {
    const value = event.target.value;

    setTranslations((prev) => ({
      ...prev,
      [currentLanguage]: {
        ...prev[currentLanguage],
        [field]: value,
      },
    }));
  };

  // Checkbox grup değişikliğini yönet
  const handleCheckboxGroupChange = (field) => (event) => {
    const value = event.target.name;

    setFormFields((prev) => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter((item) => item !== value)
        : [...prev[field], value],
    }));
  };

  // Editor ekleme
  const handleAddEditor = useCallback(() => {
    setFormFields((prev) => ({
      ...prev,
      editors: [...prev.editors, { id: `editor-${prev.editors.length}` }],
      content: [...prev.content, ''],
    }));
  }, []);

  // Editor içerik değişikliği
  const handleEditorChange = useCallback(
    (editorId, value) => {
      const editorIndex = formFields.editors.findIndex((editor) => editor.id === editorId);
      if (editorIndex === -1) return;

      // Her dil için kendi içeriğini ayrı ayrı saklayalım
      if (currentLanguage === 'english') {
        // İngilizce içerik primary content olarak kaydedilir
        setFormFields((prev) => {
          const newContent = [...prev.content];
          newContent[editorIndex] = value;
          return {
            ...prev,
            content: newContent,
          };
        });
      }

      // Her dil için çevirileri ayrı ayrı güncelle
      setTranslations((prev) => {
        const updatedTranslations = { ...prev };

        // Mevcut dil için içeriği güncelle
        if (!updatedTranslations[currentLanguage]) {
          updatedTranslations[currentLanguage] = {
            title: '',
            description: '',
            buttonText: '',
            content: [''],
          };
        }

        const currentLangContent = [...(updatedTranslations[currentLanguage].content || [''])];
        // Eğer dizide bu indeks yoksa genişlet
        while (currentLangContent.length <= editorIndex) {
          currentLangContent.push('');
        }

        currentLangContent[editorIndex] = value;

        return {
          ...updatedTranslations,
          [currentLanguage]: {
            ...updatedTranslations[currentLanguage],
            content: currentLangContent,
          },
        };
      });
    },
    [currentLanguage, formFields.editors]
  );

  // Editor silme
  const handleRemoveEditor = useCallback(
    (editorId) => {
      const editorIndex = formFields.editors.findIndex((editor) => editor.id === editorId);
      if (editorIndex === -1) return;

      setFormFields((prev) => ({
        ...prev,
        editors: prev.editors.filter((editor) => editor.id !== editorId),
        content: prev.content.filter((_, index) => index !== editorIndex),
      }));

      // Çevirilerdeki içerikleri de güncelle
      setTranslations((prev) => {
        const updatedTranslations = { ...prev };

        Object.keys(updatedTranslations).forEach((lang) => {
          if (updatedTranslations[lang].content) {
            updatedTranslations[lang].content = updatedTranslations[lang].content.filter(
              (_, index) => index !== editorIndex
            );
          }
        });

        return updatedTranslations;
      });
    },
    [formFields.editors]
  );

  // İlgili içerik tipini değiştirme
  const handleJourneyTypeChange = (newValue) => {
    setSelectedItems((prev) => ({
      ...prev,
      journeyType: newValue,
      usecase: null,
      ideation: null,
      course: null,
    }));

    setInputValues((prev) => ({
      ...prev,
      usecase: '',
      ideation: '',
      course: '',
    }));

    setFormFields((prev) => ({
      ...prev,
      journeyType: newValue,
      relatedUsecase: null,
      relatedIdeation: null,
      relatedCourse: null,
    }));
  };

  // Düğme tipini değiştirme
  const handleButtonTypeChange = (newValue) => {
    setSelectedItems((prev) => ({
      ...prev,
      buttonType: newValue,
    }));

    setFormFields((prev) => ({
      ...prev,
      buttonType: newValue,
    }));
  };

  // Dosya seçimi
  const handleFileDrop = (url) => {
    setFormFields((prev) => ({
      ...prev,
      thumbnailUrl: url,
    }));
  };

  // UUID kopyalama
  const handleCopyUUID = () => {
    navigator.clipboard
      .writeText(formFields.id || '')
      .then(() => {
        toast.success('UUID copied to clipboard');
      })
      .catch(() => {
        toast.error('UUID copy failed');
      });
  };

  // Çeviriyi başlat
  const handleTranslateClick = () => {
    if (!formFields.title.trim()) {
      toast.error('Please fill in the English title first');
      return;
    }
    setTranslateModalOpen(true);
  };

  // Çeviriyi onayla
  const handleConfirmTranslate = () => {
    const journey = {
      title_english: formFields.title,
      description: formFields.description,
      buttonText: formFields.buttonText,
      content: formFields.content,
    };

    const targetLanguage = currentLanguage;
    toast.info(`Starting translation to ${targetLanguage}...`);

    translateJourney(
      journey,
      'en',
      platformLanguages.find((l) => l.name.toLowerCase() === targetLanguage)?.lang
    )
      .then((res) => {
        if (res) {
          setTranslations((prev) => ({
            ...prev,
            [targetLanguage]: {
              title: res.message.title || res.message.title_english || '',
              description: res.message.description || '',
              buttonText: res.message.buttonText || '',
              content: Array.isArray(res.message.content)
                ? res.message.content
                : [res.message.content || ''],
            },
          }));
          toast.success(`${targetLanguage} translation successful`);
        }
      })
      .catch((error) => {
        console.error('Translation error:', error);
        toast.error(`${targetLanguage} translation failed`);
      });

    setTranslateModalOpen(false);
  };

  // Sayfadan çıkış
  const handleCancel = () => {
    navigate('/cds/journey/all');
  };

  // Güncellemeleri kaydet
  const handleSaveJourney = () => {
    if (!formFields.id) {
      toast.error('Journey ID not found');
      return;
    }

    setIsLoading(true);

    // Journey data oluştur
    let journeyData = {
      _id: formFields.id,
      title_english: formFields.title,
      description: formFields.description,
      journeyType: selectedItems.journeyType,
      buttonType: selectedItems.buttonType,
      buttonText: formFields.buttonText,
      buttonURL: formFields.buttonURL,
      newTab: formFields.isNewTab,
      closedCard: formFields.isClosedCard,
      percent: formFields.progressPercent,
      file: formFields.thumbnailUrl,
      content: formFields.content,
      translations: translations,
      areaOfInterest: formFields.targetAreas,
      functions: formFields.targetFunctions,
      levels: formFields.targetLevels,
      managementRole: formFields.targetRoles,
      provider: selectedProvider,
    };

    // Journey tipine göre ilgili alanları ayarla
    switch (selectedItems.journeyType) {
      case 'Usecases':
        journeyData = {
          ...journeyData,
          selectedFunctionSpecUseCases: selectedItems.usecase?._id || selectedItems.usecase || null,
          course: null,
          ideation: null,
        };
        break;
      case 'Ideation Project':
        journeyData = {
          ...journeyData,
          ideation: selectedItems.ideation?._id || selectedItems.ideation || null,
          selectedFunctionSpecUseCases: null,
          course: null,
        };
        break;
      case 'Course':
        journeyData = {
          ...journeyData,
          course: selectedItems.course?._id || selectedItems.course || null,
          selectedFunctionSpecUseCases: null,
          ideation: null,
        };
        break;
      default:
        journeyData = {
          ...journeyData,
          selectedFunctionSpecUseCases: null,
          course: null,
          ideation: null,
        };
    }

    editJourney(formFields.id, journeyData)
      .then((response) => {
        if (response.status === 'success') {
          toast.success('Journey updated successfully');
          navigate('/cds/journey/all');
        } else {
          throw new Error('Update failed');
        }
      })
      .catch((error) => {
        toast.error('Journey update failed: ' + error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // -------------------------------------------------------
  // 3. Helper Functions
  // -------------------------------------------------------

  // Sort languages
  const sortedLanguages = useMemo(() => {
    if (!platformLanguages) return [];

    return [...platformLanguages].sort((a, b) => {
      if (a.name.toLowerCase() === 'english') return -1;
      if (b.name.toLowerCase() === 'english') return 1;
      return 0;
    });
  }, [platformLanguages]);

  // Check form validity
  const isFormValid = useMemo(() => {
    // Basic required fields
    const hasBasicFields = formFields.title.trim() !== '' && formFields.journeyType !== '';

    // Check related content based on journey type
    let hasRequiredRelatedContent = true;

    if (formFields.journeyType === 'Usecases') {
      hasRequiredRelatedContent = !!selectedItems.usecase;
    } else if (formFields.journeyType === 'Ideation Project') {
      hasRequiredRelatedContent = !!selectedItems.ideation;
    } else if (formFields.journeyType === 'Course') {
      hasRequiredRelatedContent = !!selectedItems.course;
    } else if (
      formFields.journeyType === 'App Creator' ||
      formFields.journeyType === 'Workflow Creator' ||
      formFields.journeyType === 'GenAI Playground'
    ) {
      // Bu journey tipleri için özel bir içerik gerekmiyor, bu nedenle her zaman geçerli kabul edilir
      hasRequiredRelatedContent = true;
      console.log(
        `Journey tipi "${formFields.journeyType}" için form geçerliliği: ${hasRequiredRelatedContent}`
      );
    } else {
      // For custom journeys, check if there's content
      hasRequiredRelatedContent = formFields.content.some((content) => content.trim() !== '');
    }

    return hasBasicFields && hasRequiredRelatedContent;
  }, [formFields, selectedItems]);

  // Is data ready for display?
  const isDataReady = initialLoadDone && journeyDetails?.data;

  // -------------------------------------------------------
  // 4. Render Operations
  // -------------------------------------------------------

  const handleConfigureTinyMCE = (baseConfig) => {
    return {
      ...baseConfig,
      setup: (editor) => {
        editor.on('init', () => {
          // Manuel iframe ekleme butonu ekle
          editor.ui.registry.addButton('manualembed', {
            icon: 'embed',
            tooltip: 'Add Manual Embed',
            onAction: function () {
              editor.windowManager.open({
                title: 'Add Manual Embed / Iframe',
                body: {
                  type: 'panel',
                  items: [
                    {
                      type: 'input',
                      name: 'embedUrl',
                      label: 'Embed URL',
                      placeholder: 'https://example.com/embed',
                    },
                    {
                      type: 'input',
                      name: 'width',
                      label: 'Width',
                      inputMode: 'numeric',
                      placeholder: '560',
                    },
                    {
                      type: 'input',
                      name: 'height',
                      label: 'Height',
                      inputMode: 'numeric',
                      placeholder: '315',
                    },
                  ],
                },
                buttons: [
                  {
                    type: 'cancel',
                    text: 'Cancel',
                  },
                  {
                    type: 'submit',
                    text: 'Add',
                    primary: true,
                  },
                ],
                onSubmit: function (api) {
                  const data = api.getData();
                  const embedUrl = data.embedUrl;
                  const width = data.width || '560';
                  const height = data.height || '315';

                  if (embedUrl) {
                    const iframeHtml = `<iframe src="${embedUrl}" width="${width}" height="${height}" frameborder="0" allowfullscreen></iframe>`;
                    editor.insertContent(iframeHtml);
                  }

                  api.close();
                },
              });
            },
          });
        });

        // TinyMCE hata yönetimi
        editor.on('Error', (e) => {
          if (e.message.includes('Media embed handler')) {
            toast.error(
              'An error occurred while adding media. Try a different URL or add iframe manually.'
            );
          }
        });
      },
      // Toolbar'a manuel embed düğmesini ekle
      toolbar: (baseConfig.toolbar || '') + ' | manualembed',

      // TinyMCE temel ayarları
      branding: false,
      promotion: false,
      menubar: false,
      statusbar: true,
      resize: true,

      // Medya ayarları
      media_dimensions: false,
      media_live_embeds: false,
      media_scripts: [
        { filter: 'https://www.youtube.com/embed/*' },
        { filter: 'https://player.vimeo.com/video/*' },
        { filter: 'https://www.dailymotion.com/embed/video/*' },
      ],

      // Güvenlik ayarları
      valid_elements: baseConfig.valid_elements || '+*[*]',
      extended_valid_elements:
        baseConfig.extended_valid_elements ||
        'iframe[src|frameborder|style|scrolling|class|width|height|name|align|allowfullscreen]',

      // Medya URL çözümleyici
      media_url_resolver: (data, resolve, reject) => {
        try {
          if (data.url) {
            const url = data.url;

            // URL'yi güvenli bir şekilde işle
            let embedHtml = '';

            if (
              url.match(/youtube\.com\/watch\?v=([a-zA-Z0-9\-_]+)/) ||
              url.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)
            ) {
              // YouTube videosu
              const videoId = url.includes('youtube.com')
                ? url.match(/v=([a-zA-Z0-9\-_]+)/)?.[1]
                : url.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)?.[1];

              if (videoId) {
                embedHtml = `<iframe src="https://www.youtube.com/embed/${videoId}" width="560" height="315" frameborder="0" allowfullscreen></iframe>`;
              } else {
                reject('Not a valid YouTube URL');
                return;
              }
            } else if (url.match(/vimeo\.com\/([0-9]+)/)) {
              // Vimeo videosu
              const videoId = url.match(/vimeo\.com\/([0-9]+)/)?.[1];
              if (videoId) {
                embedHtml = `<iframe src="https://player.vimeo.com/video/${videoId}" width="560" height="315" frameborder="0" allowfullscreen></iframe>`;
              } else {
                reject('Not a valid Vimeo URL');
                return;
              }
            } else {
              // Diğer URL'ler için
              embedHtml = `<iframe src="${url}" width="560" height="315" frameborder="0" allowfullscreen></iframe>`;
            }

            resolve({ html: embedHtml });
          } else {
            reject('Please enter a valid URL');
          }
        } catch (error) {
          reject('Media URL processing error: ' + error.message);
        }
      },
    };
  };

  // Step content render
  const renderStepContent = (step) => {
    switch (step) {
      case 0: // Basic Information
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={12}>
              <Content
                currentTab={currentLanguage}
                titleEnglish={
                  currentLanguage === 'english'
                    ? formFields.title
                    : translations[currentLanguage]?.title || ''
                }
                description={
                  currentLanguage === 'english'
                    ? formFields.description
                    : translations[currentLanguage]?.description || ''
                }
                buttonText={
                  currentLanguage === 'english'
                    ? formFields.buttonText
                    : translations[currentLanguage]?.buttonText || ''
                }
                buttonUrl={formFields.buttonURL}
                buttonTypeInputValue={selectedItems.buttonType}
                setButtonTypeInputValue={handleButtonTypeChange}
                selectedButtonType={selectedItems.buttonType}
                setSelectedButtonType={handleButtonTypeChange}
                buttonTypes={buttonTypes}
                checkedClosedCard={formFields.isClosedCard}
                checkedNewTab={formFields.isNewTab}
                handleChangeButtonText={
                  currentLanguage === 'english'
                    ? handleFormChange('buttonText')
                    : handleTranslationChange('buttonText')
                }
                handleChangeButtonUrl={handleFormChange('buttonURL')}
                handleChangeClosedCard={handleFormChange('isClosedCard')}
                handleChangeNewTab={handleFormChange('isNewTab')}
                handleChangePercent={(event, newValue) => {
                  setFormFields((prev) => ({ ...prev, progressPercent: newValue }));
                }}
                handleChangetitleEnglish={
                  currentLanguage === 'english'
                    ? handleFormChange('title')
                    : handleTranslationChange('title')
                }
                handleChangeDescription={
                  currentLanguage === 'english'
                    ? handleFormChange('description')
                    : handleTranslationChange('description')
                }
                journeyTypeInputValue={selectedItems.journeyType}
                setJourneyTypeInputValue={handleJourneyTypeChange}
                selectedJourneyType={selectedItems.journeyType}
                setSelectedJourneyType={handleJourneyTypeChange}
                journeyTypes={journeyTypes}
                percent={formFields.progressPercent}
                onDropFile={handleFileDrop}
                fileUrl={formFields.thumbnailUrl}
                selectedProvider={selectedProvider}
                setSelectedProvider={setSelectedProvider}
                providerInputValue={providerInputValue}
                setProviderInputValue={setProviderInputValue}
                providerOptions={providerOptions}
                tinyMceConfig={handleConfigureTinyMCE({
                  dialog_type: 'modal',
                  image_advtab: true,
                  image_title: true,
                  image_dimensions: true,
                  image_caption: true,
                  automatic_uploads: false,
                  image_uploadtab: false,
                  file_picker_types: 'image media',
                  paste_data_images: true,
                  browser_spellcheck: true,
                  hidden_input: false,
                  convert_urls: false,
                  relative_urls: false,
                  remove_script_host: false,
                  document_base_url: window.location.origin,
                  extended_valid_elements:
                    'iframe[src|frameborder|style|scrolling|class|width|height|name|align|allowfullscreen]',
                  media_filter_html: false,
                  media_embed_cdata: true,
                  allow_script_urls: true,
                  allow_html_in_named_anchor: true,
                  valid_children: '+body[style],+a[div|p|img|span]',
                  plugins: [
                    'advlist',
                    'autolink',
                    'lists',
                    'link',
                    'image',
                    'charmap',
                    'preview',
                    'anchor',
                    'searchreplace',
                    'visualblocks',
                    'code',
                    'fullscreen',
                    'insertdatetime',
                    'media',
                    'table',
                    'help',
                    'wordcount',
                  ],
                  toolbar:
                    'undo redo | formatselect | ' +
                    'bold italic backcolor | alignleft aligncenter ' +
                    'alignright alignjustify | bullist numlist outdent indent | ' +
                    'removeformat | media | help',
                })}
              />
            </Grid>
          </Grid>
        );

      case 1: // Content
        return (
          <JourneyType
            selectedJourneyType={selectedItems.journeyType}
            editors={formFields.editors}
            editorsContent={translations[currentLanguage]?.content || formFields.content}
            selectedFSU={selectedItems.usecase}
            setSelectedFSU={(value) => setSelectedItems((prev) => ({ ...prev, usecase: value }))}
            fsuInputValue={inputValues.usecase}
            setFsuInputValue={(value) => setInputValues((prev) => ({ ...prev, usecase: value }))}
            selectedIdeation={selectedItems.ideation}
            setSelectedIdeation={(value) =>
              setSelectedItems((prev) => ({ ...prev, ideation: value }))
            }
            ideationInputValue={inputValues.ideation}
            setIdeationInputValue={(value) =>
              setInputValues((prev) => ({ ...prev, ideation: value }))
            }
            selectedCourse={selectedItems.course}
            setSelectedCourse={(value) => {
              setSelectedItems((prev) => ({ ...prev, course: value }));
            }}
            courseInputValue={inputValues.course}
            setCourseInputValue={(value) => {
              setInputValues((prev) => ({ ...prev, course: value }));
            }}
            fsu={usecases}
            handleAddEditor={handleAddEditor}
            handleEditorChange={handleEditorChange}
            handleRemoveEditor={handleRemoveEditor}
            ideations={ideations}
            courses={courses}
            tinyMceConfig={handleConfigureTinyMCE({
              dialog_type: 'modal',
              image_advtab: true,
              image_title: true,
              image_dimensions: true,
              image_caption: true,
              automatic_uploads: false,
              image_uploadtab: false,
              file_picker_types: 'image media',
              paste_data_images: true,
              browser_spellcheck: true,
              hidden_input: false,
              convert_urls: false,
              relative_urls: false,
              remove_script_host: false,
              document_base_url: window.location.origin,
              extended_valid_elements:
                'iframe[src|frameborder|style|scrolling|class|width|height|name|align|allowfullscreen]',
              media_filter_html: false,
              media_embed_cdata: true,
              allow_script_urls: true,
              allow_html_in_named_anchor: true,
              valid_children: '+body[style],+a[div|p|img|span]',
              plugins: [
                'advlist',
                'autolink',
                'lists',
                'link',
                'image',
                'charmap',
                'preview',
                'anchor',
                'searchreplace',
                'visualblocks',
                'code',
                'fullscreen',
                'insertdatetime',
                'media',
                'table',
                'help',
                'wordcount',
              ],
              toolbar:
                'undo redo | formatselect | ' +
                'bold italic backcolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | media link image | help',
            })}
          />
        );

      case 2: // Targeting
        return (
          <CheckboxArea
            aof={industry}
            functions={functions}
            levels={levels}
            managementRoles={managementRoles}
            handleAOFChange={handleCheckboxGroupChange('targetAreas')}
            handleFunctionChange={handleCheckboxGroupChange('targetFunctions')}
            handleLevelChange={handleCheckboxGroupChange('targetLevels')}
            handleManagementRoleChange={handleCheckboxGroupChange('targetRoles')}
            selectedAOF={formFields.targetAreas}
            selectedFunctions={formFields.targetFunctions}
            selectedLevels={formFields.targetLevels}
            selectedManagementRoles={formFields.targetRoles}
          />
        );

      default:
        return null;
    }
  };

  // Loading state content
  if (isFetching) {
    return <LoadingScreen />;
  }

  // If data is not ready
  if (!isDataReady) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'xl'}>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Journey data could not be loaded
          </Typography>
          <Button
            startIcon={<ArrowBack />}
            variant="outlined"
            onClick={() => navigate('/cds/journey/all')}
            sx={{ mt: 2 }}
          >
            Back to Journeys
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      {/* Title and Breadcrumbs */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            color="inherit"
            component="button"
            onClick={() => navigate('/cds')}
          >
            CDS
          </Link>
          <Link
            underline="hover"
            color="inherit"
            component="button"
            onClick={() => navigate('/cds/journey/all')}
          >
            Journeys
          </Link>
          <Typography color="text.primary">Edit Journey</Typography>
        </Breadcrumbs>

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h4">Edit Journey</Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TextField
              value={formFields.id}
              label="UUID"
              size="small"
              sx={{ width: 220 }}
              InputProps={{
                readOnly: true,
                endAdornment: (
                  <Tooltip title="Copy UUID">
                    <IconButton onClick={handleCopyUUID} size="small">
                      <ContentCopy fontSize="small" />
                    </IconButton>
                  </Tooltip>
                ),
              }}
            />
          </Box>
        </Box>
      </Box>

      <Card sx={{ p: 3, mb: 3 }}>
        {/* Stepper */}
        <Box sx={{ display: { xs: 'none', md: 'block' } }}>
          <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
            {STEPS.map((step) => (
              <Step key={step.label}>
                <StepLabel>{step.label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Mobile stepper */}
        <Box sx={{ display: { xs: 'block', md: 'none' }, mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Step {activeStep + 1}: {STEPS[activeStep].label}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {STEPS[activeStep].description}
          </Typography>
        </Box>

        {/* Language Tabs - Add in all steps except for Targeting */}
        {activeStep !== 2 && (
          <Box
            sx={{
              display: 'flex',
              borderBottom: 1,
              borderColor: 'divider',
              mb: 3,
              gap: 2,
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Tabs
              value={currentLanguage}
              onChange={(e, newValue) => setCurrentLanguage(newValue)}
              variant="scrollable"
              scrollButtons="auto"
            >
              {sortedLanguages.map((lang) => (
                <Tab key={lang.name} value={lang.name.toLowerCase()} label={lang.name} />
              ))}
            </Tabs>

            {/* Show translate button for non-English languages */}
            <Button
              variant="contained"
              startIcon={<Translate />}
              onClick={handleTranslateClick}
              disabled={currentLanguage === 'english'}
              sx={{ display: { xs: 'none', md: 'flex' } }}
              title={
                currentLanguage === 'english'
                  ? 'Select a language to translate to'
                  : 'Translate from English'
              }
            >
              Translate
            </Button>
          </Box>
        )}

        {/* Step content */}
        <Box sx={{ mb: 4 }}>{renderStepContent(activeStep)}</Box>

        {/* Buttons */}
        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between', pt: 2 }}>
          <Button
            color="inherit"
            onClick={activeStep === 0 ? handleCancel : handleBack}
            startIcon={activeStep === 0 ? <ArrowBack /> : <ChevronLeft />}
          >
            {activeStep === 0 ? 'Cancel' : 'Back'}
          </Button>

          <Box>
            <Button
              color="primary"
              variant={activeStep === STEPS.length - 1 ? 'outlined' : 'contained'}
              onClick={handleNext}
              disabled={activeStep === STEPS.length - 1}
              endIcon={<ChevronRight />}
              sx={{ mr: 1 }}
            >
              Next
            </Button>

            <Button
              color="primary"
              variant="contained"
              onClick={handleSaveJourney}
              disabled={isLoading || !isFormValid}
              startIcon={isLoading ? <CircularProgress size={20} /> : <Save />}
            >
              {isLoading ? 'Saving...' : 'Save'}
            </Button>
          </Box>
        </Box>
      </Card>

      {/* Translate modal */}
      <TranslateConfirmationModal
        open={translateModalOpen}
        onClose={() => setTranslateModalOpen(false)}
        onConfirm={handleConfirmTranslate}
      />
    </Container>
  );
}

UpdateJourneyView.propTypes = {
  journeyId: PropTypes.string,
};
