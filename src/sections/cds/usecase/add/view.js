/* eslint-disable no-nested-ternary */
/* eslint-disable react-hooks/exhaustive-deps */

// @mui
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Typography from '@mui/material/Typography';
import { useEffect, useState } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAITranslator } from 'src/apis/useAITranslator';

// components
import { useSettingsContext } from 'src/components/settings';

import { LoadingButton } from '@mui/lab';

import { useUsecases } from 'src/apis';
import TranslateConfirmationModal from 'src/components/TranslateConfirmationModal';
import UsecaseCard from './components/UsecaseCard';
import UsecaseTypes from './components/usecaseTypes';

// ----------------------------------------------------------------------

export default function AddUsecaseView() {
  const settings = useSettingsContext();
  const { getFunctions, functions, addUsecase } = useUsecases();

  const { getPlatformLanguages, translateJourney, platformLanguages } = useAITranslator();
  const [titleEnglish, setTitleEnglish] = useState('');
  const [description, setDescription] = useState('');
  const [prompt, setPrompt] = useState('');
  const [customSystemPrompt, setCustomSystemPrompt] = useState('');
  const [uniqueId, setUniqueId] = useState('');
  const [usecaseIcon, setUsecaseIcon] = useState('');
  const [apiType, setApiType] = useState('completions');
  const [selectedFunctions, setSelectedFunctions] = useState([]);
  const [isTranslating, setIsTranslating] = useState(false);
  const [currentTab, setCurrentTab] = useState('english');
  const [translations, setTranslations] = useState({
    english: { title: '', description: '', prompt: '', customSystemPrompt: '' },
  });
  const [openTranslateModal, setOpenTranslateModal] = useState(false);

  const [repeaterFormFields, setrepeaterFormFields] = useState([
    {
      id: Date.now(),
      type: '',
      label: '',
      name: '',
      defaultValue: '',
      choices: '',
    },
  ]);
  const [promptExamplefields, setPromptExamplefields] = useState([
    {
      id: Date.now(),
      title: '',
      description: '',
      prompt: '',
    },
  ]);

  const [model, setModel] = useState(apiType === 'dalle' ? 'dall-e-3' : 'gpt-3.5-turbo');
  const [maxTokens, setMaxTokens] = useState(1024);
  const [topP, setTopP] = useState(1);
  const [temperature, setTemperature] = useState(0.5);
  const [frequencyPenalty, setFrequencyPenalty] = useState(0);
  const [presencePenalty, setPresencePenalty] = useState(0);
  const [selectedUsecaseType, setSelectedUsecaseType] = useState([]);
  const [usecase_order, setUsecaseOrder] = useState(100);
  const [function_order, setFunctionOrder] = useState({});

  useEffect(() => {
    getFunctions().then((res) => {
      const order =
        res?.reduce(
          (acc, item) => ({
            ...acc,
            [item.slug]: 200,
          }),
          {}
        ) || {};
      setFunctionOrder(order);
    });
  }, []);

  const [assistant_name, setAssistantName] = useState('');
  const [assistant_instructions, setAssistantInstructions] = useState('');
  const [assistant_id, setAssistantId] = useState('');
  const [file_id, setFileId] = useState('');
  const [isChatModeActive, setIsChatModeActive] = useState(false);

  const [dallESize, setDallESize] = useState('1024x1024');
  const sortLanguages = (languages) => {
    if (!languages) return [];

    return [...languages].sort((a, b) => {
      if (a.name.toLowerCase() === 'english') return -1;
      if (b.name.toLowerCase() === 'english') return 1;
      return 0;
    });
  };

  useEffect(() => {
    if (platformLanguages?.length > 0) {
      const sortedLanguages = sortLanguages(platformLanguages);
      setCurrentTab(sortedLanguages[0].name.toLowerCase());
    }
  }, [platformLanguages]);

  const handleChangetitleEnglish = (e, lang = 'english') => {
    if (lang === 'english') {
      setTitleEnglish(e.target.value);
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        title: e.target.value,
      },
    }));
  };

  const handleChangeDescription = (e, lang = 'english') => {
    if (lang === 'english') {
      setDescription(e.target.value);
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        description: e.target.value,
      },
    }));
  };

  const handleUsecaseTypeChange = (event) => {
    const { name, checked } = event.target;
    if (checked) {
      setSelectedUsecaseType((prev) => [...prev, name]);
    } else {
      setSelectedUsecaseType((prev) => prev.filter((type) => type !== name));
    }
  };

  const handleChangePrompt = (e, lang = 'english') => {
    if (lang === 'english') {
      setPrompt(e.target.value);
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        prompt: e.target.value,
      },
    }));
  };
  const handleChangeCustomSystemPrompt = (e, lang = 'english') => {
    if (lang === 'english') {
      setCustomSystemPrompt(e.target.value);
    }
    setTranslations((prev) => ({
      ...prev,
      [lang]: {
        ...prev[lang],
        customSystemPrompt: e.target.value,
      },
    }));
  };

  const handleChangeUniqueId = (e) => {
    setUniqueId(e.target.value);
  };

  const handleChangeUsecaseIcon = (e) => {
    setUsecaseIcon(e.target.value);
  };

  const handleChangeApiType = (event) => {
    setApiType(event.target.value);
  };

  const handleFunctionChange = (event) => {
    const value = event.target.name;
    setSelectedFunctions((prev) => [...prev, value]);
  };

  const handleModelChange = (event) => {
    setModel(event.target.value);
  };

  const handleMaxTokensChange = (event) => {
    setMaxTokens(event.target.value);
  };

  const handleTopPChange = (event) => {
    setTopP(event.target.value);
  };

  const handleTemperatureChange = (event) => {
    setTemperature(event.target.value);
  };

  const handleFrequencyPenaltyChange = (event) => {
    setFrequencyPenalty(event.target.value);
  };

  const handlePresencePenaltyChange = (event) => {
    setPresencePenalty(event.target.value);
  };

  const handleUsecaseOrderChange = (e) => {
    const value = Number(e.target.value);
    setUsecaseOrder(value);
  };

  const handleFunctionOrderChange = (functionSlug, value) => {
    setFunctionOrder((prev) => ({
      ...prev,
      [functionSlug.replace(/-/g, '_')]: Number(value),
    }));
  };

  const handleChangeAssistantName = (e) => {
    setAssistantName(e.target.value);
  };

  const handleChangeAssistantInstructions = (e) => {
    setAssistantInstructions(e.target.value);
  };

  const handleChangeAssistantId = (e) => {
    setAssistantId(e.target.value);
  };

  const handleChangeFileId = (e) => {
    setFileId(e.target.value);
  };

  const handleChangeChatModeActive = (e) => {
    setIsChatModeActive(e.target.checked);
  };

  const handleDallESizeChange = (e) => {
    setDallESize(e.target.value);
  };

  useEffect(() => {
    getPlatformLanguages();
  }, []);

  const resetForm = () => {
    setTitleEnglish('');
    setDescription('');
    setrepeaterFormFields([
      {
        id: Date.now(),
        type: '',
        label: '',
        name: '',
        defaultValue: '',
        choices: '',
      },
    ]);
  };

  // eslint-disable-next-line arrow-body-style
  const isFormValid = () => {
    return titleEnglish;
  };

  const createNewUsecase = () => {
    if (!isFormValid()) {
      toast.error('Please fill in all fields before adding the journey');
      return;
    }
    const functions = Array.isArray(selectedFunctions)
      ? Array.isArray(selectedFunctions[0])
        ? selectedFunctions[0]
        : selectedFunctions
      : [];

    const usecaseData = {
      titleEnglish,
      description,
      uniqueId,
      usecaseIcon,
      apiType,
      selectedFunctions: functions,
      translations,
      [`${apiType}_settings`]: {
        form_fields: repeaterFormFields,
        prompt,
        custom_system_prompt: customSystemPrompt,
        model,
        max_tokens: maxTokens,
        top_p: topP,
        temperature,
        frequency_penalty: frequencyPenalty,
        presence_penalty: presencePenalty,
        assistant_name,
        assistant_instructions,
        assistant_id,
        file_id,
        is_chat_mode_active: isChatModeActive,
        prompt_examples: promptExamplefields,
        dall_e_size: apiType === 'dalle' ? dallESize : null,
      },
      function_order,
      usecase_type: selectedUsecaseType,
      usecase_order,
    };

    addUsecase(usecaseData).then((res) => {
      if (res) {
        if (res.status === 'success') {
          toast.success('Usecase added successfully');
          resetForm();
          navigate('/cds/usecases/all');
        } else {
          console.error('Usecase', res.message);
          toast.error('Usecase add error' + res.message);
        }
      } else {
        toast.error('Usecase add error');
      }
    });
  };
  useEffect(() => {
    setIsTranslating(false);
  }, [translations]);

  const handleTranslate = () => {
    if (!titleEnglish.trim()) {
      toast.error('Please fill in the English title first');
      return;
    }
    setOpenTranslateModal(true);
  };

  const handleConfirmTranslate = () => {
    const journey = {
      title_english: titleEnglish,
      description,
      prompt,
      custom_system_prompt: customSystemPrompt,
    };

    platformLanguages.forEach((language) => {
      if (language.name.toLowerCase() !== 'english') {
        translateJourney(journey, 'en', language.lang)
          .then((res) => {
            if (res) {
              setTranslations((prev) => ({
                ...prev,
                [language.name.toLowerCase()]: {
                  title: res.message.title || res.message.title_english || '',
                  description: res.message.description || '',
                  prompt: res.message.prompt || '',
                  custom_system_prompt: res.message.custom_system_prompt || '',
                },
              }));
              setIsTranslating(false);
              toast.success(`${language.name} translation success`);
            }
          })
          .catch((error) => {
            console.error(`Error during ${language.name} translation:`, error);
            toast.error(`${language.name} translation failed`);
          });
      }
    });

    setOpenTranslateModal(false);
  };

  const handleAddrepeaterFormField = () => {
    setrepeaterFormFields([
      ...repeaterFormFields,
      {
        id: Date.now(),
        type: '',
        label: '',
        name: '',
        defaultValue: '',
        choices: '',
      },
    ]);
  };

  const handlerepeaterFormFieldChange = (id, event) => {
    const newFields = repeaterFormFields.map((field) => {
      if (field.id === id) {
        return { ...field, [event.target.name]: event.target.value };
      }
      return field;
    });
    setrepeaterFormFields(newFields);
  };
  const handleDeleterepeaterFormField = (id) => {
    if (window.confirm('Are you sure you want to delete this field?')) {
      setrepeaterFormFields(repeaterFormFields.filter((field) => field.id !== id));
      toast.success('Field deleted successfully');
    }
  };

  const handleAddPromptExampleField = () => {
    setPromptExamplefields([
      ...promptExamplefields,
      { id: Date.now(), title: '', description: '', prompt: '' },
    ]);
  };

  const handlePromptExampleFieldChange = (id, event) => {
    const newFields = promptExamplefields.map((field) => {
      if (field.id === id) {
        return { ...field, [event.target.name]: event.target.value };
      }
      return field;
    });
    setPromptExamplefields(newFields);
  };

  const handleDeletePromptExampleField = (id) => {
    if (window.confirm('Are you sure you want to delete this field?')) {
      setPromptExamplefields(promptExamplefields.filter((field) => field.id !== id));
      toast.success('Field deleted successfully');
    }
  };

  // Tab değişikliklerini izleyen useEffect
  useEffect(() => {}, [currentTab]);

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4"> Add Usecase </Typography>

      <Box
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          mt: 3,
          display: 'flex',
          gap: 2,
          justifyContent: 'space-between',
        }}
      >
        <Tabs value={currentTab || 'english'} onChange={(e, newValue) => setCurrentTab(newValue)}>
          {platformLanguages &&
            sortLanguages(platformLanguages).map((lang) => (
              <Tab key={lang.name} value={lang.name.toLowerCase()} label={lang.name} />
            ))}
        </Tabs>
        <LoadingButton
          variant="contained"
          onClick={handleTranslate}
          loading={isTranslating}
          loadingIndicator="Translating..."
          disabled={!translations.english?.title && !translations.english?.description}
        >
          Translate
        </LoadingButton>
      </Box>

      {platformLanguages &&
        sortLanguages(platformLanguages).map(
          (lang) =>
            currentTab === lang.name.toLowerCase() && (
              <UsecaseCard
                key={lang.name}
                titleEnglish={translations[lang.name.toLowerCase()]?.title || ''}
                description={translations[lang.name.toLowerCase()]?.description || ''}
                prompt={translations[lang.name.toLowerCase()]?.prompt || ''}
                handleChangeDescription={(e) => handleChangeDescription(e, lang.name.toLowerCase())}
                handleChangePrompt={(e) => handleChangePrompt(e, lang.name.toLowerCase())}
                handleChangeCustomSystemPrompt={(e) =>
                  handleChangeCustomSystemPrompt(e, lang.name.toLowerCase())
                }
                handleChangetitleEnglish={(e) =>
                  handleChangetitleEnglish(e, lang.name.toLowerCase())
                }
                apiType={apiType}
                handleChangeApiType={handleChangeApiType}
                setApiType={setApiType}
                setUniqueId={setUniqueId}
                uniqueId={uniqueId}
                setUsecaseIcon={setUsecaseIcon}
                usecaseIcon={usecaseIcon}
                handleChangeUniqueId={handleChangeUniqueId}
                handleChangeUsecaseIcon={handleChangeUsecaseIcon}
                functions={functions}
                handleFunctionChange={handleFunctionChange}
                selectedFunctions={selectedFunctions}
                createNewUsecase={createNewUsecase}
                repeaterFormFields={repeaterFormFields}
                handleAddrepeaterFormField={handleAddrepeaterFormField}
                handlerepeaterFormFieldChange={handlerepeaterFormFieldChange}
                handleDeleterepeaterFormField={handleDeleterepeaterFormField}
                model={model}
                handleModelChange={handleModelChange}
                maxTokens={maxTokens}
                handleMaxTokensChange={handleMaxTokensChange}
                topP={topP}
                handleTopPChange={handleTopPChange}
                temperature={temperature}
                handleTemperatureChange={handleTemperatureChange}
                frequencyPenalty={frequencyPenalty}
                handleFrequencyPenaltyChange={handleFrequencyPenaltyChange}
                presencePenalty={presencePenalty}
                handlePresencePenaltyChange={handlePresencePenaltyChange}
                usecaseTypes={UsecaseTypes()}
                handleUsecaseTypeChange={handleUsecaseTypeChange}
                selectedUsecaseType={selectedUsecaseType}
                usecase_order={usecase_order}
                handleUsecaseOrderChange={handleUsecaseOrderChange}
                function_order={function_order}
                handleFunctionOrderChange={handleFunctionOrderChange}
                assistant_name={assistant_name}
                handleChangeAssistantName={handleChangeAssistantName}
                assistant_instructions={assistant_instructions}
                handleChangeAssistantInstructions={handleChangeAssistantInstructions}
                assistant_id={assistant_id}
                handleChangeAssistantId={handleChangeAssistantId}
                file_id={file_id}
                handleChangeFileId={handleChangeFileId}
                isChatModeActive={isChatModeActive}
                handleChangeChatModeActive={handleChangeChatModeActive}
                promptExamplefields={promptExamplefields}
                handleAddPromptExampleField={handleAddPromptExampleField}
                handlePromptExampleFieldChange={handlePromptExampleFieldChange}
                handleDeletePromptExampleField={handleDeletePromptExampleField}
                dallESize={dallESize}
                handleDallESizeChange={handleDallESizeChange}
              />
            )
        )}

      <TranslateConfirmationModal
        open={openTranslateModal}
        onClose={() => setOpenTranslateModal(false)}
        onConfirm={handleConfirmTranslate}
      />
    </Container>
  );
}
