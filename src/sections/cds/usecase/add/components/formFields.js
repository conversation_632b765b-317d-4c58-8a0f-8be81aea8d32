import React from 'react';
import { Box, Button, TextField, Typography, Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import PropTypes from 'prop-types';

const RepeaterFormFields = ({
  fields,
  handleAddField,
  handleFieldChange,
  handleDeleteField,
  apiType,
}) => {
  return (
    <Box>
      <h4> {apiType === 'completions' ? 'Completions' : apiType === 'stream' ? 'Stream' : apiType === 'dalle' ? 'Dalle' : apiType === 'heygen' ? 'HeyGen' : 'Assistant'} Form Fields </h4>
      {fields.map(field => (
        <Box key={field.id} sx={{ mb: 3, mt: 3, display: 'flex', justifyContent: 'space-between' , gap: 2}}
          
        >
          <FormControl fullWidth>
            <InputLabel id={`type-label-${field.id}`}>Type</InputLabel>
            <Select
              labelId={`type-label-${field.id}`}
              name="type"
              value={field.type || 'text'}
              onChange={(e) => handleFieldChange(field.id, e)}
              required
              label="Type"
            >

              
              <MenuItem value="text">Text</MenuItem>
              <MenuItem value="select">Select</MenuItem>
              <MenuItem value="textarea">Textarea</MenuItem>
              <MenuItem value="code">Code Field</MenuItem>
            </Select>
          </FormControl>

          <TextField
            name="label"
            label="Label"
            variant="outlined"
            fullWidth
            value={field.label}
            onChange={(e) => handleFieldChange(field.id, e)}
            required
          />
          <TextField
            name="name"
            label="Name"
            variant="outlined"
            fullWidth
            value={field.name}
            onChange={(e) => handleFieldChange(field.id, e)}
            required
          />
          <TextField
            name="defaultValue"
            label="Default Value"
            variant="outlined"
            fullWidth
            multiline
            rows={2}
            value={field.defaultValue}
            onChange={(e) => handleFieldChange(field.id, e)}
            required
          />
          
          {field.type === 'select' && (
            <TextField
              name="choices"
              label="Choices"
              variant="outlined"
              fullWidth
              multiline
              rows={2}
              value={field.choices}
              onChange={(e) => handleFieldChange(field.id, e)}
            />
          )}
          <Button variant="contained" onClick={() => handleDeleteField(field.id)} sx={{ mt: 0 , padding: '2px 8px', display: 'flex', justifySelf: 'right',  width: 'fit-content', height: 'fit-content', backgroundColor: 'red', color: 'white', minWidth: 'fit-content'}}>X</Button>
        </Box>
      ))}
      <Button variant="contained" onClick={handleAddField} sx={{ mt: 5 , display: 'flex', justifySelf: 'right'}}>
        Add  Field +
      </Button>
    </Box>
  );
};

RepeaterFormFields.propTypes = {
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      type: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      defaultValue: PropTypes.string.isRequired,
      choices: PropTypes.string,
    })
  ).isRequired,
  handleAddField: PropTypes.func.isRequired,
  handleFieldChange: PropTypes.func.isRequired,
};

export default RepeaterFormFields;