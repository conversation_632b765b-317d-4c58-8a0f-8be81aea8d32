import { alpha, Button, Card, Grid } from '@mui/material';
import PropTypes from 'prop-types';
import CheckboxArea from './CheckboxArea';
import Content from './Content';
 

export default function UsecaseCard({
  titleEnglish,
  handleChangeDescription,
  description, 
  prompt,
  handleChangePrompt,
  customSystemPrompt,
  handleChangeCustomSystemPrompt,
  handleChangetitleEnglish,
  uniqueId,
  handleChangeUniqueId,
  usecaseIcon,
  handleChangeUsecaseIcon,
  apiType,
  handleChangeApiType,
  functions,
  handleFunctionChange,
  selectedFunctions,
  createNewUsecase,
  repeaterFormFields,
  handleAddrepeaterFormField,
  handlerepeaterFormFieldChange,
  handleDeleterepeaterFormField,
  model,
  handleModelChange,
  maxTokens,
  handleMaxTokensChange,
  topP,
  handleTopPChange,
  temperature,
  handleTemperatureChange,
  frequencyPenalty,
  handleFrequencyPenaltyChange,
  presencePenalty,
  handlePresencePenaltyChange,
  UsecaseTypes,
  handleUsecaseTypeChange,
  selectedUsecaseType,
  usecase_order,
  handleUsecaseOrderChange,
  function_order,
  handleFunctionOrderChange, 
  assistant_name,
  handleChangeAssistantName,
  assistant_instructions,
  handleChangeAssistantInstructions,
  assistant_id,
  handleChangeAssistantId,
  file_id,
  handleChangeFileId,
  isChatModeActive,
  handleChangeChatModeActive,
  promptExamplefields,
  handleAddPromptExampleField,
  handlePromptExampleFieldChange,
  handleDeletePromptExampleField,
  dallESize,
  handleDallESizeChange,
}) {
  return (
    <Card
      sx={{
        minHeight: 570,
        mt: 5,
        borderRadius: 2,
        flexGrow: 1,
        bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04),
        p: 2,
      }} 
    >
     
      <Grid container spacing={{ xs: 2, md: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
        <Grid item xs={12} sm={12} md={12} lg={12}>
          <Content
            titleEnglish={titleEnglish}
            handleChangeDescription={handleChangeDescription}
            description={description} 
            prompt={prompt}
            handleChangePrompt={handleChangePrompt}
            handleChangetitleEnglish={handleChangetitleEnglish}
            customSystemPrompt={customSystemPrompt}
            handleChangeCustomSystemPrompt={handleChangeCustomSystemPrompt}
            uniqueId={uniqueId}
            handleChangeUniqueId={handleChangeUniqueId}
            usecaseIcon={usecaseIcon}
            handleChangeUsecaseIcon={handleChangeUsecaseIcon}
            apiType={apiType}
            handleChangeApiType={handleChangeApiType}
            repeaterFormFields={repeaterFormFields}
            handleAddrepeaterFormField={handleAddrepeaterFormField}
            handlerepeaterFormFieldChange={handlerepeaterFormFieldChange}
            handleDeleterepeaterFormField={handleDeleterepeaterFormField}
            model={model}
            handleModelChange={handleModelChange}
            maxTokens={maxTokens}
            handleMaxTokensChange={handleMaxTokensChange}
            topP={topP}
            handleTopPChange={handleTopPChange}
            temperature={temperature}
            handleTemperatureChange={handleTemperatureChange}
            frequencyPenalty={frequencyPenalty}
            handleFrequencyPenaltyChange={handleFrequencyPenaltyChange}
            presencePenalty={presencePenalty}
            handlePresencePenaltyChange={handlePresencePenaltyChange}
            assistant_name={assistant_name}
            handleChangeAssistantName={handleChangeAssistantName}
            assistant_instructions={assistant_instructions}
            handleChangeAssistantInstructions={handleChangeAssistantInstructions}
            assistant_id={assistant_id}
            handleChangeAssistantId={handleChangeAssistantId}
            file_id={file_id}
            handleChangeFileId={handleChangeFileId}
            isChatModeActive={isChatModeActive}
            handleChangeChatModeActive={handleChangeChatModeActive}
            promptExamplefields={promptExamplefields}
            handleAddPromptExampleField={handleAddPromptExampleField}
            handlePromptExampleFieldChange={handlePromptExampleFieldChange}
            handleDeletePromptExampleField={handleDeletePromptExampleField}
            dallESize={dallESize}
            handleDallESizeChange={handleDallESizeChange}
          />
        </Grid>
         
      </Grid>
      <CheckboxArea 
        functions={functions}
        selectedFunctions={selectedFunctions}
        handleFunctionChange={handleFunctionChange}
        UsecaseTypes={UsecaseTypes}
        handleUsecaseTypeChange={handleUsecaseTypeChange}
        selectedUsecaseType={selectedUsecaseType}
        usecase_order={usecase_order}
        handleUsecaseOrderChange={handleUsecaseOrderChange}
        function_order={function_order}
        handleFunctionOrderChange={handleFunctionOrderChange}
      />
      
      <Button variant="contained" fullWidth sx={{ mt: 3 }} onClick={createNewUsecase}>
        Add Usecase
      </Button>
    </Card>
  );
}

UsecaseCard.propTypes = {
  titleEnglish: PropTypes.string,
  handleChangeDescription: PropTypes.func,
  description: PropTypes.string,
  prompt: PropTypes.string,
  handleChangePrompt: PropTypes.func,
  customSystemPrompt: PropTypes.string,
  handleChangeCustomSystemPrompt: PropTypes.func,
  uniqueId: PropTypes.string,
  usecaseIcon: PropTypes.string,
  handleChangeUniqueId: PropTypes.func,
  handleChangeUsecaseIcon: PropTypes.func,
  functions: PropTypes.array,
  handleFunctionChange: PropTypes.func,
  selectedFunctions: PropTypes.array,
  createNewUsecase: PropTypes.func,
  repeaterFormFields: PropTypes.array,
  handleAddrepeaterFormField: PropTypes.func,
  handlerepeaterFormFieldChange: PropTypes.func,
  handleDeleterepeaterFormField: PropTypes.func,
  apiType: PropTypes.string,
  handleChangeApiType: PropTypes.func,
  model: PropTypes.string,
  handleModelChange: PropTypes.func,
  maxTokens: PropTypes.number,
  handleMaxTokensChange: PropTypes.func,
  topP: PropTypes.number,
  handleTopPChange: PropTypes.func,
  temperature: PropTypes.number,
  handleTemperatureChange: PropTypes.func,
  frequencyPenalty: PropTypes.number,
  handleFrequencyPenaltyChange: PropTypes.func,
  presencePenalty: PropTypes.number,
  handlePresencePenaltyChange: PropTypes.func,
  UsecaseTypes: PropTypes.array,
  handleUsecaseTypeChange: PropTypes.func,
  selectedUsecaseType: PropTypes.array,
  usecase_order: PropTypes.number,
  handleUsecaseOrderChange: PropTypes.func,
  function_order: PropTypes.object,
  handleFunctionOrderChange: PropTypes.func,
  assistant_name: PropTypes.string,
  handleChangeAssistantName: PropTypes.func,
  assistant_instructions: PropTypes.string,
  handleChangeAssistantInstructions: PropTypes.func,
  assistant_id: PropTypes.string,
  handleChangeAssistantId: PropTypes.func,
  file_id: PropTypes.string,
  handleChangeFileId: PropTypes.func,
  isChatModeActive: PropTypes.bool,
  handleChangeChatModeActive: PropTypes.func,
  promptExamplefields: PropTypes.array,
  handleAddPromptExampleField: PropTypes.func,
  handlePromptExampleFieldChange: PropTypes.func,
  handleDeletePromptExampleField: PropTypes.func,
  dallESize: PropTypes.string,
  handleDallESizeChange: PropTypes.func,
};
