import { Box, Checkbox, FormControl<PERSON>abel, Grid, Stack, Typography, TextField } from '@mui/material';
import PropTypes from 'prop-types';
import UsecaseTypes from './usecaseTypes';

export default function CheckboxArea({ 
  functions,
  usecase_order,
  handleUsecaseOrderChange,
  handleFunctionChange,
  selectedFunctions,
  selectedUsecaseType,
  handleUsecaseTypeChange,
  function_order, 
  handleFunctionOrderChange
}) {
  return (
    <Stack sx={{ mt: 5 }}>
      <Grid container spacing={{ xs: 2, md: 3, lg: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
        
        <Grid item xs={12} sm={12} md={3} lg={3} sx={{ border: '1px solid #F1F2F5' }}>
          <Typography variant="h6" component="h6">
            Functions
          </Typography>
          {functions?.map((item) => (
            <Box key={item._id}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedFunctions?.includes(item.slug)}
                    onChange={handleFunctionChange}
                    name={item.slug}
                  />
                }
                label={item.translations?.en}
              />
            </Box>
          ))}
        </Grid>
        <Grid item xs={12} sm={12} md={3} lg={3} sx={{ border: '1px solid #F1F2F5' }}>
          <Typography variant="h6" component="h6">
            Usecase Types
          </Typography>
          {UsecaseTypes().map((item) => (
            <Box key={item.value}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedUsecaseType.includes(item.value)}
                    onChange={handleUsecaseTypeChange}
                    name={item.value}
                  />
                }
                label={item.translations?.en}
              />
            </Box>
          ))}
        </Grid>
        <Grid item xs={12} sm={12} md={6} lg={6} sx={{ border: '1px solid #F1F2F5' }}>
          <Typography variant="h6" component="h6">
            Order Settings
          </Typography>
           <TextField
            label="Main Order"
            value={usecase_order}
            onChange={handleUsecaseOrderChange}
            type="number"
            fullWidth
            variant="outlined"
            sx={{ mb: 2, mt: 2 }}
          />
          <Typography variant="h6" component="h6" sx={{ mt: 3, mb: 2 }}>
            Function Orders
            </Typography>
            <Grid container spacing={2}>
              {functions?.map((item) => (
                <Grid item xs={12} sm={6} md={4} key={item.slug}>
                  <TextField
                    label={item.translations?.en}
                    value={function_order?.[item.slug] || '100'}
                    onChange={(e) => handleFunctionOrderChange(item.slug, e.target.value)}
                    type="number"
                    fullWidth
                    variant="outlined"
                  />
                </Grid>
              ))}
            </Grid>
        </Grid>
        
      </Grid>

      
    </Stack>
  );
}
CheckboxArea.propTypes = {
  functions: PropTypes.array,
  selectedFunctions: PropTypes.array, 
  selectedUsecaseType: PropTypes.array.isRequired,
  handleUsecaseTypeChange: PropTypes.func.isRequired,
  usecase_order: PropTypes.number,
  handleUsecaseOrderChange: PropTypes.func,
  function_order: PropTypes.object,
  handleFunctionOrderChange: PropTypes.func,
};
