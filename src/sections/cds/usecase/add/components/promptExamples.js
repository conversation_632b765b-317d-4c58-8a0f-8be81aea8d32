import React from 'react';
import { Box, Button, TextField, Typography, Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import PropTypes from 'prop-types';

const PromptExamples = ({
  promptExamplefields,
  handleAddPromptExampleField,
  handlePromptExampleFieldChange,
  handleDeletePromptExampleField,
  apiType,
}) => {
  return (
    <Box>
      <h4> Prompt Examples </h4>
      {promptExamplefields.map(field => (
        <Box key={field.id} sx={{ mb: 3, mt: 3, display: 'flex', justifyContent: 'space-between' , gap: 2}}
          
        > 

          <TextField
            name="title"
            label="Title"
            variant="outlined"
            fullWidth
            value={field.title}
            onChange={(e) => handlePromptExampleFieldChange(field.id, e)}
            required
          />
          <TextField
            name="description"
            label="Description"
            variant="outlined"
            fullWidth
            value={field.description}
            onChange={(e) => handlePromptExampleFieldChange(field.id, e)}
            required
          />
          <TextField
            name="prompt"
            label="Prompt"
            variant="outlined"
            fullWidth
            multiline
            rows={2}
            value={field.prompt}
            onChange={(e) => handlePromptExampleFieldChange(field.id, e)}
            required
          />
      
          <Button variant="contained" onClick={() => handleDeletePromptExampleField(field.id)} sx={{ mt: 0 , padding: '2px 8px', display: 'flex', justifySelf: 'right',  width: 'fit-content', height: 'fit-content', backgroundColor: 'red', color: 'white', minWidth: 'fit-content'}}>X</Button>
        </Box>
      ))}
      <Button variant="contained" onClick={handleAddPromptExampleField} sx={{ mt: 5 , display: 'flex', justifySelf: 'right'}}>
        Add  Field +
      </Button>
    </Box>
  );
};

PromptExamples.propTypes = {
  promptExamplefields: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
      prompt: PropTypes.string.isRequired,
    })
  ).isRequired,
  handleAddPromptExampleField: PropTypes.func.isRequired,
  handlePromptExampleFieldChange: PropTypes.func.isRequired,
  handleDeletePromptExampleField: PropTypes.func.isRequired,
};

export default PromptExamples;