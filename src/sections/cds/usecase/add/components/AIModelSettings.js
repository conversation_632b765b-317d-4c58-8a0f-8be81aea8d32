import React from "react";
import {
    Box,
    TextField,
    Slider,
    Typography,
    MenuItem,
    Grid,
    Input,
} from "@mui/material";
import PropTypes from "prop-types";
const dalleSizeData = [ 
    { dalle3: {'1024x1024': '1024x1024', '1792x1024': '1792x1024', '1024x1792': '1024x1792'}},
    { dalle2: {'256x256': '256x256', '512x512': '512x512', '1024x1024': '1024x1024'}}
];
const AIModelSettings = ({
    apiType,
    model,
    handleModelChange,
    maxTokens,
    handleMaxTokensChange,
    topP,
    handleTopPChange,
    temperature,
    handleTemperatureChange,
    frequencyPenalty,
    handleFrequencyPenaltyChange,
    presencePenalty,
    handlePresencePenaltyChange,
    dallESize,
    handleDallESizeChange,
}) => {
    return (
        <Box sx={{ mb: 3, mt: 5 }}> 
            {apiType !== 'dalle' && apiType !== 'heygen' && (
                <Grid container spacing={3}>
                    <Grid item xs={4}>
                    <TextField
                        select
                        label="OpenAI Model"
                        value={model}
                        onChange={handleModelChange}
                        fullWidth
                        variant="outlined"
                    >
                        <MenuItem value="gpt-3.5-turbo">GPT-3.5 Turbo</MenuItem>
                        <MenuItem value="gpt-4">GPT-4</MenuItem>
                    </TextField>
                    </Grid> 
                    <Grid item xs={4} sx={{ px: 2 }}>
                        <Grid
                            item
                            xs={12}
                            sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "space-between",
                                mb: 2,
                            }}
                        >
                            <Typography gutterBottom>Max Tokens</Typography>
                            <Input value={maxTokens} onChange={handleMaxTokensChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <Slider
                                value={maxTokens}
                                onChange={handleMaxTokensChange}
                                min={1}
                                max={4096}
                                valueLabelDisplay="auto"
                                getAriaValueText={() => `${maxTokens}`}
                            />
                        </Grid>
                    </Grid>
                    <Grid item xs={4}>
                        <Grid
                            item
                            xs={12}
                            sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "space-between",
                                mb: 2,
                            }}
                        >
                            <Typography gutterBottom>Top P</Typography>
                            <Input value={topP} onChange={handleTopPChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <Slider
                                value={topP}
                                onChange={handleTopPChange}
                                min={0}
                                max={1}
                                step={0.01}
                                valueLabelDisplay="auto"
                                getAriaValueText={() => `${topP}`}
                            />
                        </Grid>
                    </Grid>
                    <Grid item xs={4}>
                        <Grid
                            item
                            xs={12}
                            sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "space-between",
                                mb: 2,
                            }}
                        >
                            <Typography gutterBottom>Temperature</Typography>
                            <Input value={temperature} onChange={handleTemperatureChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <Slider
                                value={temperature}
                                onChange={handleTemperatureChange}
                                min={0}
                                max={2}
                                step={0.1}
                                valueLabelDisplay="auto"
                                getAriaValueText={() => `${temperature}`}
                            />
                        </Grid>
                    </Grid>
                    <Grid item xs={4}>
                        <Grid
                            item
                            xs={12}
                            sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "space-between",
                                mb: 2,
                            }}
                        >
                            <Typography gutterBottom>Frequency Penalty</Typography>
                            <Input
                                value={frequencyPenalty}
                                onChange={handleFrequencyPenaltyChange}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <Slider
                                value={frequencyPenalty}
                                onChange={handleFrequencyPenaltyChange}
                                min={0}
                                max={2}
                                step={0.1}
                                valueLabelDisplay="auto"
                                getAriaValueText={() => `${frequencyPenalty}`}
                            />
                        </Grid>
                    </Grid>
                    <Grid item xs={4}>
                        <Grid
                            item
                            xs={12}
                            sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "space-between",
                                mb: 2,
                            }}
                        >
                            <Typography gutterBottom>Presence Penalty </Typography>
                            <Input
                                value={presencePenalty}
                                onChange={handlePresencePenaltyChange}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <Slider
                                value={presencePenalty}
                                onChange={handlePresencePenaltyChange}
                                min={0}
                                getAriaValueText={() => `${presencePenalty}`}
                                max={2}
                                step={0.1}
                                valueLabelDisplay="auto"
                            />
                        </Grid>
                    </Grid>
                </Grid>
            )}
                
            {apiType === 'dalle' && (
                <Grid container spacing={3}>
                    <Grid item xs={4}>
                        <TextField
                            select
                            label="Dall-E Model"
                            value={model}
                            onChange={handleModelChange}
                            fullWidth
                            variant="outlined"
                        >
                            <MenuItem value="dall-e-3">Dall-E 3</MenuItem>
                            <MenuItem value="dall-e-2">Dall-E 2</MenuItem>
                        </TextField>
                    </Grid>
                    <Grid item xs={4}>
                        <TextField
                            select
                            label="Dall-E Size"
                            value={dallESize}
                            onChange={handleDallESizeChange}
                            fullWidth
                            variant="outlined"
                            >
                            {model === 'dall-e-2' && dalleSizeData[1].dalle2 && Object.entries(dalleSizeData[1].dalle2).map(([key, value]) => (
                                <MenuItem key={key} value={value}>{value}</MenuItem>
                            ))}
                            {model === 'dall-e-3' && dalleSizeData[0].dalle3 && Object.entries(dalleSizeData[0].dalle3).map(([key, value]) => (
                                <MenuItem key={key} value={value}>{value}</MenuItem>
                            ))}
                        </TextField>
                    </Grid>
                </Grid>
                 
            )} 
        </Box>
    );
};

AIModelSettings.propTypes = {
    model: PropTypes.string.isRequired,
    handleModelChange: PropTypes.func.isRequired,
    maxTokens: PropTypes.number.isRequired,
    handleMaxTokensChange: PropTypes.func.isRequired,
    topP: PropTypes.number.isRequired,
    handleTopPChange: PropTypes.func.isRequired,
    temperature: PropTypes.number.isRequired,
    handleTemperatureChange: PropTypes.func.isRequired,
    frequencyPenalty: PropTypes.number.isRequired,
    handleFrequencyPenaltyChange: PropTypes.func.isRequired,
    presencePenalty: PropTypes.number.isRequired,
    handlePresencePenaltyChange: PropTypes.func.isRequired,
    dallESize: PropTypes.string.isRequired,
    handleDallESizeChange: PropTypes.func.isRequired,
};

export default AIModelSettings;
