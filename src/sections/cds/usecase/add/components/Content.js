import {
  Autocomplete,
  Box,
  FormControlLabel,
  Grid,
  Slider,
  Stack,
  Switch,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import PropTypes from 'prop-types'; 
import React, { useState } from 'react';
import Repeater<PERSON>ormFields from './formFields';
import AIModelSettings from './AIModelSettings'; 
import PromptExamples from './promptExamples';

export default function Content({
  handleChangetitleEnglish,
  handleChangeDescription,
  description, 
  prompt,
  handleChangePrompt,
  customSystemPrompt,
  handleChangeCustomSystemPrompt,
  titleEnglish,  
  handleChangeUniqueId,
  uniqueId, 
  handleChangeUsecaseIcon,
  usecaseIcon,
  handleChangeApiType,
  apiType,
  repeaterFormFields,
  handleAddrepeaterFormField,
  handlerepeaterFormFieldChange,
  handleDeleterepeaterFormField,
  model,
  handleModelChange,
  maxTokens,
  handleMaxTokensChange,
  topP,
  handleTopPChange,
  temperature,
  handleTemperatureChange,
  frequencyPenalty,
  handleFrequencyPenaltyChange,
  presencePenalty,
  handlePresencePenaltyChange,
  assistant_name,
  handleChangeAssistantName,
  assistant_instructions,
  handleChangeAssistantInstructions,
  assistant_id,
  handleChangeAssistantId,
  file_id,
  handleChangeFileId,
  isChatModeActive,
  handleChangeChatModeActive,
  promptExamplefields,
  handleAddPromptExampleField,
  handlePromptExampleFieldChange,
  handleDeletePromptExampleField,
  dallESize,
  handleDallESizeChange,
}) {


  return (
    <Stack>
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}
      >
        <TextField
          id="title-english"
          label="Title"
          variant="outlined"
          value={titleEnglish}
          onChange={handleChangetitleEnglish}
        />

        <TextField
          id="description"
          label="Description"
          variant="outlined"
          value={description}
          multiline
          onChange={handleChangeDescription}
          rows={4}
          sx={{ mt: 2 }}
        />

        <Grid container spacing={{ xs: 2, md: 2, lg: 2 }}>
          <Grid item xs={12} sm={12} md={4} lg={4} sx={{ mt: 3 }}>
            <FormControl fullWidth>
              <InputLabel id="api-type-label">API Type</InputLabel>
              <Select
                labelId="api-type-label"
                value={apiType}
                onChange={handleChangeApiType}
                label="API Type"
              >
                <MenuItem value="completions">OpenAI Completions</MenuItem>
                <MenuItem value="assistants">OpenAI Assistants</MenuItem>
                <MenuItem value="stream">OpenAI Stream</MenuItem>
                <MenuItem value="dalle">DALL-E</MenuItem>
                <MenuItem value="heygen">HeyGen</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={12} md={4} lg={4} sx={{ mt: 3 }}>
            <TextField
              id="uniqueId"
              label="Unique ID"
              variant="outlined"
              value={uniqueId}
              onChange={handleChangeUniqueId} 
              fullWidth
            />
          </Grid>
          <Grid item xs={12} sm={12} md={4} lg={4} sx={{ mt: 3 }}>
          <TextField
              id="usecase_icon"
              label="Usecase Icon"
              variant="outlined"
              value={usecaseIcon}
              onChange={handleChangeUsecaseIcon}  
              fullWidth

            />
          </Grid>
        </Grid>
        <Grid container spacing={{ xs: 2, md: 2, lg: 2 }} sx={{ mt: 3 }}>
        {
            apiType === 'stream' && (
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={isChatModeActive}
                      onChange={handleChangeChatModeActive}
                      color="primary"
                    />
                  }
                  label="Chat Mode Active"
                  sx={{ mt: 6, ml: 2 }}
                />

                <PromptExamples
                  promptExamplefields={promptExamplefields}
                  handleAddPromptExampleField={handleAddPromptExampleField}
                  handlePromptExampleFieldChange={handlePromptExampleFieldChange}
                  handleDeletePromptExampleField={handleDeletePromptExampleField}
                />
              </Grid>
            )
        }
          {apiType !== 'assistants' && (
            <Grid item xs={12}>
              
              {
                apiType === 'stream' && isChatModeActive ? null : (
                  <RepeaterFormFields
                    fields={repeaterFormFields}
                    handleAddField={handleAddrepeaterFormField}
                    handleFieldChange={handlerepeaterFormFieldChange}
                    handleDeleteField={handleDeleterepeaterFormField}
                    apiType={apiType}
                  /> 
                )
              }
              <AIModelSettings
                apiType={apiType}
                model={model}
                handleModelChange={handleModelChange}
                maxTokens={maxTokens}
                handleMaxTokensChange={handleMaxTokensChange}
                topP={topP}
                handleTopPChange={handleTopPChange}
                temperature={temperature}
                handleTemperatureChange={handleTemperatureChange}
                frequencyPenalty={frequencyPenalty}
                handleFrequencyPenaltyChange={handleFrequencyPenaltyChange}
                presencePenalty={presencePenalty}
                handlePresencePenaltyChange={handlePresencePenaltyChange}
                dallESize={dallESize}
                handleDallESizeChange={handleDallESizeChange}
              />
              <TextField fullWidth
                id="prompt"
                label="Prompt"
                variant="outlined"
                value={prompt}
                multiline
                onChange={handleChangePrompt}
                rows={4}
                sx={{ mt: 2 }}
              />
            </Grid>
          )}
          {apiType === 'assistants' && (
            <Grid item xs={12}>
              <h5>Assistant</h5>
              <TextField fullWidth
                id="assistant_name"
                label="Assistant Name"
                variant="outlined"
                value={assistant_name}
                onChange={handleChangeAssistantName} 
              /> 
              <TextField fullWidth
                id="assistant_instructions"
                label="Assistant Instructions"
                variant="outlined"
                value={assistant_instructions}
                onChange={handleChangeAssistantInstructions}
                multiline
                rows={4}
                sx={{ mt: 3 }}
                />
                
                <TextField fullWidth
                  id="assistant_id"
                  label="Assistant ID"
                  variant="outlined"
                  value={assistant_id}
                  onChange={handleChangeAssistantId}
                  sx={{ mt: 2 }}
                />
 
                <TextField fullWidth
                  id="file_id"
                  label="File ID"
                  variant="outlined"
                  value={file_id}
                  onChange={handleChangeFileId}
                  sx={{ mt: 2 }}
                />

                <TextField
                    select
                    label="Assistant Model"
                    value={model}
                    onChange={handleModelChange}
                    fullWidth
                    variant="outlined"
                    sx={{ mt: 2 }}
                >
                    <MenuItem value="gpt-3.5-turbo">GPT-3.5 Turbo</MenuItem>
                    <MenuItem value="gpt-4">GPT-4</MenuItem>
                </TextField>
            </Grid>
          )}

          <Grid item xs={12}>
              <TextField fullWidth
                id="custom_system_prompt"
                label="Custom System Prompt"
                variant="outlined"
                value={customSystemPrompt}
                multiline
                onChange={handleChangeCustomSystemPrompt}
                rows={4}
                sx={{ mt: 2 }}
              />
          </Grid>
          
        
        </Grid>
        

        <Stack sx={{ mt: 2 }}>
          <Grid container spacing={{ xs: 2, md: 2, lg: 2 }}>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              
            </Grid>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              
            </Grid>
          </Grid>
        </Stack>

         
      </Box>

       
      <Stack sx={{ mt: 3, border: '1px solid #F1F2F5' }}>
        <Grid container>
         
        </Grid>
      </Stack>
    </Stack>
  );
}

Content.propTypes = {
  
  titleEnglish: PropTypes.string,
  description: PropTypes.string,
  prompt: PropTypes.string,
  handleChangePrompt: PropTypes.func,
  customSystemPrompt: PropTypes.string,
  handleChangeCustomSystemPrompt: PropTypes.func,
  apiType: PropTypes.string,
  uniqueId: PropTypes.string,
  usecaseIcon: PropTypes.string,
  handleChangeTitleEnglish: PropTypes.func,
  handleChangeDescription: PropTypes.func,
  handleChangeApiType: PropTypes.func,
  handleChangeUniqueId: PropTypes.func,
  handleChangeUsecaseIcon: PropTypes.func,
  repeaterFormFields: PropTypes.array,
  handleAddrepeaterFormField: PropTypes.func,
  handlerepeaterFormFieldChange: PropTypes.func,
  handleDeleterepeaterFormField: PropTypes.func,
  model: PropTypes.string,
  handleModelChange: PropTypes.func,
  maxTokens: PropTypes.number,
  handleMaxTokensChange: PropTypes.func,
  topP: PropTypes.number,
  handleTopPChange: PropTypes.func,
  temperature: PropTypes.number,
  handleTemperatureChange: PropTypes.func,
  frequencyPenalty: PropTypes.number,
  handleFrequencyPenaltyChange: PropTypes.func,
  presencePenalty: PropTypes.number,
  handlePresencePenaltyChange: PropTypes.func,
  assistant_name: PropTypes.string,
  handleChangeAssistantName: PropTypes.func,
  assistant_instructions: PropTypes.string,
  handleChangeAssistantInstructions: PropTypes.func,
  assistant_id: PropTypes.string,
  handleChangeAssistantId: PropTypes.func,
  file_id: PropTypes.string,
  handleChangeFileId: PropTypes.func,
  isChatModeActive: PropTypes.bool,
  handleChangeChatModeActive: PropTypes.func,
  promptExamplefields: PropTypes.array,
  handleAddPromptExampleField: PropTypes.func,
  handlePromptExampleFieldChange: PropTypes.func,
  handleDeletePromptExampleField: PropTypes.func,
  dallESize: PropTypes.string,
  handleDallESizeChange: PropTypes.func,
};
