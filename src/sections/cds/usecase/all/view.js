/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */

import { Autocomplete, Button, Card, Grid, Pagination, Stack, TextField } from '@mui/material';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import { alpha } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { useCallback, useEffect, useState, useMemo } from 'react';
import { ReactSortable } from 'react-sortablejs';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useUsecases } from 'src/apis/useUsecases';
import Iconify from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';

export default function AllUsecasesView() {
  const apiTypes = {
    completions: 'Completions',
    assistants: 'Assistants',
    stream: 'Stream',
    'dall-e': 'Dall-E',
    heygen: 'HeyGen',
  };

  const settings = useSettingsContext();
  const { getUsecases, getFunctions, updateUsecaseOrder, loading, error } = useUsecases();

  const [Usecases, setUsecases] = useState([]);
  const [functions, setFunctions] = useState([]);
  const [selectedFunc, setSelectedFunc] = useState('');
  const [funcInputValue, setFuncInputValue] = useState('All');
  const [selectedApiType, setSelectedApiType] = useState('');
  const [ApiTypeInputValue, setApiTypeInputValue] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    totalPages: 1,
    hasNextPage: false,
    hasPrevPage: false,
    limit: 25,
  });

  const handleSortEnd = useCallback((updatedList, setUsecasesList) => {
    const reorderedList = updatedList.map((item, index) => ({
      ...item,
    }));
    setUsecasesList(reorderedList);
  }, []);

  const handleUpdateUsecases = useCallback(
    (usecases) => {
      updateUsecaseOrder(usecases)
        .then((response) => {
          if (response.status === 'success') {
            toast.success('Usecases updated successfully');
            window.location.reload();
          } else {
            toast.error('Error updating usecases');
          }
        })
        .catch((err) => {
          console.error('Error updating usecases:', err);
          toast.error('Error updating usecases');
        });
    },
    [updateUsecaseOrder]
  );

  const renderUsecaseList = (title, usecases) => (
    <Grid item xs={12} sm={12} md={12} lg={12}>
      <Card
        sx={{
          mt: 5,
          borderRadius: 2,
          flexGrow: 1,
          bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04),
          p: 2,
        }}
      >
        {usecases?.length > 0 ? (
          <ReactSortable list={usecases} setList={(list) => handleSortEnd(list, setUsecases)}>
            {usecases.map((usecase) => (
              <Box
                key={usecase._id}
                sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <Box
                  sx={{
                    minHeight: 40,
                    width: '90%',
                    background: '#FFFFFF',
                    boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                    mt: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    cursor: 'move',
                  }}
                >
                  <Stack
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      width: '100%',
                      px: 2,
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      {usecase.icon && (
                        <Box
                          component="img"
                          src={usecase.icon}
                          sx={{ width: 24, height: 24 }}
                          alt={usecase.title}
                        />
                      )}
                      <Typography variant="subtitle2">{usecase.title}</Typography>
                    </Box>
                    <Box
                      sx={{
                        width: 25,
                        height: 25,
                        border: (theme) => `dashed 1px ${theme.palette.divider}`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="subtitle2">
                        {}
                        {selectedFunc && usecase.function_order
                          ? usecase.function_order[selectedFunc]
                          : usecase.order}
                      </Typography>
                    </Box>
                  </Stack>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: 40,
                    width: '10%',
                    mt: 2,
                    cursor: 'pointer',
                  }}
                >
                  <Iconify icon="basil:edit-outline" />
                </Box>
              </Box>
            ))}
          </ReactSortable>
        ) : (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2">Usecase data not found</Typography>
          </Box>
        )}
        <Box sx={{ mt: 2 }}>
          <Button
            disabled={usecases?.length < 1}
            fullWidth
            variant="contained"
            onClick={() => handleUpdateUsecases(usecases)}
          >
            Update
          </Button>
        </Box>
      </Card>
    </Grid>
  );

  // Fetch usecases
  useEffect(() => {
    const fetchUsecases = async () => {
      try {
        const response = await getUsecases();
        if (response && response.data) {
          const useCaseData = response.data.useCases.map((usecase) => ({
            _id: usecase._id,
            title: usecase.title,
            order: usecase.usecase_order,
            slug: usecase.slug,
            function: usecase.function,
            api_type: usecase.api_type,
            function_order: usecase.function_order || {},
            icon: usecase.usecase_icon_url,
          }));
          setUsecases(useCaseData);
          console.log('Fetched usecases:', useCaseData);
          setPagination(response.data.pagination);
        } else {
          console.error('No usecases found in the response');
          setUsecases([]);
        }
      } catch (err) {
        console.error('Error fetching usecases:', err);
        setUsecases([]);
      }
    };

    fetchUsecases();
  }, [getUsecases]);

  const handlePageChange = async (event, newPage) => {
    try {
      const response = await getUsecases(newPage);
      if (response && response.data) {
        const useCaseData = response.data.useCases.map((usecase) => ({
          _id: usecase._id,
          title: usecase.title,
          order: usecase.usecase_order,
          slug: usecase.slug,
          function: usecase.function,
          api_type: usecase.api_type,
          function_order: usecase.function_order || {},
          icon: usecase.usecase_icon_url,
        }));
        setUsecases(useCaseData);
        console.log('page change:', useCaseData);
        setPagination(response.data.pagination);
      }
    } catch (err) {
      console.error('Error fetching usecases:', err);
      toast.error('Error loading page');
    }
  };

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Memoize the search function
  const searchUsecases = useCallback(async () => {
    if (!debouncedSearchTerm && !selectedApiType && !selectedFunc) {
      return;
    }

    try {
      const response = await getUsecases(
        pagination.page,
        debouncedSearchTerm,
        selectedApiType,
        selectedFunc
      );
      if (response && response.data) {
        const useCaseData = response.data.useCases.map((usecase) => ({
          _id: usecase._id,
          title: usecase.title,
          order: usecase.usecase_order,
          slug: usecase.slug,
          function: usecase.function,
          api_type: usecase.api_type,
          function_order: usecase.function_order || {},
          icon: usecase.usecase_icon_url,
        }));
        setUsecases(useCaseData);
        setPagination(response.data.pagination);
      }
    } catch (err) {
      console.error('Error fetching usecases:', err);
      toast.error('Error loading search results');
    }
  }, [debouncedSearchTerm, pagination.page, selectedApiType, selectedFunc, getUsecases]);

  // Effect to trigger search
  useEffect(() => {
    searchUsecases();
  }, [searchUsecases]);

  const handleApiTypeChange = async (newValue) => {
    setSelectedApiType(newValue);
    if (newValue === null) {
      setSelectedApiType('');
      newValue = '';
    }

    try {
      const response = await getUsecases(pagination.page, searchTerm, newValue);
      if (response && response.data) {
        const useCaseData = response.data.useCases.map((usecase) => ({
          _id: usecase._id,
          title: usecase.title,
          order: usecase.usecase_order,
          slug: usecase.slug,
          icon: usecase.usecase_icon_url,
        }));
        setUsecases(useCaseData);
        console.log('Api type change:', useCaseData);
        setPagination(response.data.pagination);
      }
    } catch (err) {
      console.error('Error fetching usecases:', err);
      toast.error('Error loading usecases');
    }
  };

  // Fetch functions when the component mounts
  useEffect(() => {
    const fetchFunctions = async () => {
      try {
        const response = await getFunctions();

        // Map the response data to match the expected structure
        const functionsData = response.map((func) => ({
          _id: func._id, // Include _id if needed
          slug: func.slug,
          title: func.translations.en,
        }));
        setFunctions(functionsData);
      } catch (err) {
        console.error('Error fetching functions:', err);
        toast.error('Error loading functions');
      }
    };
    fetchFunctions();
  }, [getFunctions]);

  // Add a new handler for function changes
  const handleFunctionChange = async (newValue) => {
    const functionSlug = newValue?.slug || '';
    setSelectedFunc(newValue?.slug || '');
    setFuncInputValue(newValue?.title || 'All');
    try {
      const response = await getUsecases(1, searchTerm, selectedApiType, functionSlug);

      if (response && response.data) {
        const useCaseData = response.data.useCases.map((usecase) => ({
          _id: usecase._id,
          title: usecase.title,
          order: usecase.usecase_order,
          slug: usecase.slug,
          icon: usecase.usecase_icon_url,
          function_order: usecase.function_order || {},
        }));
        setUsecases(useCaseData);
        console.log('Function change:', useCaseData);
        setPagination(response.data.pagination);
      }
    } catch (err) {
      console.error('Error fetching usecases:', err);
      toast.error('Error loading usecases');
    }
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={12} md={12} lg={12}>
          <Card
            sx={{
              mt: 5,
              borderRadius: 2,
              flexGrow: 1,
              bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04),
              p: 2,
            }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
            >
              <Typography variant="h4">Usecases ({pagination.total})</Typography>
              {loading && <Typography>Loading...</Typography>}
              {error && <Typography color="error">Error: {error.message}</Typography>}

              <Stack direction="row" spacing={2}>
                <Autocomplete
                  value={selectedFunc}
                  onChange={(event, newValue) => handleFunctionChange(newValue)}
                  inputValue={funcInputValue}
                  options={functions}
                  getOptionLabel={(option) => option.title || 'All'}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Function"
                      variant="outlined"
                      sx={{ minWidth: 200 }}
                    />
                  )}
                  sx={{ minWidth: 200 }}
                />

                <Autocomplete
                  value={selectedApiType}
                  onChange={(event, newValue) => {
                    handleApiTypeChange(newValue);
                  }}
                  inputValue={ApiTypeInputValue}
                  onInputChange={(event, newInputValue) => {
                    setApiTypeInputValue(newInputValue);
                  }}
                  options={Object.keys(apiTypes)}
                  getOptionLabel={(option) => apiTypes[option] || 'All'}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select API Type"
                      variant="outlined"
                      sx={{ minWidth: 200 }}
                    />
                  )}
                  sx={{ minWidth: 200 }}
                />

                <TextField
                  fullWidth
                  variant="outlined"
                  label="Search Usecases"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </Stack>
            </Stack>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={2}>
        {renderUsecaseList('Usecases List', Usecases)}
      </Grid>

      <Stack spacing={12} sx={{ my: 1, display: 'flex', alignItems: 'center' }}>
        <Pagination
          count={pagination.totalPages}
          page={pagination.page}
          onChange={handlePageChange}
          color="primary"
          showFirstButton
          showLastButton
          disabled={loading}
        />
        <Typography variant="body2" color="text.secondary">
          Page {pagination.page} of {pagination.totalPages}
        </Typography>
      </Stack>
    </Container>
  );
}
