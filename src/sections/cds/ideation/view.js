// @mui
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';

import {
  alpha,
  Box,
  Button,
  Card,
  IconButton,
  Modal,
  Pagination,
  Stack,
  TextField,
} from '@mui/material';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { useEffect, useState } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useIdeations } from 'src/apis/useIdeations';

import DeleteConfirmationModal from 'src/components/DeleteConfirmationModal';
import { useSettingsContext } from 'src/components/settings';
// ----------------------------------------------------------------------

export default function IdeationView() {
  const settings = useSettingsContext();
  const { getIdeations, addIdeation, updateIdeation, deleteIdeation } = useIdeations();
  const [localIdeations, setLocalIdeations] = useState([]);

  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [page, setPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const [openModal, setOpenModal] = useState(false);
  const [editingIdeation, setEditingIdeation] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [editSlug, setEditSlug] = useState('');

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [ideationToDelete, setIdeationToDelete] = useState(null);

  const handleChangeTitle = (e) => {
    setTitle(e.target.value);
  };

  const handleChangeSlug = (e) => {
    setSlug(e.target.value);
  };

  const handleSave = () => {
    if (title.length === 0) {
      toast.error('Title cannot be empty!');
    } else {
      addIdeation(title, slug).then((data) => {
        if (data && data.title === title) {
          getIdeations().then((newData) => {
            setLocalIdeations(newData);
          });
          toast.success('Ideation added successfully!');
          setTitle('');
          setSlug('');
        } else {
          toast.error('Ideation could not be added!');
        }
      });
    }
  };

  const handleDelete = (id) => {
    deleteIdeation(id).then((res) => {
      if (res) {
        getIdeations().then((newData) => {
          setLocalIdeations(newData);
        });
        toast.success('Ideation deleted successfully!');
      } else {
        toast.error('Ideation could not be deleted!');
      }
    });
  };

  const handleOpenModal = (ideation) => {
    setEditingIdeation(ideation);
    setEditTitle(ideation.title);
    setEditSlug(ideation.slug);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingIdeation(null);
    setEditTitle('');
    setEditSlug('');
  };

  const handleEdit = () => {
    if (!editingIdeation) return;

    updateIdeation(editingIdeation._id, editTitle, editSlug).then(() => {
      getIdeations().then((newData) => {
        setLocalIdeations(newData);
      });
      handleCloseModal();
    });
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  const indexOfLastItem = page * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = localIdeations?.slice(indexOfFirstItem, indexOfLastItem);
  const pageCount = Math.ceil((localIdeations?.length || 0) / itemsPerPage);

  const modalStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
    borderRadius: 2,
  };

  useEffect(() => {
    getIdeations().then((data) => {
      setLocalIdeations(data);
    });
  }, []);

  const handleCopy = (id) => {
    navigator.clipboard.writeText(id);
    toast.success('ID copied!', {
      position: 'top-right',
    });
  };

  const handleDeleteClick = (ideation) => {
    setIdeationToDelete(ideation);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (ideationToDelete) {
      handleDelete(ideationToDelete._id);
      setDeleteModalOpen(false);
      setIdeationToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setIdeationToDelete(null);
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4"> Ideation </Typography>

      <Stack sx={{ mt: 5, borderRadius: 2, flexGrow: 1 }}>
        <Grid container spacing={{ xs: 2, md: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
          <Grid item xs={12} sm={12} md={5} lg={5}>
            <Card sx={{ bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04) }}>
              <Box sx={{ m: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                <Typography component="h6" variant="h6" sx={{ mt: 2, mb: 2 }}>
                  Add new
                </Typography>

                <TextField
                  label="Title"
                  variant="outlined"
                  fullWidth
                  value={title}
                  onChange={handleChangeTitle}
                />
                <TextField
                  label="Slug"
                  variant="outlined"
                  fullWidth
                  value={slug}
                  onChange={handleChangeSlug}
                  sx={{ mt: 2 }}
                />

                <Button variant="contained" sx={{ mt: 2 }} onClick={handleSave}>
                  Save
                </Button>
              </Box>
            </Card>
          </Grid>
          <Grid item xs={12} sm={12} md={7} lg={7}>
            <Card sx={{ bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04) }}>
              <Box sx={{ m: 1, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                <Typography component="h6" variant="h6" sx={{ m: 2 }}>
                  All ideation projects
                </Typography>
                {currentItems?.map((ideation) => (
                  <Card
                    key={ideation._id}
                    sx={{
                      m: 1,
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      justifyContent: 'space-between',
                    }}
                  >
                    <Typography key={ideation.id} component="div" variant="button">
                      {ideation.title}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <IconButton onClick={() => handleCopy(ideation._id)}>
                        <ContentCopyIcon />
                      </IconButton>
                      <IconButton onClick={() => handleOpenModal(ideation)}>
                        <EditIcon />
                      </IconButton>
                      <IconButton onClick={() => handleDeleteClick(ideation)}>
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </Card>
                ))}

                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, mb: 2 }}>
                  <Pagination
                    count={pageCount}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                    size="large"
                  />
                </Box>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </Stack>

      <Modal open={openModal} onClose={handleCloseModal} aria-labelledby="edit-ideation-modal">
        <Box sx={modalStyle}>
          <Typography variant="h6" component="h2" sx={{ mb: 2 }}>
            Edit
          </Typography>

          <TextField
            label="Title"
            variant="outlined"
            fullWidth
            value={editTitle}
            onChange={(e) => setEditTitle(e.target.value)}
            sx={{ mb: 2 }}
          />

          <TextField
            label="Slug"
            variant="outlined"
            fullWidth
            value={editSlug}
            onChange={(e) => setEditSlug(e.target.value)}
            sx={{ mb: 3 }}
          />

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button variant="outlined" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button variant="contained" onClick={handleEdit}>
              Save
            </Button>
          </Box>
        </Box>
      </Modal>

      <DeleteConfirmationModal
        open={deleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Project"
        description={`Are you sure you want to delete the "${ideationToDelete?.title}" project?`}
      />
    </Container>
  );
}
