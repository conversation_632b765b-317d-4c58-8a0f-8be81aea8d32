/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable import/no-extraneous-dependencies */
// @mui
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { LoadingButton } from '@mui/lab';
import {
  alpha,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Stack,
  Tab,
  Tabs,
  TextField,
} from '@mui/material';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { useEffect, useState } from 'react';
import { ReactSortable } from 'react-sortablejs';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAITranslator } from 'src/apis/useAITranslator';
import { useOnboarding } from 'src/apis/useOnboarding';

// components
import DeleteConfirmationModal from 'src/components/DeleteConfirmationModal';
import { useSettingsContext } from 'src/components/settings';
import TranslateConfirmationModal from 'src/components/TranslateConfirmationModal';

// ----------------------------------------------------------------------

export default function JobRoleView() {
  const settings = useSettingsContext();
  const { addJobRole, getJobRole, deleteJobRole, updateJobRoleOrder, editJobRole } =
    useOnboarding();
  const { getPlatformLanguages, translate, platformLanguages } = useAITranslator();

  const [jobRoleDescription, setJobRoleDescription] = useState(null);
  const [jobRoleSlug, setJobRoleSlug] = useState(null);
  const [jobRoles, setJobRoles] = useState(null);
  const [translations, setTranslations] = useState({});
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editItem, setEditItem] = useState(null);
  const [editTranslations, setEditTranslations] = useState({});
  const [editDescription, setEditDescription] = useState('');
  const [editSlug, setEditSlug] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [openTranslateModal, setOpenTranslateModal] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);

  const handleChangeJobRoleDescription = (e) => {
    const newValue = e.target.value;
    setTranslations((prev) => ({
      ...prev,
      [`${selectedLanguage}_description`]: newValue,
    }));
    setFunctionDescription(newValue);
  };
  const handleChangeJobRoleSlug = (e) => {
    setJobRoleSlug(e.target.value);
  };
  const addNewJobRole = () => {
    const hasAnyTranslation = Object.values(translations).some((value) => value.trim() !== '');

    if (hasAnyTranslation) {
      addJobRole(jobRoleDescription, jobRoleSlug, translations).then((res) => {
        if (res.status === 'success') {
          setTranslations({});
          setJobRoleDescription(null);
          setJobRoleSlug(null);
          toast.success('Job Role added successfully!', {
            position: 'top-right',
          });

          getAllJobRoles();
        } else {
          toast.error(`${res.message}`, {
            position: 'top-right',
          });
        }
      });
    } else {
      toast.error('Please enter a name !', {
        position: 'top-right',
      });
    }
  };

  useEffect(() => {
    getAllJobRoles();
  }, []);

  const getAllJobRoles = () => {
    getJobRole().then((allJobRoles) => {
      const updatedJobRoles = allJobRoles
        .map((item) => ({
          ...item,
          description: item.description || '-',
        }))
        .sort((a, b) => (a.order || 0) - (b.order || 0));

      setJobRoles(updatedJobRoles);
    });
  };

  useEffect(() => {
    getPlatformLanguages();
  }, []);

  const handleLanguageChange = (event, newValue) => {
    setSelectedLanguage(newValue);
  };

  const handleTranslationChange = (langCode, value) => {
    setTranslations((prev) => ({
      ...prev,
      [langCode]: value,
    }));
  };

  const handleTranslateClick = () => {
    if (!translations.en) {
      toast.error('Please enter English name first!', {
        position: 'top-right',
      });
      return;
    }
    setOpenTranslateModal(true);
  };

  const handleConfirmTranslate = async () => {
    try {
      setIsTranslating(true);
      const newTranslations = { ...translations };

      const translationPromises = platformLanguages
        .filter((lang) => lang.lang !== 'en')
        .map(async (lang) => {
          const formData = [
            {
              name: translations.en,
              description: jobRoleDescription || '',
            },
          ];

          const response = await translate(formData, 'en', lang.lang);
          return {
            lang: lang.lang,
            name: response?.data?.name || '',
            description: response?.data?.description || '',
          };
        });

      const results = await Promise.all(translationPromises);

      results.forEach(({ lang, name, description }) => {
        if (name) newTranslations[lang] = name;
        if (description) newTranslations[`${lang}_description`] = description;
      });

      setTranslations(newTranslations);
      setIsTranslating(false);
      setOpenTranslateModal(false);

      toast.success('Translations completed!', {
        position: 'top-right',
      });
    } catch (error) {
      setIsTranslating(false);
      setOpenTranslateModal(false);
      console.error('Translation error:', error);
      toast.error(`Translation failed: ${error.message}`, {
        position: 'top-right',
      });
    }
  };

  const handleSort = (newState) => {
    // Yeni sıralamayı order numaralarıyla güncelle
    const reorderedList = newState.map((item, index) => ({
      ...item,
      order: index + 1,
    }));

    setJobRoles(reorderedList);
  };

  const handleSaveOrder = () => {
    updateJobRoleOrder(jobRoles).then((res) => {
      if (res.status === 'success') {
        getAllJobRoles();
        toast.success('Order saved successfully!', {
          position: 'top-right',
        });
      } else {
        toast.error(`${res.message}`, {
          position: 'top-right',
        });
      }
    });
  };

  const handleEditClick = (item) => {
    setEditItem(item);
    setEditTranslations({
      [selectedLanguage]: item.translations?.[selectedLanguage] || '',
    });
    setEditDescription(item.translations?.[`${selectedLanguage}_description`] || '');
    setEditSlug(item.slug || '');
    setEditModalOpen(true);
  };

  const handleEditClose = () => {
    setEditModalOpen(false);
    setEditItem(null);
    setEditTranslations({});
    setEditDescription('');
    setEditSlug('');
  };

  const handleEditSave = async () => {
    try {
      const updatedTranslations = {
        ...editItem.translations,
        [selectedLanguage]: editTranslations[selectedLanguage],
        [`${selectedLanguage}_description`]: editDescription,
      };

      await editJobRole(editItem._id, editDescription, editSlug, updatedTranslations);

      toast.success('Successfully updated!');
      handleEditClose();
      getAllJobRoles();
    } catch (error) {
      toast.error(`Update failed: ${error.message}`);
    }
  };

  const handleEditTranslationChange = (lang, value) => {
    setEditTranslations((prev) => ({
      ...prev,
      [lang]: value,
    }));
  };

  const handleDeleteClick = (item) => {
    setItemToDelete(item);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (itemToDelete) {
      deleteJobRole(itemToDelete._id).then(() => {
        getAllJobRoles();
        toast.success('Job Role deleted successfully!', {
          position: 'top-right',
        });
      });
    }
    setDeleteModalOpen(false);
    setItemToDelete(null);
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setItemToDelete(null);
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4"> Job Role </Typography>

      <Stack
        sx={{
          mt: 5,
          borderRadius: 2,
          flexGrow: 1,
        }}
      >
        <Box
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
          }}
        >
          <Tabs value={selectedLanguage} onChange={handleLanguageChange}>
            {platformLanguages?.map((lang) => (
              <Tab key={lang.lang} label={lang.name} value={lang.lang} />
            ))}
          </Tabs>
          <LoadingButton
            variant="contained"
            onClick={handleTranslateClick}
            loading={isTranslating}
            loadingIndicator="Translating..."
            disabled={!translations.en && !translations.en?.description}
          >
            Translate
          </LoadingButton>
        </Box>
        <Grid
          container
          spacing={{ xs: 2, md: 3 }}
          columns={{ xs: 4, sm: 8, md: 12 }}
          sx={{ pt: 5 }}
        >
          <Grid item xs={12} sm={12} md={5} lg={5}>
            <Card sx={{ bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04), minHeight: 370 }}>
              <Box sx={{ m: 2, display: 'flex', justifyContent: 'space-between' }}>
                <Typography component="h6" variant="h6">
                  Add new
                </Typography>
              </Box>
              <Box sx={{ m: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                {platformLanguages?.map((language) => (
                  <Box
                    key={language.lang}
                    role="tabpanel"
                    hidden={selectedLanguage !== language.lang}
                  >
                    {selectedLanguage === language.lang && (
                      <TextField
                        id={`function-name-${language.lang}`}
                        label={`Name ${language.name}`}
                        variant="outlined"
                        value={translations[language.lang] || ''}
                        onChange={(e) => handleTranslationChange(language.lang, e.target.value)}
                        sx={{ mt: 2 }}
                        fullWidth
                      />
                    )}
                  </Box>
                ))}

                <TextField
                  id="jobRoles-slug"
                  label="Slug"
                  variant="outlined"
                  onChange={handleChangeJobRoleSlug}
                  sx={{ mt: 2 }}
                  fullWidth
                />
                <TextField
                  id="functions-description"
                  label="Description"
                  variant="outlined"
                  value={translations[`${selectedLanguage}_description`] || ''}
                  multiline
                  rows={6}
                  onChange={handleChangeJobRoleDescription}
                  fullWidth
                  sx={{ mt: 2 }}
                />
                <Button variant="contained" sx={{ mt: 2 }} onClick={addNewJobRole}>
                  Add
                </Button>
              </Box>
            </Card>
          </Grid>
          <Grid item xs={12} sm={12} md={7} lg={7}>
            <Card sx={{ bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04) }}>
              <Typography component="h6" variant="h6" sx={{ m: 2 }}>
                All Job Roles
              </Typography>
              <Box sx={{ m: 2, flexGrow: 1 }}>
                {jobRoles && jobRoles.length > 0 ? (
                  <ReactSortable
                    list={jobRoles}
                    setList={handleSort}
                    animation={200}
                    delayOnTouchStart
                    delay={2}
                    style={{ minHeight: 320 }}
                  >
                    {jobRoles.map((item) => (
                      <Box
                        key={item._id}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          p: 2,
                          mb: 1,
                          borderRadius: 1,
                          bgcolor: 'background.paper',
                          boxShadow: '0 0 5px rgba(0,0,0,0.1)',
                          cursor: 'move',
                          '&:hover': {
                            bgcolor: 'action.hover',
                          },
                        }}
                      >
                        <Grid container spacing={2} alignItems="center">
                          {/* Order numarası */}
                          <Grid item xs={1}>
                            <Typography variant="subtitle2" color="text.secondary">
                              {item.order}
                            </Typography>
                          </Grid>

                          {/* Seçili dildeki çeviri */}
                          <Grid item xs={4}>
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                {
                                  platformLanguages?.find((lang) => lang.lang === selectedLanguage)
                                    ?.name
                                }
                              </Typography>
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {item.translations?.[selectedLanguage] || '-'}
                              </Typography>
                            </Box>
                          </Grid>

                          {/* Description */}
                          <Grid item xs={3}>
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Description
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  mt: 1,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                }}
                              >
                                {item.translations?.[`${selectedLanguage}_description`] || '-'}
                              </Typography>
                            </Box>
                          </Grid>

                          {/* Slug alanı */}
                          <Grid item xs={2}>
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Slug
                              </Typography>
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {item.slug || '-'}
                              </Typography>
                            </Box>
                          </Grid>

                          {/* İşlem butonları */}
                          <Grid item xs={2}>
                            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                              <IconButton onClick={() => handleEditClick(item)}>
                                <EditIcon />
                              </IconButton>
                              <IconButton onClick={() => handleDeleteClick(item)}>
                                <DeleteIcon />
                              </IconButton>
                            </Box>
                          </Grid>
                        </Grid>
                      </Box>
                    ))}
                  </ReactSortable>
                ) : (
                  <Box
                    sx={{
                      minHeight: 320,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="subtitle1" color="text.secondary">
                      No data available
                    </Typography>
                  </Box>
                )}
              </Box>
              <Button fullWidth variant="contained" sx={{ mb: 2 }} onClick={handleSaveOrder}>
                Save Order
              </Button>
            </Card>
          </Grid>
        </Grid>
      </Stack>

      <Dialog open={editModalOpen} onClose={handleEditClose} maxWidth="md" fullWidth>
        <DialogTitle>Edit Function</DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <TextField
            label={`Name (${
              platformLanguages?.find((lang) => lang.lang === selectedLanguage)?.name
            })`}
            value={editTranslations[selectedLanguage] || ''}
            onChange={(e) => handleEditTranslationChange(selectedLanguage, e.target.value)}
            fullWidth
            sx={{ mb: 2, mt: 2 }}
          />

          <TextField
            label="Slug"
            value={editSlug}
            onChange={(e) => setEditSlug(e.target.value)}
            fullWidth
            sx={{ mb: 2 }}
          />

          <TextField
            label="Description"
            value={editDescription}
            onChange={(e) => setEditDescription(e.target.value)}
            multiline
            rows={4}
            fullWidth
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleEditClose}>Cancel</Button>
          <Button
            onClick={handleEditSave}
            variant="contained"
            disabled={!editTranslations[selectedLanguage]}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <TranslateConfirmationModal
        open={openTranslateModal}
        onClose={() => setOpenTranslateModal(false)}
        onConfirm={handleConfirmTranslate}
      />

      <DeleteConfirmationModal
        open={deleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Function"
        description="Are you sure you want to delete this function? This action cannot be undone."
      />
    </Container>
  );
}
