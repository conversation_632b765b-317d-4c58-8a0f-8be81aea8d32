/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable import/no-extraneous-dependencies */
// @mui
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  alpha,
  Button,
  Card,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Switch,
  Tab,
  Tabs,
  TextField,
} from '@mui/material';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { useEffect, useState } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useJourneys } from 'src/apis/useJourneys';

import { useAITranslator } from 'src/apis/useAITranslator';

// components
import { LoadingButton } from '@mui/lab';
import { ReactSortable } from 'react-sortablejs';
import DeleteConfirmationModal from 'src/components/DeleteConfirmationModal';
import { useSettingsContext } from 'src/components/settings';
import TranslateConfirmationModal from 'src/components/TranslateConfirmationModal';
import EditLevelModal from './EditLevelModal';
import ModalContentEditor from './ModalContentEditor';
import { useOnboarding } from 'src/apis/useOnboarding';

// ----------------------------------------------------------------------

export default function LevelsView() {
  const settings = useSettingsContext();
  const { addLevel, getLevels, deleteLevel, updateLevelOrder, editLevel } = useOnboarding();
  const { getPlatformLanguages, translate, platformLanguages } = useAITranslator();

  const [levelDescription, setLevelDescription] = useState(null);
  const [levelSlug, setLevelSlug] = useState(null);
  const [levels, setLevels] = useState(null);
  const [translations, setTranslations] = useState({});
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [selectedLevel, setSelectedLevel] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editItem, setEditItem] = useState(null);
  const [editTranslations, setEditTranslations] = useState({});
  const [editDescription, setEditDescription] = useState('');
  const [editSlug, setEditSlug] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [openTranslateModal, setOpenTranslateModal] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [modalContent, setModalContent] = useState(['']);
  const [isLastLevel, setIsLastLevel] = useState(false);
  const [nextLevel, setNextLevel] = useState('');

  const handleChangeLevelDescription = (e) => {
    const newValue = e.target.value;
    setTranslations((prev) => ({
      ...prev,
      [`${selectedLanguage}_description`]: newValue,
    }));
    setLevelDescription(newValue);
  };
  const handleChangeLevelSlug = (e) => {
    setLevelSlug(e.target.value);
  };

  const addNewLevel = () => {
    const hasAnyTranslation = Object.values(translations).some((value) => value.trim() !== '');

    if (hasAnyTranslation) {
      const formattedModalContent = modalContent.map((content) => content);
      console.log('selectedLevel:', selectedLevel);
      addLevel(
        levelDescription,
        selectedLevel,
        isLastLevel,
        formattedModalContent,
        levelSlug,
        translations
      ).then((res) => {
        if (res.status === 'success') {
          setTranslations({});
          setLevelDescription(null);
          setLevelSlug(null);
          setModalContent(['']);
          toast.success('Level added successfully!', {
            position: 'top-right',
          });

          getAllLevels();
        } else {
          toast.error(`${res.message}`, {
            position: 'top-right',
          });
        }
      });
    } else {
      toast.error('Please enter a name!', {
        position: 'top-right',
      });
    }
  };

  useEffect(() => {
    getAllLevels();
  }, []);

  const getAllLevels = () => {
    getLevels().then((allLevels) => {
      const updatedLevels = allLevels?.map((item) => ({
        ...item,
        description: item.description || '-',
      }));
      setLevels(updatedLevels);
    });
  };

  useEffect(() => {
    getPlatformLanguages();
  }, []);

  const handleLanguageChange = (event, newValue) => {
    setSelectedLanguage(newValue);
  };
  const handleLevelChange = (event) => {
    const selectedLevelId = event.target.value;
    setSelectedLevel(selectedLevelId);
  };
  const handleTranslationChange = (langCode, value) => {
    setTranslations((prev) => ({
      ...prev,
      [langCode]: value,
    }));
  };

  const handleTranslateClick = () => {
    if (!translations.en) {
      toast.error('Please enter English name first!', {
        position: 'top-right',
      });
      return;
    }
    setOpenTranslateModal(true);
  };

  const handleConfirmTranslate = async () => {
    try {
      setIsTranslating(true);
      const newTranslations = { ...translations };

      const translationPromises = platformLanguages
        .filter((lang) => lang.lang !== 'en')
        .map(async (lang) => {
          const formData = [
            {
              name: translations.en,
              description: levelDescription || '',
              content: modalContent,
            },
          ];

          const response = await translate(formData, 'en', lang.lang);

          return {
            lang: lang.lang,
            name: response?.data?.[0]?.name || '',
            description: response?.data?.[0]?.description || '',
            content: response?.data?.[0]?.content || '',
          };
        });

      const results = await Promise.all(translationPromises);

      results.forEach(({ lang, name, description, content }) => {
        if (name) newTranslations[lang] = name;
        if (description) newTranslations[`${lang}_description`] = description;
        if (content) newTranslations[`${lang}_content`] = content;
      });

      setTranslations(newTranslations);
      setIsTranslating(false);
      setOpenTranslateModal(false);

      toast.success('Translations completed!', {
        position: 'top-right',
      });
    } catch (error) {
      setIsTranslating(false);
      setOpenTranslateModal(false);
      console.error('Translation error:', error);
      toast.error(`Translation failed: ${error.message}`, {
        position: 'top-right',
      });
    }
  };

  const handleSort = (newState) => {
    // Yeni sıralamayı order numaralarıyla güncelle
    const reorderedList = newState.map((item, index) => ({
      ...item,
      order: index + 1,
    }));

    setLevels(reorderedList);
  };
  const handleSaveOrder = () => {
    updateLevelOrder(levels).then((res) => {
      if (res.status === 'success') {
        getAllLevels();
        toast.success('Order saved successfully!', {
          position: 'top-right',
        });
      } else {
        toast.error(`${res.message}`, {
          position: 'top-right',
        });
      }
    });
  };
  const handleEditClick = (item) => {
    setEditItem(item);
    setEditTranslations({
      [selectedLanguage]: item.translations?.[selectedLanguage] || '',
    });
    setEditDescription(item.translations?.[`${selectedLanguage}_description`] || '');
    setEditSlug(item.slug || '');
    setNextLevel(item.nextLevel || '');
    setIsLastLevel(item.isLastLevel || false);
    setModalContent(item.modalContent || ['']);
    setEditModalOpen(true);
  };

  const handleEditClose = () => {
    setEditModalOpen(false);
    setEditItem(null);
    setEditTranslations({});
    setEditDescription('');
    setEditSlug('');
  };

  const handleEditSave = async () => {
    try {
      const updatedTranslations = {
        ...editItem.translations,
        [selectedLanguage]: editTranslations[selectedLanguage],
        [`${selectedLanguage}_description`]: editDescription,
      };

      const finalNextLevel = isLastLevel ? '' : nextLevel;

      const formattedModalContent = modalContent.map((content) => content);

      await editLevel(
        editItem._id,
        editDescription,
        editSlug,
        updatedTranslations,
        finalNextLevel,
        isLastLevel,
        formattedModalContent
      );

      toast.success('Successfully updated!');
      handleEditClose();
      getAllLevels();
    } catch (error) {
      toast.error(`Update failed: ${error.message}`);
    }
  };

  const handleEditTranslationChange = (lang, value) => {
    setEditTranslations((prev) => ({
      ...prev,
      [lang]: value,
    }));
  };

  const handleDeleteClick = (item) => {
    setItemToDelete(item);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (itemToDelete) {
      deleteLevel(itemToDelete._id).then(() => {
        getAllLevels();
        toast.success('Level deleted successfully!', {
          position: 'top-right',
        });
      });
    }
    setDeleteModalOpen(false);
    setItemToDelete(null);
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setItemToDelete(null);
  };

  const handleAddModalContent = () => {
    setModalContent([...modalContent, '']);
  };

  const handleModalContentChange = (index, value) => {
    const updatedContent = [...modalContent];
    updatedContent[index] = value;
    setModalContent(updatedContent);

    setTranslations((prev) => ({
      ...prev,
      [`${selectedLanguage}_content`]: updatedContent,
    }));
  };

  const handleDeleteModalContent = (index) => {
    const updatedContent = modalContent.filter((_, i) => i !== index);
    setModalContent(updatedContent);
  };

  const handleLastLevelChange = (event) => {
    setIsLastLevel(event.target.checked);
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4"> Levels </Typography>

      <Stack sx={{ mt: 5, borderRadius: 2, flexGrow: 1 }}>
        <Box
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
          }}
        >
          <Tabs value={selectedLanguage} onChange={handleLanguageChange}>
            {platformLanguages?.map((lang) => (
              <Tab key={lang.lang} label={lang.name} value={lang.lang} />
            ))}
          </Tabs>
          <LoadingButton
            variant="contained"
            onClick={handleTranslateClick}
            loading={isTranslating}
            loadingIndicator="Translating..."
            disabled={!translations.en && !translations.en?.description}
          >
            Translate
          </LoadingButton>
        </Box>

        <Grid container spacing={{ xs: 2, md: 3, lg: 3 }} columns={{ xs: 12, sm: 8, md: 12 }}>
          <Grid item xs={12} sm={12} md={5} lg={5}>
            <Card sx={{ bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04), minHeight: 370 }}>
              <Typography component="h6" variant="h6" sx={{ m: 2 }}>
                Add new
              </Typography>
              <Box sx={{ m: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                {platformLanguages?.map((language) => (
                  <Box
                    key={language.lang}
                    role="tabpanel"
                    hidden={selectedLanguage !== language.lang}
                  >
                    {selectedLanguage === language.lang && (
                      <>
                        <TextField
                          id={`function-name-${language.lang}`}
                          label={`Name ${language.name}`}
                          variant="outlined"
                          value={translations[language.lang] || ''}
                          onChange={(e) => handleTranslationChange(language.lang, e.target.value)}
                          sx={{ mt: 2 }}
                          fullWidth
                        />
                        <TextField
                          id="functions-slug"
                          label="Slug"
                          variant="outlined"
                          onChange={handleChangeLevelSlug}
                          sx={{ mt: 2 }}
                          fullWidth
                        />
                        <TextField
                          id="functions-description"
                          label="Description"
                          variant="outlined"
                          value={translations[`${language.lang}_description`] || ''}
                          multiline
                          rows={6}
                          onChange={handleChangeLevelDescription}
                          fullWidth
                          sx={{ mt: 2 }}
                        />
                        <FormControl fullWidth sx={{ mt: 2 }}>
                          <InputLabel>Next Level</InputLabel>
                          <Select
                            value={selectedLevel || ''}
                            onChange={handleLevelChange}
                            label="Next Level"
                          >
                            {levels?.map((level) => (
                              <MenuItem
                                key={level._id}
                                value={
                                  level.translations?.[selectedLanguage] ||
                                  level.translations?.['en'] ||
                                  '-'
                                }
                              >
                                {level.translations?.[selectedLanguage] ||
                                  level.translations?.['en'] ||
                                  '-'}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>

                        <FormControlLabel
                          control={
                            <Switch
                              checked={isLastLevel}
                              onChange={handleLastLevelChange}
                              color="primary"
                            />
                          }
                          label="Is Last Level"
                          sx={{ mt: 2, mb: 1 }}
                        />

                        {modalContent.map((page, index) => (
                          <ModalContentEditor
                            key={index}
                            content={translations[`${selectedLanguage}_content`]?.[index] || page}
                            onChange={handleModalContentChange}
                            onDelete={handleDeleteModalContent}
                            index={index}
                          />
                        ))}
                      </>
                    )}
                  </Box>
                ))}

                <Button
                  variant="outlined"
                  onClick={handleAddModalContent}
                  fullWidth
                  sx={{ mt: 2, mb: 2 }}
                >
                  Add new content
                </Button>
                <Button variant="contained" sx={{ mt: 2 }} onClick={addNewLevel}>
                  Add
                </Button>
              </Box>
            </Card>
          </Grid>

          <Grid item xs={12} sm={12} md={7} lg={5}>
            <Card sx={{ bgcolor: (theme) => alpha(theme.palette.grey[500], 0.04) }}>
              <Typography component="h6" variant="h6" sx={{ m: 2 }}>
                All Levels
              </Typography>
              <Box sx={{ m: 2, flexGrow: 1 }}>
                {levels && levels.length > 0 ? (
                  <ReactSortable
                    list={levels}
                    setList={handleSort}
                    animation={200}
                    delayOnTouchStart
                    delay={2}
                    style={{ minHeight: 320 }}
                  >
                    {levels.map((item) => (
                      <Box
                        key={item._id}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          p: 2,
                          mb: 1,
                          borderRadius: 1,
                          bgcolor: 'background.paper',
                          boxShadow: '0 0 5px rgba(0,0,0,0.1)',
                          cursor: 'move',
                          '&:hover': {
                            bgcolor: 'action.hover',
                          },
                        }}
                      >
                        <Grid container spacing={2} alignItems="center">
                          {/* Order numarası */}
                          <Grid item xs={1}>
                            <Typography variant="subtitle2" color="text.secondary">
                              {item.order}
                            </Typography>
                          </Grid>

                          {/* Seçili dildeki çeviri */}
                          <Grid item xs={4}>
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                {
                                  platformLanguages?.find((lang) => lang.lang === selectedLanguage)
                                    ?.name
                                }
                              </Typography>
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {item.translations?.[selectedLanguage] || '-'}
                              </Typography>
                            </Box>
                          </Grid>

                          {/* Description */}
                          <Grid item xs={3}>
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Description
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  mt: 1,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                }}
                              >
                                {item.translations?.[`${selectedLanguage}_description`] || '-'}
                              </Typography>
                            </Box>
                          </Grid>

                          {/* Slug alanı */}
                          <Grid item xs={2}>
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Slug
                              </Typography>
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {item.slug || '-'}
                              </Typography>
                            </Box>
                          </Grid>

                          {/* İşlem butonları */}
                          <Grid item xs={2}>
                            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                              <IconButton onClick={() => handleEditClick(item)}>
                                <EditIcon />
                              </IconButton>
                              <IconButton onClick={() => handleDeleteClick(item)}>
                                <DeleteIcon />
                              </IconButton>
                            </Box>
                          </Grid>
                        </Grid>
                      </Box>
                    ))}
                  </ReactSortable>
                ) : (
                  <Box
                    sx={{
                      minHeight: 320,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="subtitle1" color="text.secondary">
                      No data available
                    </Typography>
                  </Box>
                )}
              </Box>
              <Button fullWidth variant="contained" sx={{ mb: 2 }} onClick={handleSaveOrder}>
                Save Order
              </Button>
            </Card>
          </Grid>
        </Grid>
      </Stack>

      <EditLevelModal
        open={editModalOpen}
        onClose={handleEditClose}
        onSave={handleEditSave}
        editTranslations={editTranslations}
        selectedLanguage={selectedLanguage}
        editSlug={editSlug}
        editDescription={editDescription}
        onTranslationChange={handleEditTranslationChange}
        onSlugChange={(value) => setEditSlug(value)}
        onDescriptionChange={(value) => setEditDescription(value)}
        platformLanguages={platformLanguages}
        modalContent={modalContent}
        setModalContent={setModalContent}
        levels={levels}
        nextLevel={nextLevel}
        setNextLevel={setNextLevel}
        isLastLevel={isLastLevel}
        setIsLastLevel={setIsLastLevel}
      />

      <TranslateConfirmationModal
        open={openTranslateModal}
        onClose={() => setOpenTranslateModal(false)}
        onConfirm={handleConfirmTranslate}
      />

      <DeleteConfirmationModal
        open={deleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Level"
        description="Are you sure you want to delete this level? This action cannot be undone."
      />
    </Container>
  );
}
