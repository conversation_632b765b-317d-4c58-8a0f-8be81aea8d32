import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
} from '@mui/material';
import ModalContentEditor from './ModalContentEditor';

export default function EditLevelModal({
  open,
  onClose,
  onSave,
  editTranslations,
  selectedLanguage,
  editSlug,
  editDescription,
  onTranslationChange,
  onSlugChange,
  onDescriptionChange,
  platformLanguages,
  modalContent,
  setModalContent,
  levels,
  nextLevel,
  setNextLevel,
  isLastLevel,
  setIsLastLevel,
}) {
  const handleAddModalContent = () => {
    setModalContent([...modalContent, '']);
  };

  const handleModalContentChange = (index, value) => {
    const updatedContent = [...modalContent];
    updatedContent[index] = value;
    setModalContent(updatedContent);
  };

  const handleDeleteModalContent = (index) => {
    const updatedContent = modalContent.filter((_, i) => i !== index);
    setModalContent(updatedContent);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Edit Level</DialogTitle>
      <DialogContent sx={{ mt: 2 }}>
        <TextField
          label={`Name (${
            platformLanguages?.find((lang) => lang.lang === selectedLanguage)?.name
          })`}
          value={editTranslations[selectedLanguage] || ''}
          onChange={(e) => onTranslationChange(selectedLanguage, e.target.value)}
          fullWidth
          sx={{ mb: 2, mt: 2 }}
        />

        <TextField
          label="Slug"
          value={editSlug}
          onChange={(e) => onSlugChange(e.target.value)}
          fullWidth
          sx={{ mb: 2 }}
        />

        <TextField
          label="Description"
          value={editDescription}
          onChange={(e) => onDescriptionChange(e.target.value)}
          multiline
          rows={4}
          fullWidth
          sx={{ mb: 2 }}
        />

        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Next Level</InputLabel>
          <Select
            value={nextLevel || ''}
            onChange={(e) => setNextLevel(e.target.value)}
            label="Next Level"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {levels?.map((level) => (
              <MenuItem key={level._id} value={level._id}>
                {level.translations?.[selectedLanguage] || level.translations?.['en'] || '-'}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControlLabel
          control={
            <Switch
              checked={isLastLevel}
              onChange={(e) => setIsLastLevel(e.target.checked)}
              color="primary"
            />
          }
          label="Is Last Level"
          sx={{ mb: 2 }}
        />

        <Box sx={{ mb: 2 }}>
          {modalContent.map((content, index) => (
            <ModalContentEditor
              key={index}
              content={content}
              onChange={handleModalContentChange}
              onDelete={handleDeleteModalContent}
              index={index}
            />
          ))}
          <Button variant="outlined" onClick={handleAddModalContent} fullWidth>
            Add New Content
          </Button>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={onSave} variant="contained" disabled={!editTranslations[selectedLanguage]}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}
