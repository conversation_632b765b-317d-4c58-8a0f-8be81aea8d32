import DeleteIcon from '@mui/icons-material/Delete';
import { Box, IconButton } from '@mui/material';
import BundledEditor from 'src/components/text-editor/editor';

export default function ModalContentEditor({ content, onChange, index, onDelete }) {
  const handleEditorChange = (content) => {
    onChange(index, content);
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <IconButton onClick={() => onDelete(index)} color="error" size="small">
          <DeleteIcon />
        </IconButton>
      </Box>
      <Box
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 1,
          overflow: 'hidden',
        }}
      >
        <BundledEditor
          value={content}
          onEditorChange={handleEditorChange}
          init={{
            height: 320,
            menubar: false,
            plugins: [
              'advlist',
              'anchor',
              'autolink',
              'image',
              'media',
              'link',
              'lists',
              'searchreplace',
              'codesample',
              'table',
              'wordcount',
            ],
            toolbar:
              'undo redo | blocks | ' +
              'bold italic forecolor | codesample | alignleft aligncenter ' +
              'alignright alignjustify | bullist numlist outdent indent | ' +
              'removeformat | image | media ',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            base_url: '/tinymce',
            skin: 'oxide',
            skin_url: '/tinymce/skins/ui/oxide',
            content_css: '/tinymce/skins/content/default/content.css',
            touch_events: true,
            browser_spellcheck: true,
            event_options: {
              passive: true,
            },
            promotion: false,
            license_key: 'gpl',
          }}
        />
      </Box>
    </Box>
  );
}
