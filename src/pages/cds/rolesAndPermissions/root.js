import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import RolesAndPermissionsView from 'src/sections/cds/rolesAndPermissions/view';

// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Roles and Permissions </title>
      </Helmet>

      <RolesAndPermissionsView />
    </>
  );
}
