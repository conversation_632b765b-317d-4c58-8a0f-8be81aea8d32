import { Helmet } from 'react-helmet-async';

import { useTranslate } from 'src/locales';

import { Container, Typography } from '@mui/material';

// ----------------------------------------------------------------------

export default function LMSPage() {
  const { t } = useTranslate();

  return (
    <>
      <Helmet>
        <title>{t('lms.title')}</title>
      </Helmet>

      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 5 }}>
          {t('lms.title')}
        </Typography>
      </Container>
    </>
  );
}
