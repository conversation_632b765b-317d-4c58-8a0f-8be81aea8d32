import { Helmet } from 'react-helmet-async';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useLocation } from 'react-router-dom';
import { Container, Typography } from '@mui/material';

import { useQuizzes } from 'src/apis';
import { useTranslate } from 'src/locales';
import { paths } from 'src/routes/paths';

import QuizNewEditForm from 'src/sections/cds/lms/quizes/quiz-new-edit-form';

// ----------------------------------------------------------------------

export default function QuizEditPage() {
  const { t } = useTranslate();
  const navigate = useNavigate();
  const { id } = useParams();

  const { getQuizById, updateQuiz, quiz, loading } = useQuizzes();

  const [loadingSave, setLoadingSave] = useState(false);

  useEffect(() => {
    if (id) {
      getQuizById(id);
    }
  }, [getQuizById, id]);

  // API'den gelen veriyi form formatına dönüştür
  const formatQuizForForm = (quiz) => {
    if (!quiz) return null;

    const lang = useLocation().search.split('lang=')[1];
    let localizedQuiz = quiz.translations.en;
    if (lang == undefined || lang == 'en') {
      localizedQuiz = quiz.translations.en;
    } else {
      localizedQuiz = quiz.translations[lang];
    }
    const formattedQuiz = { ...localizedQuiz };

    if (quiz.questions) {
      formattedQuiz.questions = localizedQuiz.questions.map((question) => {
        // Temel soru yapısı
        const formattedQuestion = {
          question: question.question,
          type: question.type,
          points: question.points || 10,
        };

        // Soru tipine göre options yapısını oluştur
        if (question.type === 'matching' && question.pairs) {
          // Matching soru tipi için pairs dizisini options dizisine dönüştür
          formattedQuestion.options = question.pairs.map((pair) => ({
            text: pair.left,
            match: pair.right,
            isCorrect: true,
            explanation: '',
          }));
        } else if (question.type === 'matching' && question.options) {
          formattedQuestion.options = question.options.map((opt) => ({
            text: opt.text,
            match: opt.match,
            isCorrect: true,
            explanation: '',
          }));
        } else if (
          (question.type === 'multiple-choice' ||
            question.type === 'single-choice' ||
            question.type === 'multiple-selection') &&
          question.options
        ) {
          formattedQuestion.options = question.options.map((opt) => {
            return {
              text: opt.option || opt.text || '',
              isCorrect: opt.correct || opt.isCorrect || false,
              explanation: opt.explanation || '',
            };
          });
        } else if (question.type === 'true-false' && question.correctAnswer !== undefined) {
          // True-false için options dizisini oluştur

          formattedQuestion.options = [
            { text: 'True', isCorrect: question.correctAnswer === true, explanation: '' },
            { text: 'False', isCorrect: question.correctAnswer === false, explanation: '' },
          ];
        } else if (question.type === 'fill-in-the-blank' && question.correctAnswer) {
          // Fill-in-the-blank için options dizisini oluştur
          formattedQuestion.options = [
            { text: question.correctAnswer, isCorrect: true, explanation: '' },
          ];
        } else if (Array.isArray(question.options) && question.options.length > 0) {
          // Options yapısını kontrol et ve gerekirse dönüştür
          if (question.options[0].option || question.options[0].text) {
            formattedQuestion.options = question.options.map((opt) => ({
              text: opt.option || opt.text || '',
              isCorrect: opt.correct || opt.isCorrect || false,
              explanation: opt.explanation || '',
            }));
          } else {
            formattedQuestion.options = question.options;
          }
        } else {
          // Varsayılan options dizisi
          formattedQuestion.options = [
            { text: '', isCorrect: false, explanation: '' },
            { text: '', isCorrect: false, explanation: '' },
          ];
        }

        return formattedQuestion;
      });
    }
    return formattedQuiz;
  };

  const handleUpdateQuiz = useCallback(
    async (data) => {
      setLoadingSave(true);

      try {
        await updateQuiz(id, data);
        toast.success(t('lms.quizzes.updateSuccess'));
        //navigate(paths.cds.lms.quizzes.root);
      } catch (error) {
        console.error(error);
        toast.error(t('lms.quizzes.updateError'));
      } finally {
        setLoadingSave(false);
      }
    },
    [id, navigate, t, updateQuiz]
  );

  // API'den gelen veriyi form formatına dönüştür
  const formattedQuiz = quiz ? formatQuizForForm(quiz) : null;

  return (
    <>
      <Helmet>
        <title>{t('lms.quizzes.edit')}</title>
      </Helmet>

      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 5 }}>
          {t('lms.quizzes.edit')}
        </Typography>

        {formattedQuiz && (
          <QuizNewEditForm
            isEdit
            currentQuiz={formattedQuiz}
            loadingSave={loadingSave || loading}
            onSave={handleUpdateQuiz}
          />
        )}
      </Container>
    </>
  );
}
