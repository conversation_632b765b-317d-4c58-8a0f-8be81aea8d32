import { Helmet } from 'react-helmet-async';
import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { Container, Typography } from '@mui/material';

import { useQuizzes } from 'src/apis';
import { useTranslate } from 'src/locales';
import { paths } from 'src/routes/paths';

import QuizNewEditForm from 'src/sections/cds/lms/quizes/quiz-new-edit-form';

// ----------------------------------------------------------------------

export default function QuizCreatePage() {
  const { t } = useTranslate();
  const navigate = useNavigate();

  const { createQuiz } = useQuizzes();

  const [loadingSave, setLoadingSave] = useState(false);

  const handleCreateQuiz = useCallback(
    async (data) => {
      setLoadingSave(true);

      try {
        await createQuiz(data);
        toast.success(t('lms.quizzes.createSuccess'));
        navigate(paths.cds.lms.quizzes.root);
      } catch (error) {
        console.error(error);
        toast.error(t('lms.quizzes.createError'));
      } finally {
        setLoadingSave(false);
      }
    },
    [createQuiz, navigate, t]
  );

  return (
    <>
      <Helmet>
        <title>{t('lms.quizzes.create')}</title>
      </Helmet>

      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 5 }}>
          {t('lms.quizzes.create')}
        </Typography>

        <QuizNewEditForm isEdit={false} loadingSave={loadingSave} onSave={handleCreateQuiz} />
      </Container>
    </>
  );
}
