import { Helmet } from 'react-helmet-async';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { Box, Card, Chip, Container, Divider, Grid, Stack, Typography } from '@mui/material';

import { useQuizzes } from 'src/apis';
import { useTranslate } from 'src/locales';
import { fDate } from 'src/utils/format-time';

// ----------------------------------------------------------------------

export default function QuizViewPage() {
  const { t } = useTranslate();
  const { id } = useParams();

  const { getQuizById, quiz, loading } = useQuizzes();

  useEffect(() => {
    if (id) {
      getQuizById(id);
    }
  }, [getQuizById, id]);

  if (!quiz) {
    return null;
  }

  const { title, description, category, questions, timeLimit, passingScore, createdAt } = quiz;

  return (
    <>
      <Helmet>
        <title>{title}</title>
      </Helmet>

      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 5 }}>
          {title}
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                {t('lms.quizzes.details')}
              </Typography>

              <Stack spacing={2}>
                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {t('lms.quizzes.category')}:
                  </Typography>
                  <Chip label={category} variant="soft" color="primary" size="small" />
                </Stack>

                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {t('lms.quizzes.timeLimit')}:
                  </Typography>
                  <Typography variant="body2">{timeLimit} min</Typography>
                </Stack>

                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {t('lms.quizzes.passingScore')}:
                  </Typography>
                  <Typography variant="body2">{passingScore}%</Typography>
                </Stack>

                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {t('lms.quizzes.questions')}:
                  </Typography>
                  <Typography variant="body2">{questions.length}</Typography>
                </Stack>

                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {t('lms.quizzes.createdAt')}:
                  </Typography>
                  <Typography variant="body2">{fDate(createdAt)}</Typography>
                </Stack>
              </Stack>

              <Divider sx={{ my: 3 }} />

              <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                {t('lms.quizzes.description')}:
              </Typography>
              <Typography variant="body2">{description}</Typography>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                {t('lms.quizzes.questions')}
              </Typography>

              <Stack spacing={3}>
                {questions.map((question, index) => (
                  <Box key={index} sx={{ p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>
                      {index + 1}. {question.question}
                    </Typography>

                    <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
                      {t('lms.quizzes.questionType')}:{' '}
                      {t(`lms.questionTypes.${question.type.replace('-', '')}`)}
                    </Typography>

                    <Stack spacing={1} sx={{ mt: 2 }}>
                      {question.type === 'matching' &&
                        question.pairs &&
                        question.pairs.map((pair, pairIndex) => (
                          <Box
                            key={pairIndex}
                            sx={{
                              p: 1.5,
                              borderRadius: 1,
                              bgcolor: 'background.paper',
                              border: (theme) => `1px solid ${theme.palette.divider}`,
                              display: 'flex',
                              justifyContent: 'space-between',
                            }}
                          >
                            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                              {String.fromCharCode(65 + pairIndex)}. {pair.left}
                            </Typography>
                            <Typography variant="body2">➔ {pair.right}</Typography>
                          </Box>
                        ))}

                      {question.type === 'true-false' && question.correctAnswer !== undefined && (
                        <>
                          <Box
                            sx={{
                              p: 1.5,
                              borderRadius: 1,
                              bgcolor: question.correctAnswer
                                ? 'success.lighter'
                                : 'background.paper',
                              border: (theme) => `1px solid ${theme.palette.divider}`,
                            }}
                          >
                            <Typography variant="body2">A. True</Typography>
                          </Box>
                          <Box
                            sx={{
                              p: 1.5,
                              borderRadius: 1,
                              bgcolor: !question.correctAnswer
                                ? 'success.lighter'
                                : 'background.paper',
                              border: (theme) => `1px solid ${theme.palette.divider}`,
                            }}
                          >
                            <Typography variant="body2">B. False</Typography>
                          </Box>
                        </>
                      )}

                      {question.type === 'fill-in-the-blank' && question.correctAnswer && (
                        <Box
                          sx={{
                            p: 1.5,
                            borderRadius: 1,
                            bgcolor: 'success.lighter',
                            border: (theme) => `1px solid ${theme.palette.divider}`,
                          }}
                        >
                          <Typography variant="body2">
                            {t('lms.quizzes.correctAnswer')}: {question.correctAnswer}
                          </Typography>
                        </Box>
                      )}

                      {(question.type === 'multiple-choice' ||
                        question.type === 'single-choice' ||
                        question.type === 'multiple-selection') &&
                        question.options &&
                        question.options.map((option, optIndex) => (
                          <Box
                            key={optIndex}
                            sx={{
                              p: 1.5,
                              borderRadius: 1,
                              bgcolor: option.correct ? 'success.lighter' : 'background.paper',
                              border: (theme) => `1px solid ${theme.palette.divider}`,
                            }}
                          >
                            <Typography variant="body2">
                              {String.fromCharCode(65 + optIndex)}. {option.option}
                            </Typography>
                            {option.explanation && (
                              <Typography
                                variant="caption"
                                sx={{ color: 'text.secondary', display: 'block', mt: 0.5 }}
                              >
                                {t('lms.quizzes.explanation')}: {option.explanation}
                              </Typography>
                            )}
                          </Box>
                        ))}

                      {!question.options &&
                        !question.pairs &&
                        question.correctAnswer === undefined && (
                          <Typography variant="body2" color="error">
                            {t('lms.quizzes.noOptionsAvailable')}
                          </Typography>
                        )}
                    </Stack>
                  </Box>
                ))}
              </Stack>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </>
  );
}
