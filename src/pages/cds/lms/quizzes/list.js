import { Helmet } from 'react-helmet-async';
import { useEffect, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  <PERSON><PERSON>,
  Card,
  Container,
  Grid,
  InputAdornment,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material';

import { useQuizzes } from 'src/apis';
import { useTranslate } from 'src/locales';
import { paths } from 'src/routes/paths';
import Iconify from 'src/components/iconify';
import QuizCard from 'src/sections/cds/lms/quizes/quiz-card';

// ----------------------------------------------------------------------

export default function QuizListPage() {
  const { t } = useTranslate();

  const {
    getQuizzes,
    deleteQuiz,
    translateQuiz,
    getQuizTranslation,
    updateQuizTranslation,
    quizzes,
    loading,
  } = useQuizzes();

  const [filterName, setFilterName] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');

  // Dil seçenekleri listesi
  const languageOptions = [
    { code: 'en', label: 'English' },
    { code: 'es', label: 'Spanish' },
    { code: 'fr', label: 'French' },
    { code: 'de', label: 'German' },
    { code: 'it', label: 'Italian' },
    { code: 'pt', label: 'Portuguese' },
    { code: 'ru', label: 'Russian' },
    { code: 'zh-Hans', label: 'Chinese (Simplified)' },
    { code: 'ja', label: 'Japanese' },
    { code: 'ko', label: 'Korean' },
    { code: 'ar', label: 'Arabic' },
    { code: 'tr', label: 'Turkish' },
  ];

  useEffect(() => {
    getQuizzes();
  }, [getQuizzes]);

  const dataFiltered = applyFilter({
    inputData: quizzes || [],
    filterName,
    filterCategory,
  });

  const handleFilterName = (event) => {
    setFilterName(event.target.value);
  };

  const handleFilterCategory = (event) => {
    setFilterCategory(event.target.value);
  };

  const handleDeleteQuiz = async (id) => {
    try {
      await deleteQuiz(id);
      toast.success(t('lms.quizzes.deleteSuccess'));
    } catch (error) {
      console.error(error);
      toast.error(t('lms.quizzes.deleteError'));
    }
  };

  // Geliştirilmiş çeviri işlemi yönetim fonksiyonu
  const handleTranslate = async (quiz, language = null) => {
    try {
      // Eğer bir dil parametresi gönderilmişse, bu mevcut çeviriyi düzenlemek içindir
      if (language) {
        // Önce mevcut çeviriyi getir
        const translationResult = await getQuizTranslation(quiz._id, language);

        if (translationResult.success) {
          // Mevcut çeviri bulundu, düzenlemek için geri döndür
          return {
            success: true,
            data: translationResult.data,
            isExistingTranslation: true,
            language,
          };
        } else {
          // Eğer bu dilde çeviri yoksa, yeni bir çeviri başlat
          toast.info(
            t(
              'lms.quizzes.translationNotFound',
              'Bu dilde çeviri bulunamadı. Yeni çeviri oluşturuluyor.'
            )
          );

          // Yeni çeviri oluştur
          return await createNewTranslation(quiz, language);
        }
      } else {
        // Dil parametresi yoksa, kullanıcı genel çeviri butonuna tıklamıştır
        // Çeviri modalı açılır ve kullanıcı bir dil seçer
        return { success: true, showLanguageSelector: true };
      }
    } catch (error) {
      console.error('Çeviri işlemi hatası:', error);
      toast.error(t('lms.quizzes.translateError', 'Çeviri işlemi sırasında bir hata oluştu'));
      throw error;
    }
  };

  // Yeni çeviri oluşturma fonksiyonu
  const createNewTranslation = async (quiz, targetLanguage) => {
    try {
      const result = await translateQuiz(quiz, targetLanguage);

      if (result.success) {
        await getQuizzes();
      } else {
        toast.error(t('lms.quizzes.translateError', 'Çeviri sırasında bir hata oluştu'));
      }

      return result;
    } catch (error) {
      console.error('Çeviri oluşturma hatası:', error);
      toast.error(t('lms.quizzes.translateError', 'Çeviri oluşturulurken bir hata oluştu'));
      throw error;
    }
  };

  // Çeviri güncelleme fonksiyonu
  const handleUpdateTranslation = async (quizId, language, translationData) => {
    try {
      const result = await updateQuizTranslation(quizId, language, translationData);

      if (result.success) {
        toast.success(t('lms.quizzes.updateTranslationSuccess', 'Çeviri başarıyla güncellendi'));
        // Değişiklikleri yansıtmak için quizleri yeniden getir
        await getQuizzes();
      } else {
        toast.error(
          t('lms.quizzes.updateTranslationError', 'Çeviri güncellenirken bir hata oluştu')
        );
      }

      return result;
    } catch (error) {
      console.error('Çeviri güncelleme hatası:', error);
      toast.error(t('lms.quizzes.updateTranslationError', 'Çeviri güncellenirken bir hata oluştu'));
      throw error;
    }
  };

  const CATEGORY_OPTIONS = [
    { value: 'all', label: t('common.all') },
    { value: 'Programming', label: t('lms.categories.programming') },
    { value: 'Data Science', label: t('lms.categories.dataScience') },
    { value: 'AI', label: t('lms.categories.ai') },
    { value: 'Business Skills', label: t('lms.categories.businessSkills') },
    { value: 'Language', label: t('lms.categories.language') },
  ];

  return (
    <>
      <Helmet>
        <title>{t('lms.quizzes.list')}</title>
      </Helmet>

      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 3 }}>
          {t('lms.quizzes.list')}
        </Typography>

        <Button
          component={RouterLink}
          to={paths.cds.lms.quizzes.add}
          variant="contained"
          startIcon={<Iconify icon="eva:plus-fill" />}
          sx={{ mb: 3 }}
        >
          {t('lms.quizzes.addNew')}
        </Button>

        <Card sx={{ p: 3, mb: 3 }}>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems={{ sm: 'center' }}>
            <TextField
              fullWidth
              value={filterName}
              onChange={handleFilterName}
              placeholder={t('lms.quizzes.searchPlaceholder')}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              fullWidth
              select
              value={filterCategory}
              onChange={handleFilterCategory}
              SelectProps={{
                MenuProps: {
                  PaperProps: {
                    sx: { maxHeight: 220 },
                  },
                },
              }}
              sx={{
                maxWidth: { sm: 240 },
                textTransform: 'capitalize',
              }}
            >
              {CATEGORY_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Stack>
        </Card>

        <Grid container spacing={3}>
          {dataFiltered.map((quiz) => (
            <Grid key={quiz._id} item xs={12} sm={6} md={4}>
              <QuizCard
                quiz={quiz}
                onDelete={() => handleDeleteQuiz(quiz._id)}
                onTranslate={handleTranslate}
                onUpdateTranslation={handleUpdateTranslation}
                languageOptions={languageOptions}
                translations={quiz.translations}
              />
            </Grid>
          ))}

          {dataFiltered.length === 0 && (
            <Grid item xs={12}>
              <Card sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body1">
                  {filterName ? t('lms.quizzes.noSearchResults') : t('lms.quizzes.noQuizzes')}
                </Typography>
              </Card>
            </Grid>
          )}
        </Grid>
      </Container>
    </>
  );
}

// ----------------------------------------------------------------------

function applyFilter({ inputData, filterName, filterCategory }) {
  if (!inputData || !inputData.length) return [];

  let filteredData = [...inputData];

  if (filterName) {
    filteredData = filteredData.filter(
      (quiz) => quiz.title.toLowerCase().indexOf(filterName.toLowerCase()) !== -1
    );
  }

  if (filterCategory !== 'all') {
    filteredData = filteredData.filter((quiz) => quiz.category === filterCategory);
  }

  return filteredData;
}
