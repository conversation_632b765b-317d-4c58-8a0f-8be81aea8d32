import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import JourneyView from 'src/sections/cds/journey/view';

// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Journey</title>
      </Helmet>

      <JourneyView />
    </>
  );
}
