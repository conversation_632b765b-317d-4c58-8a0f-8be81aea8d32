import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import IdeationView from 'src/sections/cds/ideation/view';

// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Ideation</title>
      </Helmet>

      <IdeationView />
    </>
  );
}
