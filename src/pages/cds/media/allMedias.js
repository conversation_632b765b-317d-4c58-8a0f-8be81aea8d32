import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import AllMediasView from 'src/sections/cds/media/all/view';
// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | All Medias</title>
      </Helmet>

      <AllMediasView />
    </>
  );
}
