import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import MediaView from 'src/sections/cds/media/view';

// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Media</title>
      </Helmet>

      <MediaView />
    </>
  );
}
