import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import AddMediaView from 'src/sections/cds/media/add/view';
// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Add Media</title>
      </Helmet>

      <AddMediaView />
    </>
  );
}
