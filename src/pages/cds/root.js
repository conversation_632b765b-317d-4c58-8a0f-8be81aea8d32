import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import CDSView from 'src/sections/cds/view';
// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | CDS</title>
      </Helmet>

      <CDSView />
    </>
  );
}
