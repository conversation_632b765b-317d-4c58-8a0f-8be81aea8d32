import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import AllUsecasesView from 'src/sections/cds/usecase/all/view';
// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | All Usecases</title>
      </Helmet>

      <AllUsecasesView />
    </>
  );
}
