import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import AddUsecaseView from 'src/sections/cds/usecase/add/view';
// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Add Usecase</title>
      </Helmet>

      <AddUsecaseView />
    </>
  );
}
