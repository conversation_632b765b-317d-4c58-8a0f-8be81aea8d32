import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import UsecaseView from 'src/sections/cds/usecase/view';

// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Usecases </title>
      </Helmet>

      <UsecaseView />
    </>
  );
}
