import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import EditFormView from 'src/sections/cds/formBuilder/edit/view';
import { useLocation } from 'react-router-dom';

// ----------------------------------------------------------------------

export default function Page() {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const language = searchParams.get('lang');

  const metadata = {
    title: language
      ? `${CONFIG.site.name} | Edit Form (${language.toUpperCase()})`
      : `${CONFIG.site.name} | Edit Form`,
  };

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <EditFormView />
    </>
  );
}
