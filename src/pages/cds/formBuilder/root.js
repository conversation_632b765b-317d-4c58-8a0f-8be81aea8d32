import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import FormBuilderView from 'src/sections/cds/formBuilder/view';

// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Form Builder</title>
      </Helmet>

      <FormBuilderView />
    </>
  );
}
