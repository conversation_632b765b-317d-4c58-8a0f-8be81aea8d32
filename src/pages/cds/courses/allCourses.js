import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import AllCoursesView from 'src/sections/cds/lms/courses/all-courses-view';

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Tüm <PERSON></title>
      </Helmet>

      <AllCoursesView />
    </>
  );
}
