import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import CoursesView from 'src/sections/cds/lms/courses/view';

// sections

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Courses</title>
      </Helmet>

      <CoursesView />
    </>
  );
}
