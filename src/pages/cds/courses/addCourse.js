import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';
import AddCourseView from 'src/sections/cds/lms/courses/add-course-view';

// ----------------------------------------------------------------------
const metadata = { title: `${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title>{metadata.title} | Ku<PERSON></title>
      </Helmet>

      <AddCourseView />
    </>
  );
}
