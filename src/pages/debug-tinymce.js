import { Box, Button, Container, Typography } from '@mui/material';
import { useState } from 'react';
import Head from 'next/head';
import BundledEditor from 'src/components/text-editor/editor';
import DashboardLayout from 'src/layouts/dashboard';

// -----------------------------------------------------------------------------

DebugTinyMCEPage.getLayout = (page) => <DashboardLayout>{page}</DashboardLayout>;

// -----------------------------------------------------------------------------

export default function DebugTinyMCEPage() {
  const [content, setContent] = useState('<p>Metin girişi testi...</p>');
  const [editorVersion, setEditorVersion] = useState('');

  // TinyMCE versiyonunu göster
  const handleEditorInit = (evt, editor) => {
    if (window.tinymce) {
      setEditorVersion(window.tinymce.majorVersion + '.' + window.tinymce.minorVersion);
    }
  };

  // Dialog sorunlarını manuel olarak düzelt
  const fixDialogs = () => {
    if (window.fixTinyMCEDialogs) {
      window.fixTinyMCEDialogs();
      console.log('Dialog düzeltme fonksiyonu çağrıldı');
    } else {
      console.log('Dialog düzeltme fonksiyonu bulunamadı');
    }
  };

  // Özel ayarlarla TinyMCE'yi yeniden yapılandır
  const customInitObject = {
    height: 400,
    menubar: true,
    plugins: [
      'advlist',
      'anchor',
      'autolink',
      'help',
      'image',
      'link',
      'lists',
      'searchreplace',
      'table',
      'wordcount',
      'media',
      'code',
      'fullscreen',
    ],
    toolbar:
      'undo redo | blocks | bold italic forecolor | alignleft aligncenter ' +
      'alignright alignjustify | bullist numlist outdent indent | ' +
      'removeformat | image media | code fullscreen',

    // Dialog mod seçenekleri (bunu değiştirebilirsiniz)
    dialog_type: 'floating', // 'floating', 'modal' veya 'window' olabilir

    // Resim ve Medya özellikleri
    image_advtab: true,
    image_title: true,
    image_description: true,
    media_live_embeds: true,

    // Dialog için özel durumlar
    file_picker_callback: function (callback, value, meta) {
      console.log('File picker açıldı', meta.filetype);
    },

    // URL işleme
    convert_urls: false,
    relative_urls: false,

    // DOM ve olaylar
    browser_spellcheck: true,
    hidden_input: false,

    // Loglar
    init_instance_callback: function (editor) {
      console.log('Editor başlatıldı', editor);
    },

    // Kritik - Dialog iyileştirmeleri için
    setup: function (editor) {
      editor.on('OpenWindow', function (e) {
        console.log('Dialog açıldı', e);
        // 100ms sonra dialog düzeltmesini uygula
        setTimeout(fixDialogs, 100);
      });
    },
  };

  return (
    <>
      <Head>
        <title>TinyMCE Hata Ayıklama | AIBS</title>
      </Head>

      <Container>
        <Box sx={{ mb: 5, mt: 5 }}>
          <Typography variant="h4" gutterBottom>
            TinyMCE Dialog Sorunu Testi
          </Typography>

          <Typography variant="body1" paragraph>
            Bu sayfa, TinyMCE editörünün dialog pencerelerindeki input sorunlarını test etmek için
            oluşturulmuştur. "Resim Ekle" veya "Medya Ekle" düğmelerine tıklayarak açılan dialogta
            kaynak URL alanına veri girmeyi deneyin.
          </Typography>

          <Typography variant="body2" sx={{ color: 'primary.main', mb: 2 }}>
            TinyMCE Sürümü: {editorVersion || 'Yükleniyor...'}
          </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Button variant="contained" color="primary" onClick={fixDialogs} sx={{ mb: 2, mr: 2 }}>
            Dialog Düzeltmesini Manuel Çalıştır
          </Button>

          <Button
            variant="outlined"
            color="info"
            onClick={() => {
              if (window.tinymce && window.tinymce.activeEditor) {
                window.tinymce.activeEditor.execCommand('mceImage');
              }
            }}
            sx={{ mb: 2, mr: 2 }}
          >
            Resim Dialogunu Aç
          </Button>

          <Button
            variant="outlined"
            color="secondary"
            onClick={() => {
              if (window.tinymce && window.tinymce.activeEditor) {
                window.tinymce.activeEditor.execCommand('mceMedia');
              }
            }}
            sx={{ mb: 2 }}
          >
            Medya Dialogunu Aç
          </Button>
        </Box>

        <Box sx={{ mb: 5 }}>
          <BundledEditor
            value={content}
            onInit={handleEditorInit}
            onEditorChange={(newContent) => setContent(newContent)}
            init={customInitObject}
          />
        </Box>

        <Box sx={{ mb: 5 }}>
          <Typography variant="h6" gutterBottom>
            Çıktı İçeriği:
          </Typography>
          <Box
            sx={{
              p: 2,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1,
              bgcolor: 'background.paper',
              minHeight: 100,
            }}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </Box>
      </Container>
    </>
  );
}
