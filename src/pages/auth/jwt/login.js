import { Helmet } from 'react-helmet-async';
import { CONFIG } from 'src/config-global';

import { JwtLoginView } from 'src/sections/auth/jwt';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.site.name}` };

export default function LoginPage() {
  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <JwtLoginView />
    </>
  );
}
