import { useEffect, useState } from 'react';
import { paths } from 'src/routes/paths';

import { usePermissions } from 'src/apis/usePermissions';
import { useAuthContext } from 'src/auth/hooks';
import SvgColor from 'src/components/svg-color';
import { useTranslate } from 'src/locales';

const icon = (name) => (
  <SvgColor src={`/assets/icons/navbar/${name}.svg`} sx={{ width: 1, height: 1 }} />
);

const ICONS = {
  journey: icon('ic_journey'),
  usecase: icon('ic_usecase'),
  media: icon('ic_media'),
  fsm: icon('ic_fsm'),
  ideation: icon('ic_ideation'),

  onboarding: icon('ic_onboarding'),
  permissions: icon('ic_permissions'),
  auditlog: icon('ic_auditlog'),
  formBuilder: icon('ic_formBuilder'),
  lms: icon('ic_courses'),
};

export function useNavData() {
  const { user } = useAuthContext();
  const { t } = useTranslate();
  const { getPermissions } = usePermissions();
  const [navPermissions, setNavPermissions] = useState({});
  const [permissions, setPermissions] = useState();

  const [items, setItems] = useState([]);

  useEffect(() => {
    getPermissions(user?.role, user?.company).then((res) => {
      setNavPermissions(res.perms[0].nav);
      localStorage.setItem('permissions', JSON.stringify(res.perms[0]));
      setPermissions(res.perms[0]);
    });
  }, [getPermissions, user]);

  useEffect(() => {
    const newItems = [
      {
        subheader: t('navData.ContentDeliverySystem'),
        items: [
          {
            title: t('navData.AIJourney'),
            path: paths.cds.journey.root,
            icon: ICONS.journey,
            disabled: !navPermissions?.aiJourney,
            children: [
              {
                title: t('navData.AllItems'),
                path: paths.cds.journey.all,
                disabled: !permissions?.aiJourney?.all,
              },
              {
                title: t('navData.AddNew'),
                path: paths.cds.journey.add,
                disabled: !permissions?.aiJourney?.add,
              },
            ],
          },
          {
            title: t('navData.Onboarding'),
            path: paths.cds.onboarding.root,
            icon: ICONS.onboarding,
            disabled: !navPermissions?.onboarding,
            children: [
              {
                title: t('navData.Industry'),
                path: paths.cds.onboarding.industry,
                disabled: !permissions?.onboarding?.industry,
              },
              {
                title: t('navData.ManagementRole'),
                path: paths.cds.onboarding.managementRole,
                disabled: !permissions?.onboarding?.managementRoles,
              },
              {
                title: t('navData.Function'),
                path: paths.cds.onboarding.function,
                disabled: !permissions?.onboarding?.functions,
              },
              {
                title: t('navData.Levels'),
                path: paths.cds.onboarding.levels,
                disabled: !permissions?.onboarding?.levels,
              },
              {
                title: t('navData.JobRole'),
                path: paths.cds.onboarding.jobRole,
                disabled: !permissions?.onboarding?.jobRole,
              },
            ],
          },
          {
            title: t('navData.Media'),
            path: paths.cds.media.root,
            icon: ICONS.media,
            disabled: !navPermissions?.media,
            children: [
              {
                title: t('navData.AllMedias'),
                path: paths.cds.media.all,
                disabled: !permissions?.media?.all,
              },
              {
                title: t('navData.AddNew'),
                path: paths.cds.media.add,
                disabled: !permissions?.media?.add,
              },
            ],
          },
          {
            title: t('navData.Usecases'),
            path: paths.cds.usecase.root,
            icon: ICONS.usecase,
            disabled: !navPermissions?.usecase,
            children: [
              {
                title: t('navData.AllUsecases'),
                path: paths.cds.usecase.all,
                disabled: !permissions?.usecase?.all,
              },
              {
                title: t('navData.AddNew'),
                path: paths.cds.usecase.add,
                disabled: !permissions?.usecase?.add,
              },
            ],
          },
          {
            title: t('navData.Ideation'),
            path: paths.cds.ideation.root,
            icon: ICONS.ideation,
            disabled: !navPermissions?.ideation,
          },

          {
            title: t('navData.Permissions'),
            path: paths.cds.rolesAndPermissions.root,
            icon: ICONS.permissions,
            disabled: !navPermissions?.rolesAndPermissions,
          },
          {
            title: t('navData.AuditLog'),
            path: paths.cds.auditlog.root,
            icon: ICONS.auditlog,
            // disabled: !navPermissions?.auditlog,
          },
          {
            title: t('navData.FormBuilder'),
            path: paths.cds.formBuilder.root,
            icon: ICONS.formBuilder,
            // disabled: !navPermissions?.formBuilder,
            children: [
              {
                title: t('navData.AllForms'),
                path: paths.cds.formBuilder.all,
                // disabled: !permissions?.formBuilder?.all,
              },
              {
                title: t('navData.AddNew'),
                path: paths.cds.formBuilder.add,
                // disabled: !permissions?.formBuilder?.add,
              },
            ],
          },
          {
            title: t('navData.LMS'),
            path: paths.cds.lms.root,
            icon: ICONS.lms,
            // disabled: !navPermissions?.lms,
            children: [
              {
                title: t('navData.Quizzes'),
                path: paths.cds.lms.quizzes.root,
                children: [
                  {
                    title: t('navData.AllQuizzes'),
                    path: paths.cds.lms.quizzes.root,
                    // disabled: !permissions?.lms?.quizzes,
                  },
                  {
                    title: t('navData.AddQuiz'),
                    path: paths.cds.lms.quizzes.add,
                    // disabled: !permissions?.lms?.quizzes,
                  },
                ],
              },
              {
                title: t('navData.Courses'),
                path: paths.cds.lms.courses.root,
                // disabled: !navPermissions?.courses,
                children: [
                  {
                    title: t('navData.AllCourses'),
                    path: paths.cds.lms.courses.all,
                    // disabled: !permissions?.lms?.courses,
                  },
                  {
                    title: t('navData.AddNew'),
                    path: paths.cds.lms.courses.add,
                    // disabled: !permissions?.lms?.courses,
                  },
                ],
              },
            ],
          },
        ].filter(Boolean),
      },
    ];

    setItems(newItems);
  }, [navPermissions, permissions, t]);

  return items;
}
