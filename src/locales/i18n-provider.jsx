import i18next from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next, I18nextProvider as Provider } from 'react-i18next';

import { localStorageGetItem } from 'src/utils/storage-available';

import { CONFIG } from 'src/config-global';

import PropTypes from 'prop-types';
import { fallbackLng, i18nOptions } from './config-locales';

const lng = localStorageGetItem('i18nextLng', fallbackLng);

const fetchTranslations = async (lang, ns) => {
  const token = localStorage.getItem('accessToken');
  const BASE_URL = CONFIG.site.serverUrl;

  const response = await fetch(`${BASE_URL}/locales/getLocales`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ lang }),
  });

  if (!response.ok) {
    throw new Error('Failed to fetch translations');
  }
  const data = await response.json();

  return data.data;
};

i18next
  .use(LanguageDetector)
  .use(initReactI18next)
  .use({
    type: 'backend',
    read: (language, namespace, callback) => {
      fetchTranslations(language, namespace)
        .then((data) => callback(null, data))
        .catch((err) => callback(err, false));
    },
  })
  .init({ ...i18nOptions(lng), detection: { caches: ['localStorage'] } });

export function I18nProvider({ children }) {
  return <Provider i18n={i18next}>{children}</Provider>;
}
I18nProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
