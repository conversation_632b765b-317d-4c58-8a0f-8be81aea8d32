{"months": {"Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "May": "May", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sep": "Sep", "Oct": "Oct", "Nov": "Nov", "Dec": "Dec"}, "navData": {"Home": "Home", "Ideation": "Ideation", "Reporting": "Reporting", "Users": "Users", "APIs": "APIs", "AI TOOLS": "AI TOOLS", "ContentDeliverySystem": "Content Delivery System", "AIJourney": "AI Journey", "AllItems": "All Items", "AddNew": "Add New", "Onboarding": "Onboarding", "Industry": "Industry", "ManagementRole": "Management Role", "Function": "Function", "Levels": "Levels", "JobRole": "Job Role", "Media": "Media", "AllMedias": "All Medias", "Usecases": "Usecases", "AllUsecases": "All Usecases", "Courses": "Courses", "Permissions": "Permissions", "AuditLog": "<PERSON>t Log", "FormBuilder": "Form Builder", "AllForms": "All Forms", "LMS": "Learning Management", "Quizzes": "Quizzes", "AddQuiz": "Add Quiz"}, "common": {"all": "All", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View"}, "validation": {"required": "This field is required", "min": "Value must be at least {{min}}", "max": "Value must be at most {{max}}"}, "lms": {"title": "Learning Management System", "categories": {"programming": "Programming", "dataScience": "Data Science", "ai": "Artificial Intelligence", "businessSkills": "Business Skills", "language": "Language"}, "questionTypes": {"multipleChoice": "Multiple Choice", "singleChoice": "Single Choice", "multipleSelection": "Multiple Selection", "trueFalse": "True/False", "text": "Text", "fillInTheBlank": "Fill in the Blank", "matching": "Matching"}, "quizzes": {"title": "Title", "add": "Add Quiz", "edit": "Edit Quiz", "view": "View Quiz", "delete": "Delete Quiz", "createSuccess": "Quiz created successfully", "createError": "Error creating quiz", "updateSuccess": "Quiz updated successfully", "updateError": "Error updating quiz", "deleteSuccess": "Quiz deleted successfully", "deleteError": "Error deleting quiz", "description": "Description", "category": "Category", "timeLimit": "Time Limit (minutes)", "passingScore": "Passing Score (%)", "maxAttempts": "Maximum Attempts", "maxAttemptsHelp": "0 means unlimited attempts", "shuffleQuestions": "Shuffle Questions", "showAnswers": "Show Answers After Completion", "questions": "Questions", "addQuestion": "Add Question", "questionNumber": "Question {number}", "questionText": "Question Text", "questionType": "Question Type", "points": "Points", "options": "Options", "addOption": "Add Option", "optionNumber": "Option {number}", "optionText": "Option Text", "isCorrect": "Is Correct", "explanation": "Explanation", "correctAnswer": "Correct Answer", "leftItem": "Left Item", "rightItem": "Right Item", "multipleChoice": "Multiple Choice", "singleChoice": "Single Choice", "multipleSelection": "Multiple Selection", "matching": "Matching", "trueOrFalse": "True/False", "fillInTheBlank": "Fill in the Blank", "text": "Text", "matchingPairs": "Matching Pairs", "blankText": "Fill in the Blank Text", "correctAnswers": "Correct Answers", "selectAllCorrect": "Select all correct options", "selectOneCorrect": "Select one correct option", "fillInBlank": "Fill in the blank", "matchItems": "Match items", "enterText": "Enter text answer", "id": "ID", "type": "Type", "question": "Question", "option": "Option", "match": "Match", "pairs": "Pairs", "left": "Left", "right": "Right", "noQuizzes": "No quizzes found. Create your first quiz!", "noSearchResults": "No quizzes match your search criteria.", "createdAt": "Created at"}}, "homePage": {"COHORT SIZE": "COHORT SIZE", "LOGIN MADE": "LOGIN MADE", "PROGRAM PARTICIPICATION": "PROGRAM PARTICIPICATION", "PROGRAM COMPLETED": "PROGRAM COMPLETED", "Ideation Reporting": "Ideation Reporting", "Total Ideas": "Total Ideas", "This Week": "This Week", "Last Week": "Last Week"}, "ideation": {"Ideation Management": "Ideation Management", "See user-added ideas and manage all stages.": "See user-added ideas and manage all stages.", "Ideas": "Ideas", "Analytics": "Analytics", "No results found": "No results found", "User Name": "Name", "Idea Name": "Idea Name", "Business Area": "Business Area", "Impact": "Impact", "Status": "Status", "Date": "Date", "Delete": "Delete", "Are you sure want to delete items?": "Are you sure want to delete items?", "Cancel": "Cancel", "Customer Care/After Sales Service": "Customer Care/After Sales Service", "Supply Chain & Procurement": "Supply Chain & Procurement", "Sales": "Sales", "Production & Operations": "Production & Operations", "Research & Development": "Research & Development", "Finance": "Finance", "Legal & Compliance": "Legal & Compliance", "HR": "HR", "Marketing:": "Marketing", "Approved": "Approved", "On Hold": "On Hold", "Under Review": "Under Review", "No quantitative business impact": "No quantitative business impact", "Low impact below 0.1 million CHF per year": "Low impact: below 0.1 million CHF per year", "Medium impact 0.1-1 million CHF per year": "Medium impact: 0.1-1 million CHF per year", "High impact 1-10 million CHF per year": "High impact: 1-10 million CHF per year", "Very high impact above 10 million CHF per year": "Very high impact: above 10 million CHF per year", "Number of people who uploaded their ideas": "Number of people who uploaded their ideas", "All ideas": "All ideas", "All approved ideas": "All approved ideas", "All ideas on hold": "All ideas on hold", "All rejected ideas": "All rejected ideas"}, "reporting": {"Reporting": "Reporting", "Reporting can give you detailed information, get reports and save the pdf to your file.": "Reporting can give you detailed information, get reports and save the pdf to your file.", "Training Progress Report": "Training Progress", "Nextgen Talent Radar Report": "Nextgen Talent Radar", "Usecase Application Report": "Use Case Application", "Ai Value Report": "AI Value", "Ideation Report": "Ideation", "id": "ID", "Report Name": "Report Name", "File": "Download", "Creator": "Creator", "Created At": "Created At"}, "users": {"User Management": "User Management", "You can list users on the platform and edit their roles.": "You can list users on the platform and edit their roles."}, "APIs": {"API Management": "API Management", "You can manage the APIs to be used on the platform.": "You can manage the APIs to be used on the platform.", "API Key": "API Key", "Endpoint": "Endpoint", "Deployment Name": "Deployment Name"}, "AITools": {"title": "AI Tools", "subtitle": "You Can Manage the AI Tools to be used on the platform"}, "areYouSure": "Are You Sure?", "areYouSureDescription": "Do you want to turn off your selected technology?", "yes": "Yes", "no": "No"}