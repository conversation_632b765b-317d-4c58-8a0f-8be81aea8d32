// core (MUI)
import { deDE as deDECore, trTR as trTR<PERSON>ore } from '@mui/material/locale';
// date pickers (MUI)
import { deDE as deDEDate, enUS as enUSDate, trTR as trTRDate } from '@mui/x-date-pickers/locales';
// data grid (MUI)
import {
  deDE as deDEDataGrid,
  enUS as enUSDataGrid,
  trTR as trTRDataGrid,
} from '@mui/x-data-grid/locales';

// ----------------------------------------------------------------------

export const allLangs = [
  {
    value: 'en',
    label: 'English',
    countryCode: 'GB',
    adapterLocale: 'en',
    numberFormat: { code: 'en-US', currency: 'USD' },
    systemValue: {
      components: { ...enUSDate.components, ...enUSDataGrid.components },
    },
  },
  {
    value: 'de',
    label: 'German',
    countryCode: 'DE',
    adapterLocale: 'de',
    numberFormat: { code: 'de-DE', currency: 'EUR' },
    systemValue: {
      components: { ...deDECore.components, ...deDEDate.components, ...deDEDataGrid.components },
    },
  },
  {
    value: 'tr',
    label: 'Turkish',
    countryCode: 'TR',
    adapterLocale: 'tr',
    numberFormat: { code: 'TR-tr', currency: 'EUR' },
    systemValue: {
      components: { ...trTRCore.components, ...trTRDate.components, ...trTRDataGrid.components },
    },
  },
];

/**
 * Country code:
 * https://flagcdn.com/en/codes.json
 *
 * Number format code:
 * https://gist.github.com/raushankrjha/d1c7e35cf87e69aa8b4208a8171a8416
 */
