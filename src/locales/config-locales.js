export const fallbackLng = 'en';
export const languages = ['en', 'de', 'tr'];
export const defaultNS = 'common';
export const cookieName = 'i18next';

// ----------------------------------------------------------------------

export function i18nOptions(lng = fallbackLng, ns = defaultNS) {
  return {
    // debug: true,
    lng,
    fallbackLng,
    ns,
    defaultNS,
    fallbackNS: defaultNS,
    supportedLngs: languages,
  };
}

// ----------------------------------------------------------------------

export const changeLangMessages = {
  en: {
    success: 'Language has been changed!',
    error: 'Error changing language!',
    loading: 'Loading...',
  },
  de: {
    success: 'Die Sprache wurde geändert!',
    error: '<PERSON><PERSON> beim Ändern der Sprache!',
    loading: 'Laden...',
  },
  tr: {
    success: 'Dil <PERSON>!',
    error: 'Dil değiştirme hatası!',
    loading: 'Yükleniyor...',
  },
};
