// ----------------------------------------------------------------------

const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
  CDS: '/cds',
};

// ----------------------------------------------------------------------

export const paths = {
  minimalUI: 'https://mui.com/store/items/minimal-dashboard/',
  // AUTH
  auth: {
    jwt: {
      login: `${ROOTS.AUTH}/jwt/login`,
      register: `${ROOTS.AUTH}/jwt/register`,
    },
  },
  // CDS
  cds: {
    root: ROOTS.CDS,
    formBuilder: {
      root: `${ROOTS.CDS}/formBuilder`,
      all: `${ROOTS.CDS}/formBuilder/all`,
      add: `${ROOTS.CDS}/formBuilder/add`,
      edit: (id) => `${ROOTS.CDS}/formBuilder/${id}/edit`,
    },
    journey: {
      root: `${ROOTS.CDS}/journey`,
      all: `${ROOTS.CDS}/journey/all`,
      add: `${ROOTS.CDS}/journey/add`,
    },
    onboarding: {
      root: `${ROOTS.CDS}/onboarding`,
      industry: `${ROOTS.CDS}/onboarding/industry`,
      managementRole: `${ROOTS.CDS}/onboarding/managementRole`,
      function: `${ROOTS.CDS}/onboarding/function`,
      levels: `${ROOTS.CDS}/onboarding/levels`,
      jobRole: `${ROOTS.CDS}/onboarding/jobRole`,
    },
    media: {
      root: `${ROOTS.CDS}/media`,
      all: `${ROOTS.CDS}/media/all`,
      add: `${ROOTS.CDS}/media/add`,
    },
    fsm: {
      root: `${ROOTS.CDS}/functionSpecific`,
      all: `${ROOTS.CDS}/functionSpecificModules/all`,
      add: `${ROOTS.CDS}/functionSpecificModules/add`,
    },
    ideation: {
      root: `${ROOTS.CDS}/ideation`,
    },

    lms: {
      root: `${ROOTS.CDS}/lms`,
      quizzes: {
        root: `${ROOTS.CDS}/lms/quizzes`,
        all: `${ROOTS.CDS}/lms/quizzes/all`,
        add: `${ROOTS.CDS}/lms/quizzes/add`,
        edit: (id) => `${ROOTS.CDS}/lms/quizzes/${id}/edit`,
        view: (id) => `${ROOTS.CDS}/lms/quizzes/${id}`,
      },
      courses: {
        root: `${ROOTS.CDS}/lms/courses`,
        all: `${ROOTS.CDS}/lms/courses/all`,
        add: `${ROOTS.CDS}/lms/courses/add`,
        edit: (id) => `${ROOTS.CDS}/lms/courses/${id}/edit`,
      },
    },
    usecase: {
      root: `${ROOTS.CDS}/usecase`,
      all: `${ROOTS.CDS}/usecase/all`,
      add: `${ROOTS.CDS}/usecase/add`,
    },
    rolesAndPermissions: {
      root: `${ROOTS.CDS}/rolesAndPermissions`,
    },
    auditlog: {
      root: `${ROOTS.CDS}/auditlog`,
    },
  },
  // DASHBOARD

  dashboard: {
    root: ROOTS.DASHBOARD,
    two: `${ROOTS.DASHBOARD}/two`,
    three: `${ROOTS.DASHBOARD}/three`,
    group: {
      root: `${ROOTS.DASHBOARD}/group`,
      five: `${ROOTS.DASHBOARD}/group/five`,
      six: `${ROOTS.DASHBOARD}/group/six`,
    },
  },
};
