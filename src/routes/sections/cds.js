import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';
// auth
import { AuthGuard } from 'src/auth/guard';
// layouts
import DashboardLayout from 'src/layouts/dashboard';
// components
import { LoadingScreen } from 'src/components/loading-screen';
import PermissionGuard from 'src/utils/permission-guard';

// ----------------------------------------------------------------------

const CDSPage = lazy(() => import('src/pages/cds/root'));
const JourneyPage = lazy(() => import('src/pages/cds/journey/root'));
const MediaPage = lazy(() => import('src/pages/cds/media/root'));
const UsecasesPage = lazy(() => import('src/pages/cds/usecases/root'));
const OnboardingPage = lazy(() => import('src/pages/cds/onboarding/root'));

// Journey
const AllJourneysPage = lazy(() => import('src/pages/cds/journey/allJourneys'));
const AddJourneyPage = lazy(() => import('src/pages/cds/journey/addJourney'));
const UpdateJourneyView = lazy(() => import('src/sections/cds/journey/all/UpdateJourneyModal'));

// Onboarding
const IndustryPage = lazy(() => import('src/pages/cds/onboarding/Industry'));
const JobRolePage = lazy(() => import('src/pages/cds/onboarding/jobRole'));
const ManagementRolePage = lazy(() => import('src/pages/cds/onboarding/managementRole'));
const FunctionPage = lazy(() => import('src/pages/cds/onboarding/function'));
const LevelsPage = lazy(() => import('src/pages/cds/onboarding/levels'));

// Media
const AllMediasPage = lazy(() => import('src/pages/cds/media/allMedias'));
const AddMediaPage = lazy(() => import('src/pages/cds/media/addMedia'));

// Form Builder
const FormBuilderPage = lazy(() => import('src/pages/cds/formBuilder/root'));
const AllFormsPage = lazy(() => import('src/pages/cds/formBuilder/allForms'));
const AddFormPage = lazy(() => import('src/pages/cds/formBuilder/addForm'));
const EditFormPage = lazy(() => import('src/pages/cds/formBuilder/editForm'));
// Journey
const AllUsecasesPage = lazy(() => import('src/pages/cds/usecases/allUsecases'));
const AddUsecasePage = lazy(() => import('src/pages/cds/usecases/addUsecase'));

// Ideation
const IdeationPage = lazy(() => import('src/pages/cds/ideation/root'));

// Courses
const CoursesPage = lazy(() => import('src/pages/cds/lms/courses/root'));
const AllCoursesPage = lazy(() => import('src/pages/cds/lms/courses/allCourses'));
const AddCoursePage = lazy(() => import('src/pages/cds/lms/courses/addCourse'));
const EditCoursePage = lazy(() => import('src/pages/cds/lms/courses/editCourse'));

// LMS
const LMSPage = lazy(() => import('src/pages/cds/lms/root'));
const QuizListPage = lazy(() => import('src/pages/cds/lms/quizzes/list'));
const QuizCreatePage = lazy(() => import('src/pages/cds/lms/quizzes/create'));
const QuizEditPage = lazy(() => import('src/pages/cds/lms/quizzes/edit'));
const QuizViewPage = lazy(() => import('src/pages/cds/lms/quizzes/view'));

// Permissions
const RolesAndPermissionsPage = lazy(() => import('src/pages/cds/rolesAndPermissions/root'));

// Audit Log
const AuditLogPage = lazy(() => import('src/pages/cds/auditlog/root'));

// ----------------------------------------------------------------------

export const cdsRoutes = [
  {
    path: 'cds',
    element: (
      <AuthGuard>
        <DashboardLayout>
          <Suspense fallback={<LoadingScreen />}>
            <Outlet />
          </Suspense>
        </DashboardLayout>
      </AuthGuard>
    ),
    children: [
      { element: <CDSPage />, index: true },

      {
        path: 'journey',
        children: [
          { element: <JourneyPage />, index: true },
          {
            path: 'all',
            element: (
              <PermissionGuard path="aiJourney" subPath="all">
                <AllJourneysPage />
              </PermissionGuard>
            ),
          },
          {
            path: 'add',
            element: (
              <PermissionGuard path="aiJourney" subPath="add">
                <AddJourneyPage />
              </PermissionGuard>
            ),
          },
          {
            path: 'edit/:journeyId',
            element: (
              <PermissionGuard path="aiJourney" subPath="all">
                <UpdateJourneyView />
              </PermissionGuard>
            ),
          },
        ],
      },
      {
        path: 'onboarding',
        children: [
          { element: <OnboardingPage />, index: true },
          {
            path: 'industry',
            element: (
              <PermissionGuard path="onboarding" subPath="industry">
                <IndustryPage />
              </PermissionGuard>
            ),
          },
          {
            path: 'managementRole',
            element: (
              <PermissionGuard path="onboarding" subPath="managementRole">
                <ManagementRolePage />
              </PermissionGuard>
            ),
          },
          {
            path: 'jobRole',
            element: (
              <PermissionGuard path="onboarding" subPath="jobRole">
                <JobRolePage />
              </PermissionGuard>
            ),
          },
          {
            path: 'function',
            element: (
              <PermissionGuard path="onboarding" subPath="function">
                <FunctionPage />
              </PermissionGuard>
            ),
          },
          {
            path: 'levels',
            element: (
              <PermissionGuard path="onboarding" subPath="levels">
                <LevelsPage />
              </PermissionGuard>
            ),
          },
        ],
      },
      {
        path: 'media',
        children: [
          { element: <MediaPage />, index: false },
          {
            path: 'all',
            element: (
              <PermissionGuard path="media" subPath="all">
                <AllMediasPage />
              </PermissionGuard>
            ),
          },
          {
            path: 'add',
            element: (
              <PermissionGuard path="media" subPath="add">
                <AddMediaPage />
              </PermissionGuard>
            ),
          },
        ],
      },
      {
        path: 'formBuilder',
        children: [
          { element: <FormBuilderPage />, index: true },
          {
            path: 'all',
            element: (
              <PermissionGuard path="formBuilder" subPath="all">
                <AllFormsPage />
              </PermissionGuard>
            ),
          },
          {
            path: 'add',
            element: (
              <PermissionGuard path="formBuilder" subPath="add">
                <AddFormPage />
              </PermissionGuard>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <PermissionGuard path="formBuilder" subPath="edit">
                <EditFormPage />
              </PermissionGuard>
            ),
          },
        ],
      },
      {
        path: 'usecase',
        children: [
          { element: <UsecasesPage />, index: true },
          {
            path: 'all',
            element: (
              <PermissionGuard path="usecase" subPath="all">
                <AllUsecasesPage />
              </PermissionGuard>
            ),
          },
          {
            path: 'add',
            element: (
              <PermissionGuard path="usecase" subPath="add">
                <AddUsecasePage />
              </PermissionGuard>
            ),
          },
        ],
      },
      {
        path: 'ideation',
        children: [
          {
            element: (
              <PermissionGuard path="ideation" subPath="root">
                <IdeationPage />
              </PermissionGuard>
            ),
            index: true,
          },
        ],
      },

      {
        path: 'lms',
        children: [
          {
            element: <LMSPage />,
            index: true,
          },
          {
            path: 'quizzes',
            children: [
              {
                element: <QuizListPage />,
                index: true,
              },
              {
                path: 'add',
                element: <QuizCreatePage />,
              },
              {
                path: ':id/edit',
                element: <QuizEditPage />,
              },
              {
                path: ':id',
                element: <QuizViewPage />,
              },
            ],
          },
          {
            path: 'courses',
            children: [
              {
                element: (
                  <PermissionGuard path="courses" subPath="root">
                    <CoursesPage />
                  </PermissionGuard>
                ),
                index: true,
              },
              {
                path: 'all',
                element: (
                  <PermissionGuard path="courses" subPath="all">
                    <AllCoursesPage />
                  </PermissionGuard>
                ),
              },
              {
                path: 'add',
                element: (
                  <PermissionGuard path="courses" subPath="add">
                    <AddCoursePage />
                  </PermissionGuard>
                ),
              },
              {
                path: ':id/edit',
                element: (
                  <PermissionGuard path="courses" subPath="edit">
                    <EditCoursePage />
                  </PermissionGuard>
                ),
              },
            ],
          },
        ],
      },
      {
        path: 'rolesAndPermissions',
        children: [
          {
            element: (
              <PermissionGuard path="rolesAndPermissions" subPath="root">
                <RolesAndPermissionsPage />
              </PermissionGuard>
            ),
            index: true,
          },
        ],
      },
      {
        path: 'auditlog',
        children: [
          {
            element: (
              <PermissionGuard path="auditlog" subPath="root">
                <AuditLogPage />
              </PermissionGuard>
            ),
            index: true,
          },
        ],
      },
    ],
  },
];
