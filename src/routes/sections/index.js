import { Navigate, useRoutes } from 'react-router-dom';
// config
import { PATH_AFTER_LOGIN } from 'src/config-global';
//
import { mainRoutes } from './main';

import ForbiddenPage from 'src/pages/403';
import { authRoutes } from './auth';
import { cdsRoutes } from './cds';

// ----------------------------------------------------------------------

export default function Router() {
  return useRoutes([
    {
      path: '/',
      element: <Navigate to={PATH_AFTER_LOGIN} replace />,
    },

    // Auth routes
    ...authRoutes,

    // Dashboard routes
    // ...dashboardRoutes,

    // Journey routes
    ...cdsRoutes,

    // Main routes
    ...mainRoutes,

    // No match 404
    { path: '*', element: <Navigate to="/404" replace /> },
    { path: '/403', element: <ForbiddenPage /> },
  ]);
}
