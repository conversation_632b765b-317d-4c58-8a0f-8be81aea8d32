import PropTypes from 'prop-types';
import { useDropzone } from 'react-dropzone';
// @mui
import { alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
//
import Iconify from '../iconify';

// ----------------------------------------------------------------------

export default function UploadBox({ placeholder, error, disabled, onDropFile, sx, ...other }) {
  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    disabled,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const fileUrl = URL.createObjectURL(acceptedFiles[0]);
        onDropFile(fileUrl);
      }
    },
    ...other,
  });

  const hasError = isDragReject || error;

  return (
    <Box
      {...getRootProps()}
      sx={{
        m: 0.5,
        width: 1,
        height: 64,
        flexShrink: 0,
        display: 'flex',
        borderRadius: 1,
        cursor: 'pointer',
        alignItems: 'center',
        color: 'text.disabled',
        justifyContent: 'center',
        bgcolor: (theme) => alpha(theme.palette.grey[500], 0.08),
        border: (theme) => `dashed 1px ${alpha(theme.palette.grey[500], 0.16)}`,
        ...(isDragActive && {
          opacity: 0.72,
        }),
        ...(disabled && {
          opacity: 0.48,
          pointerEvents: 'none',
        }),
        ...(hasError && {
          color: 'error.main',
          bgcolor: 'error.lighter',
          borderColor: 'error.light',
        }),
        '&:hover': {
          opacity: 0.72,
        },
        ...sx,
      }}
      role="button"
      aria-label="Upload File" // Erişilebilirlik için açıklama ekledik
      tabIndex={0} // Klavye ile erişim için tabIndex ekledik
      onKeyDown={(event) => {
        if (event.key === 'Enter' || event.key === ' ') {
          event.preventDefault();
          // Klavye ile alanı tıklamak için getRootProps fonksiyonunu çağırıyoruz
          getRootProps().onClick(event);
        }
      }}
    >
      <input {...getInputProps()} />
      {placeholder || <Iconify icon="eva:cloud-upload-fill" width={28} />}
      Upload File
    </Box>
  );
}

UploadBox.propTypes = {
  disabled: PropTypes.bool,
  error: PropTypes.bool,
  placeholder: PropTypes.node,
  onDropFile: PropTypes.func.isRequired,
  sx: PropTypes.object,
};
