import PropTypes from 'prop-types';
import { useForm<PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';

import { TextField } from '@mui/material';

// ----------------------------------------------------------------------

export default function RHFSelect({
  name,
  native,
  maxHeight = 220,
  helperText,
  children,
  onChange,
  ...other
}) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          select
          fullWidth
          SelectProps={{
            native,
            MenuProps: {
              PaperProps: {
                sx: {
                  maxHeight,
                },
              },
            },
            sx: { textTransform: 'capitalize' },
          }}
          error={!!error}
          helperText={error ? error?.message : helperText}
          onChange={(event) => {
            field.onChange(event);
            if (onChange) {
              onC<PERSON><PERSON>(event);
            }
          }}
          {...other}
        >
          {children}
        </TextField>
      )}
    />
  );
}

RHFSelect.propTypes = {
  children: PropTypes.node,
  helperText: PropTypes.string,
  maxHeight: PropTypes.number,
  name: PropTypes.string,
  native: PropTypes.bool,
  onChange: PropTypes.func,
};
