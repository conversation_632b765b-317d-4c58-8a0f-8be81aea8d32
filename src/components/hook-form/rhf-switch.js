import PropTypes from 'prop-types';
import { useForm<PERSON>ontext, Controller } from 'react-hook-form';

import { Switch, FormControlLabel } from '@mui/material';

// ----------------------------------------------------------------------

export default function RHFSwitch({ name, helperText, ...other }) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <div>
          <FormControlLabel control={<Switch {...field} checked={field.value} />} {...other} />

          {(!!error || helperText) && (
            <FormHelperText error={!!error}>{error ? error?.message : helperText}</FormHelperText>
          )}
        </div>
      )}
    />
  );
}

RHFSwitch.propTypes = {
  helperText: PropTypes.string,
  name: PropTypes.string,
};

// ----------------------------------------------------------------------

function FormHelperText({ children, error }) {
  return (
    <div
      style={{
        marginTop: 8,
        marginLeft: 14,
        marginRight: 14,
        fontSize: '0.75rem',
        color: error ? '#FF4842' : 'rgba(0, 0, 0, 0.6)',
      }}
    >
      {children}
    </div>
  );
}

FormHelperText.propTypes = {
  children: PropTypes.node,
  error: PropTypes.bool,
};
