import PropTypes from 'prop-types';
import { forwardRef } from 'react';
// @mui
import { useTheme } from '@mui/material/styles';
import { Box, Chip } from '@mui/material';

// ----------------------------------------------------------------------

const Label = forwardRef(
  ({ children, color = 'default', variant = 'soft', startIcon, endIcon, sx, ...other }, ref) => {
    const theme = useTheme();

    const iconStyles = {
      width: 16,
      height: 16,
      '& svg, img': { width: 1, height: 1, objectFit: 'cover' },
    };

    return (
      <Chip
        ref={ref}
        icon={startIcon ? <Box sx={{ ...iconStyles }}>{startIcon}</Box> : null}
        deleteIcon={endIcon ? <Box sx={{ ...iconStyles }}>{endIcon}</Box> : null}
        onDelete={endIcon ? () => {} : null}
        color={color}
        variant={variant}
        label={children}
        sx={{
          height: 22,
          minWidth: 22,
          fontSize: '0.75rem',
          fontWeight: 600,
          borderRadius: '6px',
          cursor: 'default',
          alignItems: 'center',
          whiteSpace: 'nowrap',
          '& .MuiChip-deleteIcon': {
            color: 'inherit',
            opacity: 0.7,
            '&:hover': {
              opacity: 1,
              color: 'inherit',
            },
          },
          ...sx,
        }}
        {...other}
      />
    );
  }
);

Label.propTypes = {
  children: PropTypes.node,
  color: PropTypes.oneOf([
    'default',
    'primary',
    'secondary',
    'info',
    'success',
    'warning',
    'error',
  ]),
  endIcon: PropTypes.node,
  startIcon: PropTypes.node,
  sx: PropTypes.object,
  variant: PropTypes.oneOf(['filled', 'outlined', 'ghost', 'soft']),
};

export default Label;
