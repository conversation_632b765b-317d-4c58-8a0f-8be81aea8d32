import PropTypes from 'prop-types';

import { TableCell, TableRow } from '@mui/material';

// ----------------------------------------------------------------------

export default function TableNoData({ isNotFound }) {
  return (
    <TableRow>
      {isNotFound ? (
        <TableCell colSpan={12}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '40px 0',
            }}
          >
            No data found
          </div>
        </TableCell>
      ) : (
        <TableCell colSpan={12} sx={{ p: 0 }} />
      )}
    </TableRow>
  );
}

TableNoData.propTypes = {
  isNotFound: PropTypes.bool,
};
