import PropTypes from 'prop-types';
// @mui
import { styled } from '@mui/material/styles';
import { Typography, Box, Stack, Paper } from '@mui/material';
// components
import Iconify from '../iconify';

// ----------------------------------------------------------------------

const StyledRoot = styled(Box)(({ theme }) => ({
  display: 'flex',
  textAlign: 'center',
  alignItems: 'center',
  flexDirection: 'column',
  justifyContent: 'center',
  padding: theme.spacing(3),
}));

// ----------------------------------------------------------------------

EmptyContent.propTypes = {
  sx: PropTypes.object,
  img: PropTypes.string,
  title: PropTypes.string,
  description: PropTypes.string,
  icon: PropTypes.string,
};

export default function EmptyContent({
  title = 'İçerik Bulunamadı',
  description = 'Bu bölümde henüz içerik bulunmamaktadır.',
  img,
  icon = 'eva:alert-circle-outline',
  sx,
  ...other
}) {
  return (
    <StyledRoot sx={sx} {...other}>
      {img ? (
        <Box component="img" alt="empty content" src={img} sx={{ height: 240, mb: 3 }} />
      ) : (
        <Paper
          sx={{
            width: 160,
            height: 160,
            borderRadius: '50%',
            backgroundColor: (theme) => theme.palette.background.neutral,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 3,
          }}
        >
          <Iconify icon={icon} width={80} height={80} sx={{ opacity: 0.4 }} />
        </Paper>
      )}

      <Stack spacing={1}>
        <Typography variant="h5" gutterBottom>
          {title}
        </Typography>

        {description && (
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {description}
          </Typography>
        )}
      </Stack>
    </StyledRoot>
  );
}
