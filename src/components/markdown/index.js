import PropTypes from 'prop-types';
import ReactMarkdown from 'react-markdown';
// @mui
import { styled } from '@mui/material/styles';
import { Box, Link, Typography, Divider } from '@mui/material';

// ----------------------------------------------------------------------

const MarkdownStyle = styled('div')(({ theme }) => ({
  // Paragraflar
  '& p': {
    marginBottom: theme.spacing(2),
  },
  // Listeler
  '& ul, & ol': {
    marginLeft: theme.spacing(3),
    marginBottom: theme.spacing(2),
  },
  // Başlıklar
  '& h1': {
    ...theme.typography.h1,
    marginBottom: theme.spacing(2),
  },
  '& h2': {
    ...theme.typography.h2,
    marginBottom: theme.spacing(1.5),
  },
  '& h3': {
    ...theme.typography.h3,
    marginBottom: theme.spacing(1),
  },
  '& h4': {
    ...theme.typography.h4,
    marginBottom: theme.spacing(1),
  },
  '& h5': {
    ...theme.typography.h5,
    marginBottom: theme.spacing(0.5),
  },
  '& h6': {
    ...theme.typography.h6,
    marginBottom: theme.spacing(0.5),
  },
  // Ayraç
  '& hr': {
    margin: theme.spacing(2, 0),
  },
  // Bağlantılar
  '& a': {
    color: theme.palette.primary.main,
    textDecoration: 'none',
    '&:hover': {
      textDecoration: 'underline',
    },
  },
  // Kod blokları
  '& pre, & code': {
    ...theme.typography.body2,
    fontFamily: theme.typography.fontFamilyCode,
    fontSize: 14,
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.grey[900],
    color: theme.palette.common.white,
    padding: theme.spacing(0.5, 1),
    whiteSpace: 'pre-wrap',
    margin: theme.spacing(1, 0),
  },
  // Tablolar
  '& table': {
    width: '100%',
    borderCollapse: 'collapse',
    border: `1px solid ${theme.palette.divider}`,
    'th, td': {
      border: `1px solid ${theme.palette.divider}`,
      padding: theme.spacing(1),
    },
    'thead th': {
      backgroundColor: theme.palette.background.neutral,
    },
  },
  // Resimler
  '& img': {
    maxWidth: '100%',
    borderRadius: theme.shape.borderRadius,
  },
}));

// ----------------------------------------------------------------------

Markdown.propTypes = {
  sx: PropTypes.object,
  children: PropTypes.string,
};

export default function Markdown({ children, sx, ...other }) {
  if (!children) return null;

  return (
    <MarkdownStyle sx={sx} {...other}>
      <ReactMarkdown
        components={{
          h1: ({ ...props }) => <Typography variant="h1" {...props} />,
          h2: ({ ...props }) => <Typography variant="h2" {...props} />,
          h3: ({ ...props }) => <Typography variant="h3" {...props} />,
          h4: ({ ...props }) => <Typography variant="h4" {...props} />,
          h5: ({ ...props }) => <Typography variant="h5" {...props} />,
          h6: ({ ...props }) => <Typography variant="h6" {...props} />,
          p: ({ ...props }) => <Typography variant="body1" {...props} />,
          a: ({ href, ...props }) => <Link href={href} target="_blank" rel="noopener" {...props} />,
          hr: ({ ...props }) => <Divider {...props} />,
          img: ({ ...props }) => (
            <Box component="img" {...props} sx={{ my: 2, maxWidth: '100%' }} />
          ),
        }}
      >
        {children}
      </ReactMarkdown>
    </MarkdownStyle>
  );
}
