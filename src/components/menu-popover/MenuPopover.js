import PropTypes from 'prop-types';

import { Popover } from '@mui/material';

// ----------------------------------------------------------------------

export default function MenuPopover({ open, onClose, children, arrow, sx, ...other }) {
  return (
    <Popover
      open={open}
      anchorReference="anchorEl"
      anchorEl={other.anchorEl}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      slotProps={{
        paper: {
          sx: {
            width: 200,
            p: 1,
            mt: 1.5,
            ml: 0.75,
            overflow: 'inherit',
            boxShadow: (theme) => theme.shadows[20],
            ...sx,
            ...(arrow && {
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 20,
                width: 12,
                height: 12,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: -1,
              },
            }),
          },
        },
      }}
      onClose={onClose}
      {...other}
    >
      {children}
    </Popover>
  );
}

MenuPopover.propTypes = {
  arrow: PropTypes.string,
  children: PropTypes.node,
  onClose: PropTypes.func,
  open: PropTypes.bool,
  sx: PropTypes.object,
};
