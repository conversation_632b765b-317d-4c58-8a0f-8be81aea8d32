import PropTypes from 'prop-types';
// @mui
import {
  Dialog,
  Button,
  DialogTitle,
  DialogActions,
  DialogContent,
  DialogContentText,
} from '@mui/material';

// ----------------------------------------------------------------------

ConfirmDialog.propTypes = {
  open: PropTypes.bool,
  title: PropTypes.string,
  action: PropTypes.func,
  content: PropTypes.string,
  onClose: PropTypes.func,
};

export default function ConfirmDialog({ title, content, action, open, onClose }) {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle sx={{ pb: 2 }}>{title}</DialogTitle>

      {content && (
        <DialogContent sx={{ pb: 0 }}>
          <DialogContentText>{content}</DialogContentText>
        </DialogContent>
      )}

      <DialogActions>
        <Button variant="outlined" color="inherit" onClick={onClose}>
          İptal
        </Button>

        <Button
          variant="contained"
          color="error"
          onClick={() => {
            if (action) {
              action();
            }
            onClose();
          }}
        >
          Onayla
        </Button>
      </DialogActions>
    </Dialog>
  );
}
