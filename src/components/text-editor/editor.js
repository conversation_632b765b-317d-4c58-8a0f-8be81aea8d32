import { Editor } from '@tinymce/tinymce-react';
import { fixDialogInputs } from './editor-dialog-fix';
import './editor-styles.css';

// TinyMCE so the global var exists
import 'tinymce/tinymce';
// DOM model
import 'tinymce/models/dom/model';
// Theme
import 'tinymce/themes/silver';
// Toolbar icons
import 'tinymce/icons/default';
// Editor styles
import 'tinymce/skins/ui/oxide/skin';

// importing the plugin js.
// if you use a plugin that is not listed here the editor will fail to load
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/anchor';
import 'tinymce/plugins/autolink';
import 'tinymce/plugins/autoresize';
import 'tinymce/plugins/autosave';
import 'tinymce/plugins/charmap';
import 'tinymce/plugins/code';
import 'tinymce/plugins/codesample';
import 'tinymce/plugins/directionality';
import 'tinymce/plugins/emoticons';

import 'tinymce/plugins/fullscreen';
import 'tinymce/plugins/help';
import 'tinymce/plugins/help/js/i18n/keynav/en';
import 'tinymce/plugins/image';
import 'tinymce/plugins/importcss';
import 'tinymce/plugins/insertdatetime';
import 'tinymce/plugins/link';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/media';
import 'tinymce/plugins/nonbreaking';
import 'tinymce/plugins/pagebreak';
import 'tinymce/plugins/preview';
import 'tinymce/plugins/quickbars';
import 'tinymce/plugins/save';
import 'tinymce/plugins/searchreplace';
import 'tinymce/plugins/table';
import 'tinymce/plugins/visualblocks';
import 'tinymce/plugins/visualchars';
import 'tinymce/plugins/wordcount';

// importing plugin resources
import 'tinymce/plugins/emoticons/js/emojis';

// Content styles, including inline UI like fake cursors
import 'tinymce/skins/content/default/content';
import 'tinymce/skins/ui/oxide/content';

export default function BundledEditor(props) {
  // Varsayılan init ayarları
  const defaultInit = {
    height: 500,
    menubar: true,

    // TinyMCE varsayılan ayarları
    statusbar: true,
    branding: false,
    promotion: false,

    // Eklentiler
    plugins: [
      'advlist',
      'anchor',
      'autolink',
      'help',
      'image',
      'link',
      'lists',
      'searchreplace',
      'table',
      'wordcount',
      'media',
      'code',
      'fullscreen',
    ],

    // Araç çubuğu
    toolbar:
      'undo redo | blocks | ' +
      'bold italic forecolor | alignleft aligncenter ' +
      'alignright alignjustify | bullist numlist outdent indent | ' +
      'removeformat | image media | code fullscreen',

    // İçerik stili
    content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',

    // URL yolları
    base_url: '/tinymce',
    skin: 'oxide',
    skin_url: '/tinymce/skins/ui/oxide',
    content_css: '/tinymce/skins/content/default/content.css',

    // Resim ve Medya İletişim Kutuları için Özel Ayarlar
    image_advtab: true,
    image_dimensions: true,
    image_title: true,
    image_caption: true,

    // Dialog pencereleri için kritik ayarlar - modal tipi çalışıyor
    dialog_type: 'modal',
    file_picker_types: 'image media',

    // Upload özelliklerini kapat - manuel URL giriş seçenekleri çalışsın
    automatic_uploads: false,
    image_uploadtab: false,

    // Medya ayarları
    media_live_embeds: true,
    media_alt_source: true,
    media_poster: true,
    media_dimensions: true,

    // URL işleme ayarları - URL dönüşümünü yapma
    convert_urls: false,
    relative_urls: false,
    remove_script_host: false,
    document_base_url: window.location.origin,

    // iFrame desteği
    extended_valid_elements:
      'iframe[src|frameborder|style|scrolling|class|width|height|name|align]',

    // DOM event binding sorununu düzelt
    browser_spellcheck: true,
    hidden_input: false,

    // Setup fonksiyonu
    setup: function (editor) {
      // Editor başlatıldığında
      editor.on('init', function () {
        // Global TinyMCE nesnesine erişim
        if (window.tinymce) {
          window.tinymce.activeEditor = editor;
        }

        // Dialog açıldığında input düzeltmelerini uygula
        editor.on('OpenWindow', function () {
          setTimeout(fixDialogInputs, 100);
        });
      });
    },
  };

  // Kullanıcının init ayarlarını varsayılan ayarlarla birleştir
  const finalInit = { ...defaultInit, ...props.init };

  return (
    <Editor
      tinymceScriptSrc="/tinymce/tinymce.min.js"
      licenseKey="gpl"
      {...props}
      init={finalInit}
    />
  );
}
