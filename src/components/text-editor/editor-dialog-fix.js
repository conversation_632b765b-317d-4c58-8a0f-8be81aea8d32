/**
 * TinyMCE Dialog Onarımı
 *
 * <PERSON><PERSON>, TinyMCE'nin dialog pencerelerindeki girdi alan<PERSON>ını düzeltmek için kullanılır.
 * Özellikle Source URL input alanlarında yazma sorunlarını giderir.
 */

// Global TinyMCE nesnesi varsa ve yüklenmişse
if (window.tinymce) {
  // TinyMCE yüklendikten sonra
  window.tinymce.on('AddEditor', function (e) {
    const editor = e.editor;

    // Yeni editor örneği oluşturulduğunda
    editor.on('init', function () {
      // TinyMCE dialog açıldığında, inputların etkinliğini kontrol et
      editor.on('OpenWindow', function (event) {
        // Dialog penceresindeki tüm inputları etkinleştir
        setTimeout(function () {
          fixDialogInputs();
        }, 100);
      });
    });
  });
}

/**
 * TinyMCE dialog pencerelerindeki inputları düzeltir
 */
function fixDialogInputs() {
  // Dialog konteynerini bul
  const dialogContainers = document.querySelectorAll('.tox-dialog, .mce-window');

  if (dialogContainers.length > 0) {
    dialogContainers.forEach((container) => {
      // Dialog içindeki tüm inputları ve textarea'ları bul
      const inputs = container.querySelectorAll('input, textarea');

      // Her bir input için düzeltmeleri uygula
      inputs.forEach((input) => {
        // Input'u etkinleştir
        input.removeAttribute('disabled');
        input.removeAttribute('readonly');

        // Tab indeksini düzelt
        input.setAttribute('tabindex', '0');

        // Tıklanabilirliği iyileştir
        input.style.pointerEvents = 'auto';

        // Olay dinleyicileri ekle
        enhanceInputEvents(input);
      });
    });
  }
}

/**
 * Input için gelişmiş olay dinleyicileri ekler
 */
function enhanceInputEvents(input) {
  // Varolan olay dinleyicilerini kaldır
  input.replaceWith(input.cloneNode(true));

  // Yeni elementi tekrar seç
  const newInput = document.querySelector(
    `input[name="${input.name}"], textarea[name="${input.name}"]`
  );

  if (newInput) {
    // Yeni olay dinleyicileri ekle
    newInput.addEventListener('click', function (e) {
      e.stopPropagation();
    });

    newInput.addEventListener('focus', function () {
      this.select();
    });
  }
}

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', function () {
  // Global düzeltme fonksiyonunu dışa aktar
  window.fixTinyMCEDialogs = fixDialogInputs;
});

export { fixDialogInputs };
