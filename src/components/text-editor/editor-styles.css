/* TinyMCE Dialog Fix Styles */

/* Dialog içindeki tüm inputların tıklanabilirliğini iyileştir */
.tox-dialog input,
.tox-dialog textarea,
.tox-dialog select,
.tox-dialog button,
.tox-dialog .tox-textfield,
.tox-dialog .tox-toolbar-textfield {
  pointer-events: auto !important;
  opacity: 1 !important;
  cursor: text !important;
  user-select: text !important;
}

/* Input alanlarının görünümünü iyileştir - disabled görünümün<PERSON> kaldır */
.tox-dialog input[type="text"],
.tox-dialog input[type="url"],
.tox-dialog textarea {
  background-color: #fff !important;
  color: #222 !important;
  border: 1px solid #ccc !important;
}

/* Dialog arka planının z-index sorunlarını çöz */
.tox-dialog-wrap {
  z-index: 1000000 !important;
}

/* Dialog overlay'inin tıklanabilirliğini iyileştir */
.tox-dialog-wrap__backdrop {
  pointer-events: auto !important;
}

/* Source URL alanı için özel düzeltme */
.tox-form__group--stretched .tox-textfield.tox-textfield--urlinput,
.tox-form__group input[aria-label="Source"],
.tox-form__group input[placeholder*="Source"],
.tox-form__group input[title*="URL"] {
  color: #222 !important;
  background-color: #fff !important;
  border: 1px solid #ccc !important;
  cursor: text !important;
}

/* İptal ve kaydet butonlarının stilini iyileştir */
.tox-dialog__footer-start button,
.tox-dialog__footer-end button {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Dialog arka planını ve kapatma düğmesini geliştir */
.tox-dialog {
  box-shadow: 0 16px 16px -10px rgba(0, 0, 0, 0.15), 0 0 40px 1px rgba(0, 0, 0, 0.15) !important;
}

.tox-dialog__header, 
.tox-dialog__body,
.tox-dialog__footer {
  padding: 8px 16px !important;
}

.tox-dialog__body-content {
  padding: 16px !important;
}

/* Dialog kapatma butonunu belirginleştir */
.tox-dialog__header .tox-button {
  color: #888 !important;
  cursor: pointer !important;
} 