{"name": "@minimal-kit/simple-vite-js", "author": "Minimals", "version": "5.3.0", "description": "Simple Vite & JavaScript", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "clear-all": "rm -rf node_modules .next out dist build", "re-start": "rm -rf node_modules .next out dist build && yarn install && yarn dev", "re-build": "rm -rf node_modules .next out dist build && yarn install && yarn build", "postinstall": "node ./postinstall.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.1.1", "@iconify/react": "^4.1.1", "@mui/icons-material": "^6.4.6", "@mui/lab": "^5.0.0-alpha.136", "@mui/material": "^5.14.0", "@mui/x-data-grid": "^6.10.0", "@mui/x-date-pickers": "^7.22.2", "@tinymce/tinymce-react": "^5.1.1", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.7", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "draft-js": "^0.11.7", "eslint-plugin-prettier": "^5.2.1", "framer-motion": "^10.12.21", "fs-extra": "^11.2.0", "history": "^5.3.0", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "i18next-resources-to-backend": "^1.2.1", "lodash": "^4.17.21", "nprogress": "^0.2.0", "numeral": "^2.0.6", "prop-types": "^15.8.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-dropzone": "^14.2.9", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.53.1", "react-i18next": "^15.1.1", "react-lazy-load-image-component": "^1.6.2", "react-router": "^6.14.1", "react-router-dom": "^6.14.1", "react-sortablejs": "^6.1.4", "react-toastify": "^10.0.5", "simplebar-react": "^3.2.4", "sonner": "^2.0.1", "sortablejs": "^1.15.3", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "tinymce": "^7.5.1", "yup": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-syntax-flow": "^7.22.5", "@babel/plugin-transform-react-jsx": "^7.22.5", "@babel/traverse": "^7.25.9", "@types/react": "^18.2.15", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.15.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.0.0", "typescript": "^5.1.6", "vite": "^5.4.11"}}