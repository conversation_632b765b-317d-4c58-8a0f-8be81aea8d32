name: Deploy to Development

on:
  push:
    branches:
      - dev

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      # 1. Kodun Checkout Edilmesi
      - name: Checkout code
        uses: actions/checkout@v3

      # 2. Azure Container Registry'e Giriş <PERSON>a
      - name: Log in to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_LOGIN_SERVER }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      # 3. Docker İmajını Build Et ve Push Et
      - name: Build and Push Docker Image
        run: |
          docker build -f Dockerfile.development -t ${{ secrets.ACR_LOGIN_SERVER }}/dev-image:latest .
          docker push ${{ secrets.ACR_LOGIN_SERVER }}/dev-image:latest
      
      - name: Azure CLI ile Giriş Yap
        run: |
          az login --service-principal \
            --username ${{ secrets.AZURE_CLIENT_ID }} \
            --password ${{ secrets.AZURE_CLIENT_SECRET }} \
            --tenant ${{ secrets.AZURE_TENANT_ID }}


      # 4. Azure Web App'e Docker Container'ı Deploy Et
      - name: Deploy to Azure Web App
        run: |
          az webapp config container set \
            --name contentfrontenddev \
            --resource-group Serverless \
            --docker-custom-image-name ${{ secrets.ACR_LOGIN_SERVER }}/dev-image:latest \
            --docker-registry-server-url https://${{ secrets.ACR_LOGIN_SERVER }} \
            --docker-registry-server-user ${{ secrets.ACR_USERNAME }} \
            --docker-registry-server-password ${{ secrets.ACR_PASSWORD }}
     # 5. Web App'i Yeniden Başlat
      - name: Restart Azure Web App
        run: |
          az webapp restart \
            --name contentfrontenddev \
            --resource-group Serverless
